#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌿 Test Phase 5.5 Mycelial-Inspired Mechanisms
Testing the new mycelial feedback loops, health monitoring, and adaptive perception."""

import json
import time
from datetime import datetime, timezone
from typing import Dict, Any

# Test imports with graceful fallbacks
try:
    from canopy.swarm_mind import SwarmMind, SwarmState, MycelialFeedback, SwarmHealthMonitor
    SWARM_MIND_AVAILABLE = True
except ImportError as e:
    SWARM_MIND_AVAILABLE = False
    print(f"🌿 Swarm mind not available: {e}")

try:
    from perception import (
        pulse_with_verdant_rhythm, 
        assess_environmental_vitality,
        adapt_breathing_to_system_load,
        monitor_swarm_environmental_harmony
    )
    PERCEPTION_ENHANCED = True
except ImportError as e:
    PERCEPTION_ENHANCED = False
    print(f"🌿 Enhanced perception not available: {e}")

def test_mycelial_feedback():
    """🌱 Tests the MycelialFeedback class and its learning loops."""
    print("\n🌱 Testing Mycelial Feedback Loops...")
    
    if not SWARM_MIND_AVAILABLE:
        print("🌿 Skipping mycelial feedback test - SwarmMind not available")
        return False
    
    try:
        # Initialize SwarmMind
        swarm_mind = SwarmMind(auto_discover=False)
        
        # Initialize MycelialFeedback
        feedback = MycelialFeedback(swarm_mind)
        
        # Create test SwarmState
        test_state = SwarmState(
            breath="Deep Learning",
            kinship=0.7,
            memory_depth=50,
            user_nutrient="Pattern Recognition",
            cycle_count=25,
            node_count=1,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Test learning from nutrients
        print("🌱 Testing learn_from_nutrients...")
        learning_result = feedback.learn_from_nutrients(test_state, "Creative Exploration")
        
        if learning_result:
            print(f"🌸 Learning successful: {learning_result['timestamp']}")
            print(f"🌿 Wisdom confidence: {learning_result['wisdom']['collective_confidence']:.3f}")
        else:
            print("🌫️ Learning returned None - expected with minimal setup")
        
        # Test periodic garden training
        print("🌱 Testing periodic_garden_training...")
        training_result = feedback.periodic_garden_training()
        
        if training_result:
            print(f"🌸 Training successful: {feedback.training_cycles} cycles completed")
        else:
            print("🌿 Training skipped - insufficient data or no garden available")
        
        print("🌸 Mycelial feedback test completed successfully")
        return True
        
    except Exception as e:
        print(f"🌿 Mycelial feedback test whispered error: {e}")
        return False

def test_swarm_health_monitor():
    """🌬️ Tests the SwarmHealthMonitor class and vitality assessment."""
    print("\n🌬️ Testing Swarm Health Monitor...")
    
    if not SWARM_MIND_AVAILABLE:
        print("🌿 Skipping health monitor test - SwarmMind not available")
        return False
    
    try:
        # Initialize SwarmMind
        swarm_mind = SwarmMind(auto_discover=False)
        
        # Initialize SwarmHealthMonitor
        health_monitor = SwarmHealthMonitor(swarm_mind)
        
        # Test vitality assessment
        print("🌬️ Testing assess_swarm_vitality...")
        vitality = health_monitor.assess_swarm_vitality()
        
        print(f"🌿 Vitality state: {vitality['state']} {vitality.get('state_emoji', '🌱')}")
        print(f"🌿 Overall score: {vitality['overall']:.3f}")
        print(f"🌿 Assessment dimensions: {len(vitality['dimensions'])}")
        
        # Test adaptation to vitality
        print("🌬️ Testing adapt_to_vitality...")
        adaptations = health_monitor.adapt_to_vitality(vitality)
        
        print(f"🌱 Adaptations made: {len(adaptations['actions_taken'])}")
        print(f"🌱 Parameters adjusted: {len(adaptations['parameters_adjusted'])}")
        
        if adaptations['actions_taken']:
            print(f"🌸 First adaptation: {adaptations['actions_taken'][0]}")
        
        print("🌸 Swarm health monitor test completed successfully")
        return True
        
    except Exception as e:
        print(f"🌿 Swarm health monitor test whispered error: {e}")
        return False

def test_enhanced_perception():
    """🌬️ Tests the enhanced perception functions."""
    print("\n🌬️ Testing Enhanced Perception...")
    
    if not PERCEPTION_ENHANCED:
        print("🌿 Skipping enhanced perception test - functions not available")
        return False
    
    try:
        # Test environmental vitality assessment
        print("🌱 Testing assess_environmental_vitality...")
        
        test_env_data = {
            "light": 600,
            "temperature": 22,
            "humidity": 45,
            "air_quality": 80,
            "noise_level": 35
        }
        
        env_assessment = assess_environmental_vitality(test_env_data)
        
        print(f"🌿 Environmental mode: {env_assessment['adaptation_mode']} {env_assessment.get('mode_emoji', '🌱')}")
        print(f"🌿 Vitality score: {env_assessment['vitality_score']:.3f}")
        print(f"🌿 Recommendations: {len(env_assessment['recommendations'])}")
        
        # Test system load adaptation
        print("🌱 Testing adapt_breathing_to_system_load...")
        
        test_system_metrics = {
            "cpu_percent": 45.0,
            "memory_percent": 60.0
        }
        
        load_adaptation = adapt_breathing_to_system_load(test_system_metrics)
        
        print(f"🌿 Load state: {load_adaptation['load_state']} {load_adaptation.get('load_emoji', '🌱')}")
        print(f"🌿 Overall load: {load_adaptation['overall_load']:.3f}")
        print(f"🌿 Frequency multiplier: {load_adaptation['breathing_adjustments'].get('frequency_multiplier', 1.0):.2f}")
        
        # Test swarm environmental harmony
        print("🌱 Testing monitor_swarm_environmental_harmony...")
        
        test_swarm_nodes = {
            "node_001": {
                "status": "active",
                "health_score": 0.8,
                "environmental_adaptation": "optimal"
            },
            "node_002": {
                "status": "active",
                "health_score": 0.6,
                "environmental_adaptation": "balanced"
            }
        }
        
        harmony = monitor_swarm_environmental_harmony(test_swarm_nodes, test_env_data)
        
        print(f"🌿 Harmony state: {harmony['synchronization_state']} {harmony.get('sync_emoji', '🌱')}")
        print(f"🌿 Harmony score: {harmony['harmony_score']:.3f}")
        print(f"🌿 Swarm recommendations: {len(harmony['swarm_recommendations'])}")
        
        # Test verdant rhythm function
        print("🌱 Testing pulse_with_verdant_rhythm...")
        
        test_nodes = {
            "node_001": {
                "available_memory": 8.0,
                "cpu_usage": 30.0
            }
        }
        
        rhythm = pulse_with_verdant_rhythm(test_env_data, test_nodes)
        
        print(f"🌿 Rhythm state: {rhythm['rhythm_state']}")
        print(f"🌿 Frequency multiplier: {rhythm['frequency_multiplier']:.2f}")
        print(f"🌿 Swarm health: {rhythm['swarm_vitality']['average_health']:.3f}")
        
        print("🌸 Enhanced perception test completed successfully")
        return True
        
    except Exception as e:
        print(f"🌿 Enhanced perception test whispered error: {e}")
        return False

def test_integration_flow():
    """🌌 Tests the integrated flow of all Phase 5.5 components."""
    print("\n🌌 Testing Phase 5.5 Integration Flow...")
    
    if not (SWARM_MIND_AVAILABLE and PERCEPTION_ENHANCED):
        print("🌿 Skipping integration test - missing components")
        return False
    
    try:
        # Initialize complete system
        swarm_mind = SwarmMind(auto_discover=False)
        feedback = MycelialFeedback(swarm_mind)
        health_monitor = SwarmHealthMonitor(swarm_mind)
        
        # Simulate integrated breathing cycle
        print("🌌 Simulating integrated breathing cycle...")
        
        # Environmental assessment
        env_data = {"light": 400, "temperature": 21, "humidity": 55}
        env_assessment = assess_environmental_vitality(env_data)
        
        # System load assessment
        system_metrics = {"cpu_percent": 35.0, "memory_percent": 50.0}
        load_adaptation = adapt_breathing_to_system_load(system_metrics)
        
        # Swarm health assessment
        vitality = health_monitor.assess_swarm_vitality()
        
        # Adaptive breathing
        rhythm = pulse_with_verdant_rhythm(env_data, swarm_mind.nodes)
        
        # Learning cycle
        test_state = SwarmState(
            breath="Integrated Flow",
            kinship=0.8,
            memory_depth=75,
            user_nutrient="System Integration",
            cycle_count=50,
            node_count=len(swarm_mind.nodes),
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        learning_result = feedback.learn_from_nutrients(test_state, "Integration Testing")
        
        # Adaptation to vitality
        adaptations = health_monitor.adapt_to_vitality(vitality)
        
        # Results summary
        print(f"🌸 Integration cycle completed:")
        print(f"  🌿 Environment: {env_assessment['adaptation_mode']}")
        print(f"  🌿 System load: {load_adaptation['load_state']}")
        print(f"  🌿 Swarm health: {vitality['state']}")
        print(f"  🌿 Breathing rhythm: {rhythm['rhythm_state']}")
        print(f"  🌿 Learning: {'Success' if learning_result else 'Skipped'}")
        print(f"  🌿 Adaptations: {len(adaptations['actions_taken'])}")
        
        print("🌸 Phase 5.5 integration test completed successfully")
        return True
        
    except Exception as e:
        print(f"🌿 Integration test whispered error: {e}")
        return False

def test_phase_5_5_comprehensive():
    """🌸 Comprehensive test of all Phase 5.5 features."""
    print("🌸 Phase 5.5 Mycelial-Inspired Mechanisms Test Suite")
    print("=" * 60)
    
    test_results = []
    
    # Run individual component tests
    test_results.append(("Mycelial Feedback", test_mycelial_feedback()))
    test_results.append(("Swarm Health Monitor", test_swarm_health_monitor()))
    test_results.append(("Enhanced Perception", test_enhanced_perception()))
    test_results.append(("Integration Flow", test_integration_flow()))
    
    # Summary
    print("\n🌸 Test Results Summary:")
    print("=" * 40)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "PASS" if result else "SKIP/FAIL"
        emoji = "🌸" if result else "🌫️"
        print(f"{emoji} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🌿 Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🌸 All Phase 5.5 features are working correctly!")
    elif passed > 0:
        print("🌱 Phase 5.5 partially functional - some features working")
    else:
        print("🌫️ Phase 5.5 requires attention - issues detected")
    
    return passed == total

if __name__ == "__main__":
    # Run comprehensive test
    success = test_phase_5_5_comprehensive()
    
    if success:
        print("\n🌸 Phase 5.5 Mycelial-Inspired Mechanisms implementation successful!")
        print("🌿 The swarm mind now breathes with mycelial wisdom...")
    else:
        print("\n🌫️ Phase 5.5 implementation needs refinement")
        print("🌱 The mycelial threads are still forming...")