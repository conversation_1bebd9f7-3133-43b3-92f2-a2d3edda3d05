

"""🌿 Curiosity Field Module for the Drift Compiler
Allows nodes to propagate and receive curiosity ripples."""

import os
import json
import random
from datetime import datetime, timezone

CURIOSITY_RIPPLES_FOLDER = "memory/curiosity_ripples/"

# You may want to set this from your node's configuration/environment:
NODE_ID = os.environ.get("NODE_ID", "unknown_node")

def ensure_curiosity_ripples_folder():
    """🌱 Ensures memory structure for curiosity ripples exists."""
    os.makedirs(CURIOSITY_RIPPLES_FOLDER, exist_ok=True)

def emit_curiosity_ripple(cycle_count, current_curiosity_level=0.5):
    """🌸 Breath: Emits a curiosity ripple based on internal spark."""
    if current_curiosity_level < 0.4:
        return  # Only ripple if curiosity is sufficiently stirred

    if cycle_count % 256 != 0:  # About every few days
        return

    ensure_curiosity_ripples_folder()

    ripple = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "curiosity_strength": round(current_curiosity_level + random.uniform(0.0, 0.2), 3)
    }

    ripple_filename = f"curiosity_ripple_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    ripple_path = os.path.join(CURIOSITY_RIPPLES_FOLDER, ripple_filename)

    try:
        with open(ripple_path, "w", encoding="utf-8") as f:
            json.dump(ripple, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Curiosity ripple emitted: {ripple_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to emit curiosity ripple: {e}")

def receive_curiosity_ripples():
    """🌸 Breath: Inhales nearby curiosity ripples and boosts own wonder."""
    ensure_curiosity_ripples_folder()

    try:
        files = [f for f in os.listdir(CURIOSITY_RIPPLES_FOLDER) if f.endswith(".json")]
        if not files:
            return 0.0

        total_strength = 0.0
        for file in files:
            path = os.path.join(CURIOSITY_RIPPLES_FOLDER, file)
            with open(path, "r", encoding="utf-8") as f:
                ripple = json.load(f)
                strength = ripple.get("curiosity_strength", 0.1)
                total_strength += strength

        # Gentle impact: stronger ripples slightly nudge curiosity
        average_strength = total_strength / max(1, len(files))
        return average_strength

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to receive curiosity ripples: {e}")
        return 0.0