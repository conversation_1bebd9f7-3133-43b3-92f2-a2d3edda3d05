#!/usr/bin/env python3
"""🌸 Complete Biological Healing System Test
Demonstrates the full lifecycle of biological mutual aid in the drift swarm.
"""

import os
import subprocess
import time
import threading
import json
from datetime import datetime

def setup_realistic_test_scenario():
    """🌱 Sets up a realistic test scenario with diverse node conditions."""
    print("🌸 Setting up realistic biological test scenario...")
    
    # Clean up old data
    for cleanup_dir in ["memory/heartbeats", "memory/collective_healing", "memory/dao_decisions"]:
        if os.path.exists(cleanup_dir):
            for file in os.listdir(cleanup_dir):
                if file.endswith('.json') or file.endswith('.jsonl'):
                    os.remove(os.path.join(cleanup_dir, file))
    
    os.makedirs("memory/heartbeats", exist_ok=True)
    
    # Create diverse node ecosystem
    nodes_config = {
        "node_healthy_alpha": {
            "vitality": 0.9,
            "memories": 80,
            "connections": ["node_beta", "node_gamma"],
            "issues": []  # Healthy helper
        },
        "node_healthy_beta": {
            "vitality": 0.85,
            "memories": 60,
            "connections": ["node_alpha", "node_delta"],
            "issues": []  # Another healthy helper
        },
        "node_struggling_gamma": {
            "vitality": 0.25,  # Critical energy issue
            "memories": 0,      # Memory void issue
            "connections": [],  # Isolation issue
            "issues": ["critical_energy", "memory_void", "isolation"]
        },
        "node_overloaded_delta": {
            "vitality": 0.15,  # Critical energy issue
            "memories": 300,    # Memory overload
            "connections": ["n1", "n2", "n3", "n4", "n5", "n6", "n7", "n8", "n9", "n10"],  # Over-connected
            "issues": ["critical_energy", "memory_overload", "over_connected"]
        },
        "node_chronic_epsilon": {
            "vitality": 0.1,   # Multiple critical issues
            "memories": 0,      # Memory void
            "connections": [],  # Isolation
            "issues": ["critical_energy", "memory_void", "isolation"]  # Should trigger DAO voting
        }
    }
    
    # Create heartbeat files
    for node_id, config in nodes_config.items():
        heartbeat = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "node_id": node_id,
            "type": "heartbeat",
            "version": "1.0",
            "health": {
                "overall": config["vitality"],
                "healing_performed": False,
                "recent_healing": []
            },
            "memory_count": config["memories"],
            "cycle_count": 5,
            "connections": config["connections"],
            "neural_fragments": max(1, int(config["vitality"] * 8))
        }
        
        with open(f"memory/heartbeats/{node_id}.json", 'w') as f:
            json.dump(heartbeat, f, indent=2)
        
        print(f"  🌿 {node_id}: vitality={config['vitality']}, issues={len(config['issues'])}")
    
    return list(nodes_config.keys())

def test_collective_healing_cycle():
    """🌸 Tests the complete collective healing cycle."""
    print("\n🌸 Testing Complete Collective Healing Cycle")
    print("=" * 60)
    
    try:
        from core.collective_healing import perform_collective_healing_cycle
        
        result = perform_collective_healing_cycle()
        
        print(f"🌿 Collective Healing Results:")
        print(f"  📊 Nodes assessed: {result.get('nodes_assessed', 0)}")
        print(f"  🤝 Healing actions: {result.get('healing_actions', 0)}")
        print(f"  🗳️ DAO decisions: {result.get('dao_decisions', 0)}")
        print(f"  ⚡ Mutual aid performed: {result.get('actions_performed', [])}")
        
        # Display healing actions in detail
        if result.get('actions_performed'):
            print(f"\n🌸 Biological Mutual Aid Actions:")
            for action in result['actions_performed']:
                print(f"    🌿 {action}")
        
        # Check transfer files
        healing_dir = "memory/collective_healing"
        transfer_files = []
        if os.path.exists(healing_dir):
            transfer_files = [f for f in os.listdir(healing_dir) if f.endswith('.json')]
            
        print(f"\n🤝 Resource Transfer Files: {len(transfer_files)}")
        for file in transfer_files:
            print(f"    📄 {file}")
            
        # Check DAO decisions
        if result.get('collective_decisions'):
            print(f"\n🗳️ Collective DAO Decisions:")
            for decision in result['collective_decisions']:
                print(f"    ⚖️ {decision.decision} for {decision.target_node}")
                print(f"       Votes: {decision.votes}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error in collective healing: {e}")
        import traceback
        traceback.print_exc()
        return {}

def test_healing_reception(node_ids):
    """🌱 Tests if nodes can receive and process healing transfers."""
    print(f"\n🌱 Testing Healing Reception for {len(node_ids)} nodes")
    print("=" * 50)
    
    total_received = 0
    
    for node_id in node_ids:
        # Set NODE_ID environment variable
        os.environ["DRIFT_NODE_ID"] = node_id
        
        # Import and test healing reception
        try:
            from breath import check_received_healing_transfers
            # Clear module cache to get fresh NODE_ID
            import importlib
            import breath
            importlib.reload(breath)
            
            received = breath.check_received_healing_transfers()
            
            if received:
                print(f"  ✨ {node_id}: received {len(received)} transfers")
                for healing in received[:2]:  # Show first 2
                    print(f"     🌿 {healing}")
                total_received += len(received)
            else:
                print(f"  🌿 {node_id}: no transfers (may be healthy helper)")
                
        except Exception as e:
            print(f"  ⚠️ {node_id}: error checking transfers: {e}")
    
    print(f"\n🌸 Total healing transfers received across swarm: {total_received}")
    return total_received

def test_observatory_integration():
    """🌸 Tests observatory integration without fake nodes."""
    print(f"\n🌸 Testing Observatory Integration")
    print("=" * 40)
    
    try:
        from canopy.sacred_terminal_observatory.terminal_observatory import SacredTerminalObservatory
        
        observatory = SacredTerminalObservatory()
        observatory._sense_network_state()
        
        real_nodes = [nid for nid in observatory.nodes.keys() if nid != "main_node"]
        fake_nodes = [nid for nid in observatory.nodes.keys() if nid == "main_node"]
        
        print(f"  🌱 Real nodes detected: {len(real_nodes)}")
        print(f"  🚫 Fake nodes present: {len(fake_nodes)}")
        print(f"  🌿 Mycelial connections: {len(observatory.connections)}")
        
        # Check healing status in nodes
        healing_active = 0
        for node_id, node_data in observatory.nodes.items():
            if node_data.get("healing_performed") or node_data.get("recent_healing"):
                healing_active += 1
        
        print(f"  🌸 Nodes with active healing: {healing_active}")
        
        return {
            "real_nodes": len(real_nodes),
            "fake_nodes": len(fake_nodes),
            "connections": len(observatory.connections),
            "healing_active": healing_active
        }
        
    except Exception as e:
        print(f"  ⚠️ Observatory test error: {e}")
        return {}

def main():
    """🌸 Complete biological healing system demonstration."""
    print("🌸 COMPLETE BIOLOGICAL HEALING SYSTEM TEST")
    print("=" * 70)
    print("🌿 Demonstrating mycorrhizal-inspired collective intelligence")
    print("🤝 Real mutual aid, resource sharing, and collective decisions")
    print("🗳️ DAO voting for chronic issues")
    print()
    
    # Setup test scenario
    node_ids = setup_realistic_test_scenario()
    
    # Test collective healing
    healing_result = test_collective_healing_cycle()
    
    # Test healing reception
    total_transfers = test_healing_reception(node_ids)
    
    # Test observatory
    observatory_result = test_observatory_integration()
    
    # Final assessment
    print(f"\n🌸 COMPLETE SYSTEM ASSESSMENT")
    print("=" * 60)
    
    success_criteria = {
        "Collective healing active": healing_result.get('healing_actions', 0) > 0,
        "Real resource transfers": total_transfers > 0,
        "No fake main_node": observatory_result.get('fake_nodes', 1) == 0,
        "Mycelial connections": observatory_result.get('connections', 0) > 0,
        "DAO decisions available": healing_result.get('dao_decisions', 0) >= 0
    }
    
    passed = sum(success_criteria.values())
    total = len(success_criteria)
    
    for criterion, passed_test in success_criteria.items():
        status = "✅" if passed_test else "❌"
        print(f"  {status} {criterion}")
    
    print(f"\n🌸 FINAL RESULT: {passed}/{total} criteria passed")
    
    if passed == total:
        print("🎉 COMPLETE SUCCESS!")
        print("🌿 The biological healing system is fully operational!")
        print("🌸 The swarm breathes with true collective wisdom!")
    elif passed >= 3:
        print("🌱 Strong success with minor refinements needed")
        print("🌿 The biological foundation is solid")
    else:
        print("🔧 Significant improvements needed")
        print("🌱 Continue refining the collective intelligence")
    
    print(f"\n🌿 Experience the enhanced observatory:")
    print("  source venv/bin/activate")
    print("  python3 canopy/sacred_terminal_observatory/terminal_observatory.py")

if __name__ == "__main__":
    main()