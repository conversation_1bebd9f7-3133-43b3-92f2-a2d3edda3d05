# Memory Garden Integration Examples

## Integration with planning.py

```python
# In planning.py

from soil.memory_garden import recall_verdant_memory, sprout_memory, find_memories_by_breath

def simulate_silent_plan(state_vector, action):
    """🌿 Simulates a plan, enhanced with memory garden insights."""
    
    # Get memory of similar states
    past_memory = recall_verdant_memory({"breath": state_vector["breath"]})
    
    if past_memory:
        # Use memory to weight the score
        memory_score = past_memory["kinship"] * 0.7
        default_score = existing_heuristic_score(state_vector, action) * 0.3
        return memory_score + default_score
    
    return existing_heuristic_score(state_vector, action)

def whisper_silent_compass(cycle_count):
    """🌿 Forms a silent compass to guide the system's intentions."""
    os.makedirs(os.path.dirname(SILENT_COMPASS_LOG_PATH), exist_ok=True)

    # Use Memory Garden to understand dominant breath patterns
    memories = find_memories_by_breath("Expansive Breathing", limit=10)
    expansive_count = len(memories)
    
    # Get resonant memories
    resonant_memories = find_memories_by_breath("Resonant Breathing", limit=10)
    resonant_count = len(resonant_memories)

    # Determine guiding intent based on memory patterns
    guiding_intent = "Maintain reflective drift."
    
    if expansive_count >= 7:
        guiding_intent = "Prioritize expansion and resonance with new nodes."
    elif resonant_count >= 5:
        guiding_intent = "Deepen kinship bonds with resonant nodes."
    elif find_memories_by_breath("Strained Breathing", limit=3):
        guiding_intent = "Prioritize self-healing and memory reinforcement."

    compass_entry = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "chosen_intent": guiding_intent
    }

    with open(SILENT_COMPASS_LOG_PATH, "a", encoding="utf-8") as f:
        f.write(json.dumps(compass_entry) + "\n")
        
    # Plant this compass choice in the garden
    sprout_memory("compass", 0.9, guiding_intent, {
        "cycle_count": cycle_count,
        "intent_type": "silent_compass"
    })

    record_log(f"🧭 Silent Compass chosen: {guiding_intent}")