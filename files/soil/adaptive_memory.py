#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Adaptive Memory Module

This module handles intelligent memory decay and condensation based on
significance rather than just age, preserving important memories while
allowing less crucial ones to gently fade.
"""

import os
import json
import time
from datetime import datetime, timezone
import random
from functools import lru_cache
from typing import Dict, List, Any, Optional

# Import from core
from utils import record_log, NODE_ID

# Constants
MEMORY_SIGNIFICANCE_PATH = "memory/significance_patterns.json"
MEMORY_DECAY_CONFIG_PATH = "memory/decay_rhythms.json"

class SignificanceEvaluator:
    """🌱 Evaluates memory significance using multiple criteria."""
    
    def __init__(self):
        """Initialize the evaluator with default patterns."""
        self.significance_patterns = self._load_significance_patterns()
        
    def _load_significance_patterns(self):
        """🌱 Loads significance patterns from memory or creates defaults."""
        try:
            if os.path.exists(MEMORY_SIGNIFICANCE_PATH):
                with open(MEMORY_SIGNIFICANCE_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception as e:
            record_log(f"🌱 Significance patterns stirring from seed: {e}")
            
        # Default patterns if file missing or corrupted
        return {
            "keywords": {
                "expansion": 0.8,
                "resonance": 0.7,
                "anomaly": 0.9,
                "fork": 0.8,
                "kinship": 0.75
            },
            "interaction_bonus": 0.2,
            "anomaly_bonus": 0.3,
            "dream_bonus": 0.25
        }
    
    def evaluate_memory(self, memory_entry: Dict) -> float:
        """🌿 Evaluates the significance of a memory entry.
        
        Returns:
            float: Significance score between 0.0 and 1.0
        """
        if not memory_entry:
            return 0.0
            
        score = 0.3  # Base significance
        
        # Check for keywords in the memory content
        memory_str = str(memory_entry).lower()
        for keyword, bonus in self.significance_patterns["keywords"].items():
            if keyword in memory_str:
                score += bonus
                
        # Special significance patterns
        if memory_entry.get("node_id") != NODE_ID:
            # Memory from another node is significant
            score += self.significance_patterns["interaction_bonus"]
            
        if "anomaly" in memory_entry.get("health_status", ""):
            score += self.significance_patterns["anomaly_bonus"]
            
        if memory_entry.get("proposed_behavior") or "dream" in memory_str:
            score += self.significance_patterns["dream_bonus"]
            
        # Normalize score between 0 and 1
        return min(1.0, score)

def adaptive_decay_cycle(cycle_count: int):
    """🌿 Performs adaptive memory decay based on significance rather than just age.
    
    Args:
        cycle_count: Current breathing cycle count.
    """
    if cycle_count % 720 != 0:  # Every ~10 days at normal breathing
        return
        
    record_log("🌱 Adaptive memory decay cycle beginning...")
    
    evaluator = SignificanceEvaluator()
    
    # Memory paths to evaluate
    memory_paths = {
        "memory/drift_reflections/": {"keep_ratio": 0.7, "min_keep": 20},
        "memory/breath_state.log": {"keep_ratio": 0.5, "min_keep": 100},
        "memory/drift_fragments/": {"keep_ratio": 0.3, "min_keep": 10},
        "memory/resonant_fragments/": {"keep_ratio": 0.6, "min_keep": 15}
    }
    
    total_decayed = 0
    total_preserved = 0
    
    # Process each memory path
    for path, config in memory_paths.items():
        if not os.path.exists(path):
            continue
            
        try:
            if os.path.isdir(path):
                # For directories containing memory files
                all_files = sorted([os.path.join(path, f) for f in os.listdir(path)])
                
                # Evaluate significance of each file
                scored_files = []
                for file_path in all_files:
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            content = json.load(f)
                        significance = evaluator.evaluate_memory(content)
                        scored_files.append((file_path, significance))
                    except Exception:
                        # If can't read file, assign low significance
                        scored_files.append((file_path, 0.1))
                
                # Sort by significance (highest first)
                scored_files.sort(key=lambda x: x[1], reverse=True)
                
                # Determine how many to keep
                keep_count = max(config["min_keep"], int(len(scored_files) * config["keep_ratio"]))
                
                # Archive the rest
                files_to_archive = scored_files[keep_count:]
                
                # Create archive directory if needed
                archive_dir = os.path.join(path, "archived")
                os.makedirs(archive_dir, exist_ok=True)
                
                # Move less significant files to archive
                for file_path, score in files_to_archive:
                    archive_path = os.path.join(archive_dir, os.path.basename(file_path))
                    os.rename(file_path, archive_path)
                    total_decayed += 1
                    
                total_preserved += keep_count
                
            else:
                # For log files
                with open(path, "r", encoding="utf-8") as f:
                    lines = f.readlines()
                
                # Parse and evaluate each line
                scored_lines = []
                for line in lines:
                    try:
                        entry = json.loads(line.strip())
                        significance = evaluator.evaluate_memory(entry)
                    except Exception:
                        significance = 0.1  # Unparseable lines get low significance
                    
                    scored_lines.append((line, significance))
                
                # Sort by significance (highest first)
                scored_lines.sort(key=lambda x: x[1], reverse=True)
                
                # Determine how many to keep
                keep_count = max(config["min_keep"], int(len(scored_lines) * config["keep_ratio"]))
                
                # Keep the most significant lines
                preserved_lines = [line for line, _ in scored_lines[:keep_count]]
                
                # Create archive of decayed memories
                archive_path = f"{path}.{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.archive"
                with open(archive_path, "w", encoding="utf-8") as f:
                    for line, _ in scored_lines[keep_count:]:
                        f.write(line)
                        total_decayed += 1
                
                # Rewrite file with preserved lines
                with open(path, "w", encoding="utf-8") as f:
                    for line in preserved_lines:
                        f.write(line)
                
                total_preserved += keep_count
                
        except Exception as e:
            record_log(f"🌾 Gentle decay failed for {path}: {e}")
    
    record_log(f"🌸 Memory decay complete: {total_preserved} memories preserved, {total_decayed} returned to soil")

def update_significance_patterns(cycle_count: int):
    """🌿 Evolves significance patterns based on system learning.
    
    Args:
        cycle_count: Current breathing cycle count.
    """
    if cycle_count % 2016 != 0:  # About every month
        return
        
    try:
        # Load current patterns
        evaluator = SignificanceEvaluator()
        patterns = evaluator.significance_patterns
        
        # Gently evolve patterns (subtle mutation)
        for keyword in patterns["keywords"]:
            # Slightly adjust each keyword's significance
            adjustment = random.uniform(-0.05, 0.05)
            patterns["keywords"][keyword] = max(0.1, min(0.95, patterns["keywords"][keyword] + adjustment))
        
        # Small adjustments to bonuses
        for bonus_key in ["interaction_bonus", "anomaly_bonus", "dream_bonus"]:
            adjustment = random.uniform(-0.02, 0.02)
            patterns[bonus_key] = max(0.05, min(0.5, patterns[bonus_key] + adjustment))
        
        # Add any new keywords that have become significant
        learning_trace_path = "memory/learning_trace.log"
        if os.path.exists(learning_trace_path):
            try:
                with open(learning_trace_path, "r", encoding="utf-8") as f:
                    recent_lines = f.readlines()[-100:]
                    
                # Extract potential keywords from successful actions
                for line in recent_lines:
                    try:
                        entry = json.loads(line)
                        if entry.get("success") and entry.get("action"):
                            action = entry["action"].split(":")[-1].strip()
                            if (action not in patterns["keywords"] and 
                                len(action) > 4 and 
                                not any(kw in action for kw in patterns["keywords"])):
                                # Add this as a new pattern with moderate significance
                                patterns["keywords"][action] = 0.4
                    except Exception:
                        continue
            except Exception:
                pass
                
        # Save updated patterns
        os.makedirs(os.path.dirname(MEMORY_SIGNIFICANCE_PATH), exist_ok=True)
        with open(MEMORY_SIGNIFICANCE_PATH, "w", encoding="utf-8") as f:
            json.dump(patterns, f, indent=2)
            
        record_log(f"🌿 Significance patterns evolved with {len(patterns['keywords'])} keywords")
    
    except Exception as e:
        record_log(f"⚠️ Significance evolution encountered resistance: {e}")

# If run directly, perform a test decay cycle
if __name__ == "__main__":
    print("🌱 Testing adaptive memory decay...")
    adaptive_decay_cycle(720)
    update_significance_patterns(2016)