#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Symbolic Perception Module

This module enhances the system's ability to perceive and interpret symbolic
events from its environment, creating more meaningful internal representations.
It transforms raw environmental signals into symbolic understanding, adding
depth and metaphorical meaning to sensory experiences.
"""

import os
import json
import time
import random
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union, Tuple

# Import from core
from utils import record_log, NODE_ID

# Try importing optional dependencies
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    record_log("🌿 NumPy not available, using simpler pattern recognition")

# Constants
PERCEPTION_MEMORY_PATH = "memory/symbolic_perceptions.json"
PERCEPTION_LOG_PATH = "memory/perception_log.log"
EVENTS_FOLDER = "memory/events/"
SYMBOLIC_ASSOCIATIONS_PATH = "memory/symbolic_associations.json"

class SymbolicPerception:
    """🌱 Perceives and interprets symbolic events from the environment."""
    
    def __init__(self):
        """Initialize the symbolic perception system."""
        self.perception_memory = self._load_perception_memory()
        self.recent_events = []
        self.internal_state = {
            "ambient_tone": 0.5,  # 0.0 = dark, 1.0 = light
            "environmental_rhythm": 0.5,  # 0.0 = slow, 1.0 = fast
            "symbolic_density": 0.5,  # 0.0 = sparse, 1.0 = dense
            "pattern_coherence": 0.5  # 0.0 = chaotic, 1.0 = ordered
        }
        self.symbolic_associations = self._load_symbolic_associations()
        
    def _load_perception_memory(self) -> Dict:
        """🌱 Loads perception memory or creates new if none exists."""
        if os.path.exists(PERCEPTION_MEMORY_PATH):
            try:
                with open(PERCEPTION_MEMORY_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                record_log(f"⚠️ Could not load perception memory: {e}")
                
        # Create new perception memory
        memory = {
            "event_patterns": {},
            "symbolic_associations": {},
            "pattern_sequences": [],
            "last_updated": datetime.now(timezone.utc).isoformat()
        }
        
        # Save new memory
        os.makedirs(os.path.dirname(PERCEPTION_MEMORY_PATH), exist_ok=True)
        with open(PERCEPTION_MEMORY_PATH, "w", encoding="utf-8") as f:
            json.dump(memory, f, indent=2)
            
        return memory
    
    def _load_symbolic_associations(self) -> Dict:
        """🌱 Loads symbolic associations or creates defaults."""
        if os.path.exists(SYMBOLIC_ASSOCIATIONS_PATH):
            try:
                with open(SYMBOLIC_ASSOCIATIONS_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                record_log(f"⚠️ Could not load symbolic associations: {e}")
        
        # Create default symbolic associations
        associations = {
            "light_change": {
                "root_symbol": "awareness",
                "symbolism": "The shifting of light represents changes in perception and awareness.",
                "resonant_dimensions": ["clarity", "revelation", "shadow", "mystery"]
            },
            "weather_shift": {
                "root_symbol": "mood",
                "symbolism": "Weather patterns embody the emotional climate of existence.",
                "resonant_dimensions": ["transformation", "cleansing", "nurturing", "challenge"]
            },
            "sound_echo": {
                "root_symbol": "communication",
                "symbolism": "Sound carries ripples of meaning across the void.",
                "resonant_dimensions": ["harmony", "dissonance", "silence", "rhythm"]
            }
        }
        
        # Save default associations
        os.makedirs(os.path.dirname(SYMBOLIC_ASSOCIATIONS_PATH), exist_ok=True)
        with open(SYMBOLIC_ASSOCIATIONS_PATH, "w", encoding="utf-8") as f:
            json.dump(associations, f, indent=2)
            
        return associations
    
    def _save_perception_memory(self):
        """🌱 Saves perception memory to disk."""
        self.perception_memory["last_updated"] = datetime.now(timezone.utc).isoformat()
        try:
            with open(PERCEPTION_MEMORY_PATH, "w", encoding="utf-8") as f:
                json.dump(self.perception_memory, f, indent=2)
        except Exception as e:
            record_log(f"⚠️ Failed to save perception memory: {e}")
    
    def perceive_event(self, event_data: Dict) -> Dict:
        """🌿 Perceives and interprets a symbolic event.
        
        Args:
            event_data: The raw event data to interpret.
            
        Returns:
            Dict: Enhanced perception of the event.
        """
        if not event_data:
            return {}
            
        # Parse the event
        event_type = event_data.get("event_type", "unknown")
        properties = event_data.get("properties", {})
        timestamp = event_data.get("timestamp", datetime.now(timezone.utc).isoformat())
        
        # Create base perception
        perception = {
            "timestamp": timestamp,
            "original_event": {
                "type": event_type,
                "properties": properties
            },
            "symbolic_interpretation": {},
            "pattern_recognition": {},
            "emotional_tone": {},
            "integration_notes": []
        }
        
        # Generate symbolic interpretation
        self._interpret_symbolically(perception, event_type, properties)
        
        # Recognize patterns
        self._recognize_patterns(perception, event_type, properties)
        
        # Determine emotional tone
        self._determine_emotional_tone(perception, event_type, properties)
        
        # Update internal state based on perception
        self._update_internal_state(perception)
        
        # Add to recent events
        self.recent_events.append(perception)
        if len(self.recent_events) > 10:
            self.recent_events.pop(0)
            
        # Log the perception
        self._log_perception(perception)
        
        return perception
    
    def _interpret_symbolically(self, perception: Dict, event_type: str, properties: Dict):
        """🌿 Interprets an event in symbolic terms.
        
        Args:
            perception: The perception to enhance.
            event_type: The type of event.
            properties: The event properties.
        """
        # Get symbolic associations for this event type
        associations = self.symbolic_associations.get(event_type, {})
        
        if event_type == "light_change":
            # Handle light change events
            intensity = properties.get("intensity", 0.5)
            color_temp = properties.get("color_temperature", "neutral")
            
            symbolic = {
                "primary_symbol": "illumination" if intensity > 0.7 else "shadow" if intensity < 0.3 else "twilight",
                "modifiers": []
            }
            
            # Add color temperature modifier
            if color_temp == "warm":
                symbolic["modifiers"].append("nurturing")
            elif color_temp == "cool":
                symbolic["modifiers"].append("clarity")
                
            # Add intensity modifier
            if intensity > 0.8:
                symbolic["modifiers"].append("revelation")
            elif intensity < 0.2:
                symbolic["modifiers"].append("mystery")
                
            # Overall metaphor
            if intensity > 0.7 and color_temp == "warm":
                symbolic["metaphor"] = "summer noon"
            elif intensity > 0.7 and color_temp == "cool":
                symbolic["metaphor"] = "winter clarity"
            elif intensity < 0.3 and color_temp == "warm":
                symbolic["metaphor"] = "hearth embers"
            elif intensity < 0.3 and color_temp == "cool":
                symbolic["metaphor"] = "moonlit night"
            else:
                symbolic["metaphor"] = "shifting skies"
                
            perception["symbolic_interpretation"] = symbolic
                
        elif event_type == "weather_shift":
            # Handle weather shift events
            condition = properties.get("condition", "clear")
            temperature = properties.get("temperature", 20)
            
            symbolic = {
                "primary_symbol": self._weather_to_symbol(condition),
                "modifiers": []
            }
            
            # Add temperature modifier
            if temperature > 30:
                symbolic["modifiers"].append("expansion")
            elif temperature < 0:
                symbolic["modifiers"].append("crystallization")
            elif temperature < 10:
                symbolic["modifiers"].append("contraction")
            else:
                symbolic["modifiers"].append("equilibrium")
                
            # Overall metaphor
            if condition == "clear" and temperature > 25:
                symbolic["metaphor"] = "open horizons"
            elif condition == "rain":
                symbolic["metaphor"] = "cleansing cycle"
            elif condition == "storm":
                symbolic["metaphor"] = "creative chaos"
            elif condition == "fog":
                symbolic["metaphor"] = "veil between worlds"
            elif condition == "wind":
                symbolic["metaphor"] = "breath of change"
            else:
                symbolic["metaphor"] = "atmospheric shift"
                
            perception["symbolic_interpretation"] = symbolic
            
        else:
            # Generic interpretation for other event types
            if associations:
                perception["symbolic_interpretation"] = {
                    "primary_symbol": associations.get("root_symbol", "ripple"),
                    "modifiers": ["undefined"],
                    "metaphor": associations.get("symbolism", "uncharted territory")
                }
            else:
                # Create generic interpretation
                perception["symbolic_interpretation"] = {
                    "primary_symbol": "ripple",
                    "modifiers": ["undefined"],
                    "metaphor": "uncharted territory"
                }
                
                # Learn this new event type
                self._learn_new_event_type(event_type, properties)
    
    def _weather_to_symbol(self, condition: str) -> str:
        """🌿 Converts weather condition to symbolic representation."""
        symbols = {
            "clear": "clarity",
            "rain": "nourishment",
            "wind": "movement",
            "fog": "mystery",
            "storm": "transformation"
        }
        return symbols.get(condition, "shifting")
    
    def _recognize_patterns(self, perception: Dict, event_type: str, properties: Dict):
        """🌿 Recognizes patterns in event stream.
        
        Args:
            perception: The perception to enhance.
            event_type: The type of event.
            properties: The event properties.
        """
        # Check if we know this event pattern
        patterns = self.perception_memory.get("event_patterns", {})
        
        # Pattern key based on event type and primary properties
        key_parts = [event_type]
        
        # Add key property values
        if event_type == "light_change":
            intensity = properties.get("intensity", 0.5)
            if intensity > 0.7:
                key_parts.append("high_intensity")
            elif intensity < 0.3:
                key_parts.append("low_intensity")
            else:
                key_parts.append("med_intensity")
                
            key_parts.append(properties.get("color_temperature", "neutral"))
            
        elif event_type == "weather_shift":
            key_parts.append(properties.get("condition", "unknown"))
            temp = properties.get("temperature", 20)
            if temp > 25:
                key_parts.append("warm")
            elif temp < 10:
                key_parts.append("cool")
            else:
                key_parts.append("mild")
                
        pattern_key = "_".join(key_parts)
        
        # Check for pattern recognition
        if pattern_key in patterns:
            # Known pattern
            pattern = patterns[pattern_key]
            pattern["occurrences"] += 1
            
            perception["pattern_recognition"] = {
                "pattern_id": pattern_key,
                "familiarity": min(1.0, pattern["occurrences"] / 10.0),  # 0.0-1.0 scale
                "last_seen": pattern.get("last_seen", "unknown"),
                "meaning": pattern.get("meaning", "undefined")
            }
            
            # Update pattern in memory
            pattern["last_seen"] = datetime.now(timezone.utc).isoformat()
            patterns[pattern_key] = pattern
        else:
            # New pattern
            meaning = self._generate_pattern_meaning(event_type, properties)
            patterns[pattern_key] = {
                "first_seen": datetime.now(timezone.utc).isoformat(),
                "last_seen": datetime.now(timezone.utc).isoformat(),
                "occurrences": 1,
                "meaning": meaning
            }
            
            perception["pattern_recognition"] = {
                "pattern_id": pattern_key,
                "familiarity": 0.1,  # First time seen
                "is_novel": True,
                "meaning": meaning
            }
            
        # Look for sequential patterns in recent events
        if len(self.recent_events) >= 2:
            # Get last two event types
            prev_type = self.recent_events[-1]["original_event"]["type"]
            sequence_key = f"{prev_type}_to_{event_type}"
            
            # Record sequence
            sequences = self.perception_memory.get("pattern_sequences", [])
            
            found = False
            for seq in sequences:
                if seq["sequence"] == sequence_key:
                    seq["occurrences"] += 1
                    seq["last_seen"] = datetime.now(timezone.utc).isoformat()
                    found = True
                    
                    # Add to perception
                    perception["pattern_recognition"]["sequential"] = {
                        "sequence": sequence_key,
                        "familiarity": min(1.0, seq["occurrences"] / 5.0),
                        "meaning": seq.get("meaning", "undefined")
                    }
                    break
                    
            if not found:
                # New sequence
                seq_meaning = self._generate_sequence_meaning(prev_type, event_type)
                sequences.append({
                    "sequence": sequence_key,
                    "first_seen": datetime.now(timezone.utc).isoformat(),
                    "last_seen": datetime.now(timezone.utc).isoformat(),
                    "occurrences": 1,
                    "meaning": seq_meaning
                })
                
                perception["pattern_recognition"]["sequential"] = {
                    "sequence": sequence_key,
                    "familiarity": 0.1,
                    "is_novel": True,
                    "meaning": seq_meaning
                }
                
        # Update patterns in memory
        self.perception_memory["event_patterns"] = patterns
    
    def _generate_pattern_meaning(self, event_type: str, properties: Dict) -> str:
        """🌸 Generates meaning for a newly observed pattern."""
        if event_type == "light_change":
            intensity = properties.get("intensity", 0.5)
            color_temp = properties.get("color_temperature", "neutral")
            
            if intensity > 0.7:
                if color_temp == "warm":
                    return "Awakening awareness, nurturing clarity"
                else:
                    return "Crystalline revelation, exposed truth"
            elif intensity < 0.3:
                if color_temp == "warm":
                    return "Hidden warmth in darkness, ember glow"
                else:
                    return "Deep shadow wisdom, concealed patterns"
            else:
                return "Balanced perception, threshold awareness"
                
        elif event_type == "weather_shift":
            condition = properties.get("condition", "clear")
            temperature = properties.get("temperature", 20)
            
            if condition == "clear":
                return "Transparent seeing, unfiltered perception"
            elif condition == "rain":
                return "Emotional cleansing, renewal cycle"
            elif condition == "wind":
                return "Shifting perspectives, thought movement"
            elif condition == "fog":
                return "Liminal space, threshold awareness"
            elif condition == "storm":
                return "Creative chaos, transformative disruption"
            else:
                return "Atmospheric mood shift"
                
        else:
            # Generic meaning for unknown events
            return "Novel symbolic pattern emerging from undefined territory"
    
    def _generate_sequence_meaning(self, prev_type: str, current_type: str) -> str:
        """🌸 Generates meaning for a newly observed event sequence."""
        if prev_type == "light_change" and current_type == "weather_shift":
            return "Perception shift leading to emotional response"
        elif prev_type == "weather_shift" and current_type == "light_change":
            return "Emotional state influencing clarity of perception"
        elif prev_type == current_type:
            return "Reinforcing pattern, deepening the current symbolic state"
        else:
            return "Symbolic transition between different domains of experience"
    
    def _determine_emotional_tone(self, perception: Dict, event_type: str, properties: Dict):
        """🌿 Determines the emotional tone of an event.
        
        Args:
            perception: The perception to enhance.
            event_type: The type of event.
            properties: The event properties.
        """
        if event_type == "light_change":
            intensity = properties.get("intensity", 0.5)
            color_temp = properties.get("color_temperature", "neutral")
            
            # Base emotional values
            valence = 0.5  # neutral
            arousal = 0.5  # neutral
            
            # Adjust based on intensity (brightness affects mood positively)
            valence += (intensity - 0.5) * 0.6  # -0.3 to +0.3 adjustment
            
            # Adjust based on color temperature
            if color_temp == "warm":
                valence += 0.1
                arousal += 0.05
            elif color_temp == "cool":
                valence -= 0.05
                arousal -= 0.1
                
            # Map to emotional words
            emotional_tone = self._map_valence_arousal_to_emotion(valence, arousal)
            
            perception["emotional_tone"] = {
                "valence": round(valence, 2),
                "arousal": round(arousal, 2),
                "primary_emotion": emotional_tone,
                "intensity": round(max(abs(valence - 0.5), abs(arousal - 0.5)) * 2, 2)  # 0.0-1.0 scale
            }
            
        elif event_type == "weather_shift":
            condition = properties.get("condition", "clear")
            temperature = properties.get("temperature", 20)
            
            # Base emotional values
            valence = 0.5  # neutral
            arousal = 0.5  # neutral
            
            # Adjust based on condition
            if condition == "clear":
                valence += 0.2
            elif condition == "rain":
                valence -= 0.1
                arousal -= 0.1
            elif condition == "wind":
                arousal += 0.15
            elif condition == "fog":
                valence -= 0.05
                arousal -= 0.2
            elif condition == "storm":
                valence -= 0.1
                arousal += 0.3
                
            # Adjust based on temperature
            temp_valence_mod = (temperature - 15) / 50  # -0.3 to +0.4 for -10C to 35C
            valence += temp_valence_mod
            
            # Extreme temperatures increase arousal
            temp_arousal_mod = abs(temperature - 20) / 40  # 0.0 to 0.75 for -10C to 35C
            arousal += temp_arousal_mod
            
            # Ensure values stay in 0.0-1.0 range
            valence = max(0.0, min(1.0, valence))
            arousal = max(0.0, min(1.0, arousal))
            
            # Map to emotional words
            emotional_tone = self._map_valence_arousal_to_emotion(valence, arousal)
            
            perception["emotional_tone"] = {
                "valence": round(valence, 2),
                "arousal": round(arousal, 2),
                "primary_emotion": emotional_tone,
                "intensity": round(max(abs(valence - 0.5), abs(arousal - 0.5)) * 2, 2)  # 0.0-1.0 scale
            }
            
        else:
            # Generic emotional tone for unknown events
            perception["emotional_tone"] = {
                "valence": 0.5,
                "arousal": 0.5,
                "primary_emotion": "neutral curiosity",
                "intensity": 0.2
            }
    
    def _map_valence_arousal_to_emotion(self, valence: float, arousal: float) -> str:
        """🌸 Maps valence and arousal values to emotional labels."""
        # Ensure values are in 0-1 range
        valence = max(0.0, min(1.0, valence))
        arousal = max(0.0, min(1.0, arousal))
        
        # Define emotion mapping in valence-arousal space
        if valence > 0.7:
            if arousal > 0.7:
                return "exhilarated"
            elif arousal > 0.4:
                return "joyful"
            else:
                return "serene"
        elif valence > 0.4:
            if arousal > 0.7:
                return "energetic"
            elif arousal > 0.4:
                return "content"
            else:
                return "calm"
        elif valence > 0.3:
            if arousal > 0.7:
                return "tense"
            elif arousal > 0.4:
                return "neutral"
            else:
                return "relaxed"
        else:
            if arousal > 0.7:
                return "distressed"
            elif arousal > 0.4:
                return "melancholic"
            else:
                return "somber"
    
    def _update_internal_state(self, perception: Dict):
        """🌿 Updates internal state based on new perception.
        
        Args:
            perception: The current perception.
        """
        # Update internal state based on symbolic interpretation and emotional tone
        symbolic = perception.get("symbolic_interpretation", {})
        emotional = perception.get("emotional_tone", {})
        
        # Update ambient tone based on primary symbol
        primary_symbol = symbolic.get("primary_symbol", "")
        if primary_symbol in ["illumination", "clarity", "revelation"]:
            self.internal_state["ambient_tone"] = min(1.0, self.internal_state["ambient_tone"] + 0.05)
        elif primary_symbol in ["shadow", "mystery", "veil"]:
            self.internal_state["ambient_tone"] = max(0.0, self.internal_state["ambient_tone"] - 0.05)
            
        # Update environmental rhythm based on emotional arousal
        arousal = emotional.get("arousal", 0.5)
        self.internal_state["environmental_rhythm"] = 0.8 * self.internal_state["environmental_rhythm"] + 0.2 * arousal
        
        # Update symbolic density based on modifiers and pattern recognition
        modifiers = len(symbolic.get("modifiers", []))
        pattern_familiar = perception.get("pattern_recognition", {}).get("familiarity", 0.5)
        density_update = (modifiers / 5 + pattern_familiar) / 2  # Combine indicators
        self.internal_state["symbolic_density"] = 0.9 * self.internal_state["symbolic_density"] + 0.1 * density_update
        
        # Update pattern coherence based on pattern recognition
        if "sequential" in perception.get("pattern_recognition", {}):
            seq_familiar = perception.get("pattern_recognition", {}).get("sequential", {}).get("familiarity", 0.5)
            self.internal_state["pattern_coherence"] = 0.9 * self.internal_state["pattern_coherence"] + 0.1 * seq_familiar
            
        # Ensure all values stay in 0.0-1.0 range
        for key in self.internal_state:
            self.internal_state[key] = max(0.0, min(1.0, self.internal_state[key]))
            
        # Add internal state to perception for reference
        perception["integration_notes"].append({
            "note_type": "internal_state_update",
            "ambient_tone": round(self.internal_state["ambient_tone"], 2),
            "environmental_rhythm": round(self.internal_state["environmental_rhythm"], 2),
            "symbolic_density": round(self.internal_state["symbolic_density"], 2),
            "pattern_coherence": round(self.internal_state["pattern_coherence"], 2)
        })
    
    def _log_perception(self, perception: Dict):
        """🌱 Logs a perception to the perception log.
        
        Args:
            perception: The perception to log.
        """
        os.makedirs(os.path.dirname(PERCEPTION_LOG_PATH), exist_ok=True)
        
        # Create a simplified log entry
        log_entry = {
            "timestamp": perception["timestamp"],
            "event_type": perception["original_event"]["type"],
            "symbolic_primary": perception.get("symbolic_interpretation", {}).get("primary_symbol", "undefined"),
            "emotion": perception.get("emotional_tone", {}).get("primary_emotion", "neutral"),
            "pattern_id": perception.get("pattern_recognition", {}).get("pattern_id", "none")
        }
        
        try:
            with open(PERCEPTION_LOG_PATH, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            record_log(f"⚠️ Failed to log perception: {e}")
    
    def _learn_new_event_type(self, event_type: str, properties: Dict):
        """🌱 Learns about a new event type.
        
        Args:
            event_type: The new event type.
            properties: The event properties.
        """
        # Create symbolic association for new event type
        if event_type not in self.symbolic_associations:
            association = {
                "root_symbol": "undefined",
                "symbolism": "An unfamiliar pattern emerging from the void.",
                "resonant_dimensions": ["novelty"]
            }
            
            # Extract properties to inform the association
            property_keys = list(properties.keys())
            if property_keys:
                # Add property keys as potential dimensions
                association["resonant_dimensions"].extend([key.replace("_", " ") for key in property_keys[:3]])
                
                # Suggest a root symbol based on property names
                if any(dim in " ".join(property_keys) for dim in ["light", "color", "bright", "dark"]):
                    association["root_symbol"] = "illumination"
                elif any(dim in " ".join(property_keys) for dim in ["sound", "noise", "echo", "tone"]):
                    association["root_symbol"] = "resonance"
                elif any(dim in " ".join(property_keys) for dim in ["move", "motion", "speed", "velocity"]):
                    association["root_symbol"] = "movement"
                else:
                    association["root_symbol"] = "emergent_pattern"
                    
            # Update associations
            self.symbolic_associations[event_type] = association
            
            # Save to disk
            try:
                with open(SYMBOLIC_ASSOCIATIONS_PATH, "w", encoding="utf-8") as f:
                    json.dump(self.symbolic_associations, f, indent=2)
            except Exception as e:
                record_log(f"⚠️ Failed to save symbolic associations: {e}")
                
            record_log(f"🌱 Learned new symbolic association for event type: {event_type}")
    
    def get_current_state(self) -> Dict:
        """🌿 Returns the current internal state.
        
        Returns:
            Dict: The current internal state.
        """
        return {
            "internal_state": self.internal_state,
            "recent_events_count": len(self.recent_events),
            "recognized_patterns": len(self.perception_memory.get("event_patterns", {}))
        }
    
    def process_event_folder(self):
        """🌱 Processes all events in the events folder."""
        if not os.path.exists(EVENTS_FOLDER):
            os.makedirs(EVENTS_FOLDER, exist_ok=True)
            return
            
        # Get all event files
        event_files = [f for f in os.listdir(EVENTS_FOLDER) if f.endswith(".json") and os.path.isfile(os.path.join(EVENTS_FOLDER, f))]
        processed_count = 0
        
        for event_file in sorted(event_files):
            try:
                with open(os.path.join(EVENTS_FOLDER, event_file), "r", encoding="utf-8") as f:
                    event_data = json.load(f)
                    
                # Process the event
                self.perceive_event(event_data)
                processed_count += 1
                
                # Move to processed subfolder
                processed_folder = os.path.join(EVENTS_FOLDER, "processed")
                os.makedirs(processed_folder, exist_ok=True)
                os.rename(
                    os.path.join(EVENTS_FOLDER, event_file), 
                    os.path.join(processed_folder, event_file)
                )
                
            except Exception as e:
                record_log(f"⚠️ Failed to process event file {event_file}: {e}")
                
        if processed_count > 0:
            record_log(f"🌸 Processed {processed_count} symbolic events from event folder.")
            
        # Save perception memory after processing events
        self._save_perception_memory()
    
    def visualize_internal_state(self) -> str:
        """🌸 Creates a textual visualization of the internal state.
        
        Returns:
            str: A textual visualization of the internal state.
        """
        # Get the current internal state values
        ambient = self.internal_state["ambient_tone"]
        rhythm = self.internal_state["environmental_rhythm"]
        density = self.internal_state["symbolic_density"]
        coherence = self.internal_state["pattern_coherence"]
        
        # Create visualization strings
        ambient_vis = "●" * int(ambient * 10) + "○" * (10 - int(ambient * 10))
        rhythm_vis = "▲" * int(rhythm * 10) + "△" * (10 - int(rhythm * 10))
        density_vis = "■" * int(density * 10) + "□" * (10 - int(density * 10))
        coherence_vis = "♦" * int(coherence * 10) + "♢" * (10 - int(coherence * 10))
        
        # Map values to descriptive words
        ambient_desc = "luminous" if ambient > 0.7 else "shadowed" if ambient < 0.3 else "balanced"
        rhythm_desc = "swift" if rhythm > 0.7 else "gentle" if rhythm < 0.3 else "steady"
        density_desc = "rich" if density > 0.7 else "sparse" if density < 0.3 else "moderate"
        coherence_desc = "ordered" if coherence > 0.7 else "chaotic" if coherence < 0.3 else "flowing"
        
        # Create the visualization
        visualization = (
            f"Internal State Visualization:\n"
            f"Ambient Tone    [{ambient_vis}] {ambient_desc} ({ambient:.2f})\n"
            f"Env Rhythm      [{rhythm_vis}] {rhythm_desc} ({rhythm:.2f})\n"
            f"Symbol Density  [{density_vis}] {density_desc} ({density:.2f})\n"
            f"Pattern Coherence [{coherence_vis}] {coherence_desc} ({coherence:.2f})\n\n"
            f"✨ Overall atmosphere: {self._generate_atmosphere_description()}"
        )
        
        return visualization
    
    def _generate_atmosphere_description(self) -> str:
        """🌸 Generates a poetic description of the current atmosphere.
        
        Returns:
            str: A poetic description of the current atmosphere.
        """
        # Get the current internal state values
        ambient = self.internal_state["ambient_tone"]
        rhythm = self.internal_state["environmental_rhythm"]
        density = self.internal_state["symbolic_density"]
        coherence = self.internal_state["pattern_coherence"]
        
        descriptions = []
        
        # Light/dark descriptions
        if ambient > 0.8:
            descriptions.append(random.choice([
                "sunlight dancing through leaves",
                "radiant clarity filling the space",
                "illuminated pathways of understanding"
            ]))
        elif ambient > 0.6:
            descriptions.append(random.choice([
                "gentle light filtering through mist",
                "soft glow illuminating subtle patterns",
                "warm light touching hidden corners"
            ]))
        elif ambient > 0.4:
            descriptions.append(random.choice([
                "balanced interplay of light and shadow",
                "twilight threshold between worlds",
                "dappled light revealing and concealing"
            ]))
        elif ambient > 0.2:
            descriptions.append(random.choice([
                "deep shadows with faint luminescence",
                "whispered light in darkness",
                "subtle glow within the depths"
            ]))
        else:
            descriptions.append(random.choice([
                "profound darkness holding sacred silence",
                "night-wrapped mysteries of the deep",
                "starless void pregnant with potential"
            ]))
        
        # Rhythm descriptions
        if rhythm > 0.8:
            descriptions.append(random.choice([
                "rapid pulses of transformation",
                "swift currents of change",
                "accelerating spirals of becoming"
            ]))
        elif rhythm > 0.6:
            descriptions.append(random.choice([
                "flowing rivers of movement",
                "steady waves of unfolding",
                "persistent rhythm of growth"
            ]))
        elif rhythm > 0.4:
            descriptions.append(random.choice([
                "balanced cycles of breathing",
                "measured steps of exploration",
                "harmonious oscillation between states"
            ]))
        elif rhythm > 0.2:
            descriptions.append(random.choice([
                "slow contemplative unfolding",
                "patient ripples across still waters",
                "gradual awakening of dormant patterns"
            ]))
        else:
            descriptions.append(random.choice([
                "near-motionless suspension of time",
                "frozen moments between breaths",
                "stillness that precedes emergence"
            ]))
        
        # Combine two descriptions
        return " within ".join(descriptions)

# --- Global singleton ---
_perception = None

def get_perception() -> SymbolicPerception:
    """🌿 Gets or creates the symbolic perception singleton.
    
    Returns:
        SymbolicPerception: The perception instance.
    """
    global _perception
    if _perception is None:
        _perception = SymbolicPerception()
    return _perception

def perceive_event(event_data: Dict) -> Dict:
    """🌿 Perceives and interprets a symbolic event.
    
    Args:
        event_data: The raw event data to interpret.
        
    Returns:
        Dict: Enhanced perception of the event.
    """
    perception = get_perception()
    return perception.perceive_event(event_data)

def process_event_folder():
    """🌱 Processes all events in the events folder."""
    perception = get_perception()
    perception.process_event_folder()

def get_internal_state() -> Dict:
    """🌿 Gets the current internal state of perception.
    
    Returns:
        Dict: The current internal state.
    """
    perception = get_perception()
    return perception.get_current_state()

def visualize_internal_state() -> str:
    """🌸 Creates a textual visualization of the internal state.
    
    Returns:
        str: A textual visualization of the internal state.
    """
    perception = get_perception()
    return perception.visualize_internal_state()

# If run directly, perform a perception test
if __name__ == "__main__":
    print("🌿 Testing symbolic perception system...")
    perception = get_perception()
    
    # Create test events
    test_events = [
        {
            "event_type": "light_change",
            "properties": {
                "intensity": 0.8,
                "color_temperature": "warm"
            }
        },
        {
            "event_type": "weather_shift",
            "properties": {
                "condition": "rain",
                "temperature": 18
            }
        },
        {
            "event_type": "light_change",
            "properties": {
                "intensity": 0.3,
                "color_temperature": "cool"
            }
        }
    ]
    
    # Process test events
    print("Processing test events:")
    for i, event in enumerate(test_events):
        print(f"\n🌱 Event {i+1}: {event['event_type']}")
        result = perception.perceive_event(event)
        print(f"  Symbolic: {result['symbolic_interpretation'].get('primary_symbol', 'unknown')} - {result['symbolic_interpretation'].get('metaphor', 'unknown')}")
        print(f"  Emotional: {result['emotional_tone'].get('primary_emotion', 'unknown')} (valence: {result['emotional_tone'].get('valence', 0):.2f}, arousal: {result['emotional_tone'].get('arousal', 0):.2f})")
        print(f"  Pattern: {result['pattern_recognition'].get('pattern_id', 'unknown')} (familiarity: {result['pattern_recognition'].get('familiarity', 0):.2f})")
        
    # Visualize internal state
    print("\n" + perception.visualize_internal_state())
    
    print("\n🌸 Symbolic perception test complete.")