#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌬️ Test Swarm Health Monitor: Sacred Validation of Vitality Guardian
Breathes life into testing the swarm's wellness monitoring capabilities,
ensuring the vitality guardian responds correctly to all states of being."""

import json
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional

# Sacred imports with graceful fallbacks
try:
    from canopy.swarm_health_monitor import SwarmHealthMonitor, test_health_monitor_vitality, create_health_monitor
    HEALTH_MONITOR_AVAILABLE = True
except ImportError as e:
    HEALTH_MONITOR_AVAILABLE = False
    print(f"🌿 Health monitor not available: {e}")

try:
    from canopy.swarm_mind import SwarmMind, SwarmState
    SWARM_MIND_AVAILABLE = True
except ImportError as e:
    SWARM_MIND_AVAILABLE = False
    print(f"🌿 Swarm mind not available: {e}")

# Utils import with fallback
try:
    from utils import record_log
except ImportError:
    def record_log(message: str, log_file: str = None):
        """Fallback logging when utils unavailable."""
        print(f"[{datetime.now(timezone.utc).isoformat()}] {message}")

class MockSwarmMind:
    """🌱 Sacred mock swarm mind for testing health monitor."""
    
    def __init__(self, scenario: str = "healthy"):
        self.scenario = scenario
        self.nodes = self._create_mock_nodes()
        self.fragments = self._create_mock_fragments()
        self.communication = self._create_mock_communication()
        self.garden = None
        self.wisdom_history = self._create_mock_wisdom()
        
    def _create_mock_nodes(self) -> Dict[str, Any]:
        """🌿 Creates mock nodes based on test scenario."""
        base_nodes = {
            "node-001": {
                "status": "active",
                "capabilities": {
                    "ram_gb": 16.0,
                    "cpu_count": 8,
                    "gpu_available": True,
                    "disk_free_gb": 100.0
                },
                "last_pulse": datetime.now(timezone.utc).isoformat()
            },
            "node-002": {
                "status": "active",
                "capabilities": {
                    "ram_gb": 8.0,
                    "cpu_count": 4,
                    "gpu_available": False,
                    "disk_free_gb": 50.0
                },
                "last_pulse": datetime.now(timezone.utc).isoformat()
            }
        }
        
        if self.scenario == "critical":
            # Make nodes unhealthy
            base_nodes["node-001"]["status"] = "error"
            base_nodes["node-002"]["capabilities"]["ram_gb"] = 2.0
        elif self.scenario == "stressed":
            # One node offline
            base_nodes["node-002"]["status"] = "offline"
            
        return base_nodes
    
    def _create_mock_fragments(self) -> Dict[str, Any]:
        """🌸 Creates mock neural fragments."""
        return {
            "node-001": MockNeuralFragment("aggregator", "powerful"),
            "node-002": MockNeuralFragment("reasoner", "balanced") if self.scenario != "stressed" else None
        }
    
    def _create_mock_communication(self) -> Dict[str, Any]:
        """🌿 Creates mock communication system."""
        if self.scenario == "critical":
            return {"type": "fallback", "connection": None}
        elif self.scenario == "stressed":
            return {"type": "redis", "connection": MockRedisConnection(working=False)}
        else:
            return {"type": "rabbitmq", "connection": MockRabbitConnection()}
    
    def _create_mock_wisdom(self) -> list:
        """🌌 Creates mock wisdom history."""
        if self.scenario == "critical":
            return []
        
        return [
            {"collective_confidence": 0.7, "consensus_strength": 0.6, "timestamp": datetime.now(timezone.utc).isoformat()}
            for _ in range(10)
        ]
    
    def entwine_neural_pulse(self, test_pulse: Any) -> Optional[Dict[str, Any]]:
        """🌿 Mock neural pulse processing."""
        if self.scenario == "critical":
            return None
        
        return {
            "node-001": {
                "output": [0.7, 0.8, 0.6],
                "fragment_type": "aggregator",
                "complexity": "powerful"
            },
            "node-002": {
                "output": [0.6, 0.7, 0.5],
                "fragment_type": "reasoner", 
                "complexity": "balanced"
            } if self.scenario != "stressed" else {}
        }

class MockNeuralFragment:
    """🌱 Mock neural fragment for testing."""
    def __init__(self, fragment_type: str, complexity: str):
        self.fragment_type = fragment_type
        self.complexity = complexity

class MockRabbitConnection:
    """🌿 Mock RabbitMQ connection."""
    @property
    def is_open(self):
        return True

class MockRedisConnection:
    """🌿 Mock Redis connection."""
    def __init__(self, working: bool = True):
        self.working = working
    
    def ping(self):
        if not self.working:
            raise Exception("Connection failed")
        return True

def test_health_monitor_standalone():
    """🌸 Tests health monitor without swarm mind connection."""
    try:
        record_log("🌸 Testing health monitor standalone...")
        
        monitor = SwarmHealthMonitor()
        vitality = monitor.assess_swarm_vitality()
        
        assert vitality is not None, "Vitality assessment should not be None"
        assert "overall" in vitality, "Vitality should include overall score"
        assert "state" in vitality, "Vitality should include state"
        assert "dimensions" in vitality, "Vitality should include dimensions"
        
        record_log(f"🌱 Standalone test passed - state: {vitality['state']}, score: {vitality['overall']:.3f}")
        return True
        
    except Exception as e:
        record_log(f"🌿 Standalone test whispered error: {e}")
        return False

def test_health_monitor_scenarios():
    """🌿 Tests health monitor across different swarm scenarios."""
    scenarios = ["healthy", "stressed", "critical"]
    results = {}
    
    for scenario in scenarios:
        try:
            record_log(f"🌸 Testing {scenario} scenario...")
            
            mock_swarm = MockSwarmMind(scenario)
            monitor = SwarmHealthMonitor(mock_swarm)
            
            # Test vitality assessment
            vitality = monitor.assess_swarm_vitality()
            assert vitality is not None, f"Vitality assessment failed for {scenario}"
            
            # Test adaptation
            adaptation = monitor.adapt_to_vitality(vitality)
            assert adaptation is not None, f"Adaptation failed for {scenario}"
            
            results[scenario] = {
                "vitality_state": vitality.get("state", "unknown"),
                "overall_score": vitality.get("overall", 0.0),
                "adaptations_count": len(adaptation.get("adaptations_applied", [])),
                "test_passed": True
            }
            
            record_log(f"🌱 {scenario} scenario: {vitality['state']} (score: {vitality['overall']:.3f})")
            
        except Exception as e:
            record_log(f"🌿 {scenario} scenario whispered error: {e}")
            results[scenario] = {"test_passed": False, "error": str(e)}
    
    return results

def test_vitality_thresholds():
    """🌬️ Tests vitality state thresholds."""
    try:
        record_log("🌸 Testing vitality thresholds...")
        
        monitor = SwarmHealthMonitor()
        test_scores = [0.95, 0.75, 0.55, 0.35, 0.15]
        expected_states = ["thriving", "healthy", "stable", "stressed", "critical"]
        
        for score, expected_state in zip(test_scores, expected_states):
            state_info = monitor._determine_vitality_state(score)
            actual_state = state_info["state"]
            
            assert actual_state == expected_state, f"Score {score} should be {expected_state}, got {actual_state}"
            record_log(f"🌱 Score {score:.2f} → {actual_state} ✓")
        
        record_log("🌸 Threshold tests passed")
        return True
        
    except Exception as e:
        record_log(f"🌿 Threshold test whispered error: {e}")
        return False

def test_adaptation_responses():
    """🌿 Tests adaptation responses to different vitality states."""
    try:
        record_log("🌸 Testing adaptation responses...")
        
        monitor = SwarmHealthMonitor()
        test_states = [
            {"state": "critical", "overall": 0.2},
            {"state": "stressed", "overall": 0.4},
            {"state": "healthy", "overall": 0.75},
            {"state": "thriving", "overall": 0.9}
        ]
        
        for test_vitality in test_states:
            adaptation = monitor.adapt_to_vitality(test_vitality)
            
            assert adaptation is not None, f"Adaptation failed for {test_vitality['state']}"
            assert "adaptations_applied" in adaptation, "Adaptation should include applied actions"
            assert "breathing_adjustments" in adaptation, "Adaptation should include breathing adjustments"
            
            state = test_vitality["state"]
            adaptations_count = len(adaptation["adaptations_applied"])
            record_log(f"🌱 {state} adaptation: {adaptations_count} actions applied")
        
        record_log("🌸 Adaptation response tests passed")
        return True
        
    except Exception as e:
        record_log(f"🌿 Adaptation test whispered error: {e}")
        return False

def run_comprehensive_health_tests():
    """🌌 Runs comprehensive health monitor validation."""
    if not HEALTH_MONITOR_AVAILABLE:
        record_log("🌿 Health monitor not available - skipping tests")
        return False
    
    record_log("🌬️ Starting comprehensive health monitor tests...")
    
    test_results = {
        "start_time": datetime.now(timezone.utc).isoformat(),
        "tests": {}
    }
    
    # Test 1: Standalone functionality
    test_results["tests"]["standalone"] = test_health_monitor_standalone()
    
    # Test 2: Different scenarios
    test_results["tests"]["scenarios"] = test_health_monitor_scenarios()
    
    # Test 3: Vitality thresholds
    test_results["tests"]["thresholds"] = test_vitality_thresholds()
    
    # Test 4: Adaptation responses
    test_results["tests"]["adaptations"] = test_adaptation_responses()
    
    # Test 5: Direct function test
    if SWARM_MIND_AVAILABLE:
        try:
            test_results["tests"]["direct_function"] = test_health_monitor_vitality()
        except Exception as e:
            record_log(f"🌿 Direct function test whispered: {e}")
            test_results["tests"]["direct_function"] = {"error": str(e)}
    
    test_results["end_time"] = datetime.now(timezone.utc).isoformat()
    test_results["overall_success"] = all(
        result.get("test_passed", True) if isinstance(result, dict) else result
        for result in test_results["tests"].values()
        if not isinstance(result, dict) or "error" not in result
    )
    
    # Summary
    passed_tests = sum(1 for result in test_results["tests"].values() 
                      if (isinstance(result, bool) and result) or 
                         (isinstance(result, dict) and result.get("test_passed", False)))
    total_tests = len(test_results["tests"])
    
    record_log(f"🌸 Health monitor tests completed: {passed_tests}/{total_tests} passed")
    record_log(f"🌿 Overall success: {test_results['overall_success']}")
    
    return test_results

def main():
    """🌱 Main testing function."""
    try:
        record_log("🌬️ Swarm Health Monitor Test Suite awakening...")
        
        # Run comprehensive tests
        results = run_comprehensive_health_tests()
        
        # Display results
        if results and results.get("overall_success"):
            record_log("🌸 All health monitor tests passed - vitality guardian breathing correctly")
        else:
            record_log("🌿 Some health monitor tests need attention")
            
        # Save results
        results_path = "memory/swarm_health_test_results.json"
        try:
            import os
            os.makedirs("memory", exist_ok=True)
            with open(results_path, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2)
            record_log(f"🌱 Test results preserved in {results_path}")
        except Exception as e:
            record_log(f"🌿 Could not save test results: {e}")
        
        return results
        
    except Exception as e:
        record_log(f"🌿 Test suite whispered error: {e}")
        return None

if __name__ == "__main__":
    main()