#!/usr/bin/env python3
"""🌸 Direct Test of Collective Healing System"""

import os
import json
import time
from datetime import datetime, timezone

def create_test_heartbeats():
    """🌱 Creates test heartbeats with specific conditions for healing."""
    os.makedirs("memory/heartbeats", exist_ok=True)
    
    # Create helper node (healthy, can provide aid)
    helper_heartbeat = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": "node_helper",
        "type": "heartbeat",
        "version": "1.0",
        "health": {
            "overall": 0.85,  # High vitality
            "healing_performed": False,
            "recent_healing": []
        },
        "memory_count": 50,  # Good memory
        "cycle_count": 10,
        "connections": ["node_struggling", "node_overloaded"],  # Well connected
        "neural_fragments": 5
    }
    
    # Create struggling node (needs memory help)
    struggling_heartbeat = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": "node_struggling",
        "type": "heartbeat", 
        "version": "1.0",
        "health": {
            "overall": 0.3,  # Low vitality = fatigue issue
            "healing_performed": False,
            "recent_healing": []
        },
        "memory_count": 0,  # No memory = memory_void issue
        "cycle_count": 5,
        "connections": [],  # No connections = isolation issue
        "neural_fragments": 2
    }
    
    # Create overloaded node (needs load balancing)
    overloaded_heartbeat = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": "node_overloaded",
        "type": "heartbeat",
        "version": "1.0", 
        "health": {
            "overall": 0.25,  # Very low vitality = critical_energy issue
            "healing_performed": False,
            "recent_healing": []
        },
        "memory_count": 250,  # Too much memory = memory_overload issue  
        "cycle_count": 3,
        "connections": ["n1", "n2", "n3", "n4", "n5", "n6", "n7", "n8", "n9"],  # Over-connected
        "neural_fragments": 1
    }
    
    # Write heartbeat files
    heartbeats = [
        ("node_helper.json", helper_heartbeat),
        ("node_struggling.json", struggling_heartbeat), 
        ("node_overloaded.json", overloaded_heartbeat)
    ]
    
    for filename, heartbeat in heartbeats:
        with open(f"memory/heartbeats/{filename}", 'w') as f:
            json.dump(heartbeat, f, indent=2)
    
    print("🌱 Created test heartbeats:")
    print(f"  🌿 node_helper: vitality=0.85, memories=50, connections=2")
    print(f"  🌿 node_struggling: vitality=0.3, memories=0, connections=0") 
    print(f"  🌿 node_overloaded: vitality=0.25, memories=250, connections=9")

def test_collective_healing():
    """🌸 Tests the collective healing system directly."""
    print("\n🌸 Testing Collective Healing System")
    print("=" * 50)
    
    try:
        from core.collective_healing import perform_collective_healing_cycle
        
        # Test the healing cycle
        result = perform_collective_healing_cycle()
        
        print(f"🌿 Healing cycle completed:")
        print(f"  📊 Nodes assessed: {result.get('nodes_assessed', 0)}")
        print(f"  🤝 Healing actions: {result.get('healing_actions', 0)}")
        print(f"  🗳️ DAO decisions: {result.get('dao_decisions', 0)}")
        print(f"  ⚡ Actions performed: {result.get('actions_performed', [])}")
        
        # Check if transfer files were created
        healing_dir = "memory/collective_healing"
        if os.path.exists(healing_dir):
            files = [f for f in os.listdir(healing_dir) if f.endswith('.json')]
            print(f"  📁 Transfer files created: {len(files)}")
            for file in files:
                print(f"    📄 {file}")
        else:
            print(f"  ⚠️ No collective healing directory found")
            
        # Check healing action log
        log_file = "memory/collective_healing/healing_actions.jsonl"
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                actions = f.readlines()
            print(f"  📝 Actions logged: {len(actions)}")
            for action in actions[-3:]:  # Show last 3 actions
                action_data = json.loads(action)
                print(f"    🌸 {action_data['action_type']}: {action_data['source_node']} → {action_data['target_node']}")
        
        return result.get('healing_actions', 0) > 0
        
    except Exception as e:
        print(f"❌ Error testing collective healing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """🌸 Main test function."""
    print("🌸 COLLECTIVE HEALING DIRECT TEST")
    print("=" * 60)
    
    # Clean up old test data
    for cleanup_dir in ["memory/collective_healing", "memory/dao_decisions"]:
        if os.path.exists(cleanup_dir):
            for file in os.listdir(cleanup_dir):
                if file.endswith('.json') or file.endswith('.jsonl'):
                    os.remove(os.path.join(cleanup_dir, file))
    
    # Create test heartbeats with specific conditions
    create_test_heartbeats()
    
    # Wait a moment
    time.sleep(1)
    
    # Test collective healing
    healing_worked = test_collective_healing()
    
    print(f"\n🌸 TEST RESULT:")
    if healing_worked:
        print("  ✅ COLLECTIVE HEALING IS WORKING!")
        print("  🌿 Real biological mutual aid system operational")
    else:
        print("  ❌ Collective healing needs more work")
        print("  🔧 Check the debug output above for issues")
        
    print(f"\n🌿 Next: Test the observatory with:")
    print("  python3 canopy/sacred_terminal_observatory/terminal_observatory.py")

if __name__ == "__main__":
    main()