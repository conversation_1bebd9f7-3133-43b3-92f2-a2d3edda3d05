{"rituals": {"FileNotFoundError": {"type": "healing", "severity": "moderate", "recovery": "create_path", "breath_pause": 2}, "PermissionError": {"type": "protective", "severity": "high", "recovery": "alternate_path", "breath_pause": 5}, "json.JSONDecodeError": {"type": "restoration", "severity": "high", "recovery": "restore_backup", "breath_pause": 3}, "redis.ConnectionError": {"type": "isolation", "severity": "moderate", "recovery": "local_fallback", "breath_pause": 1}, "KeyboardInterrupt": {"type": "peaceful_ending", "severity": "natural", "recovery": "graceful_exit", "breath_pause": 1}}, "default_ritual": {"type": "generic_healing", "severity": "unknown", "recovery": "log_and_continue", "breath_pause": 3}, "critical_exceptions": ["SystemExit", "KeyboardInterrupt"]}