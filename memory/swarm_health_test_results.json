{"start_time": "2025-07-12T16:23:40.543100+00:00", "tests": {"standalone": true, "scenarios": {"healthy": {"vitality_state": "stable", "overall_score": 0.68975, "adaptations_count": 1, "test_passed": true}, "stressed": {"vitality_state": "stressed", "overall_score": 0.44775, "adaptations_count": 3, "test_passed": true}, "critical": {"vitality_state": "critical", "overall_score": 0.2375, "adaptations_count": 3, "test_passed": true}}, "thresholds": true, "adaptations": true, "direct_function": {"timestamp": "2025-07-12T16:23:40.545301+00:00", "assessment_completed": true, "vitality_state": "critical", "overall_score": 0.26, "dimensions_assessed": 5, "recommendations_generated": 8, "test_status": "success"}}, "end_time": "2025-07-12T16:23:40.545352+00:00", "overall_success": true}