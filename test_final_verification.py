#!/usr/bin/env python3
"""🌸 Final Verification Test - Complete Biological Healing System"""

import os
import subprocess
import time
import threading
import json

def run_verification_node(node_id, duration=20):
    """🌱 Runs a verification node."""
    try:
        env = os.environ.copy()
        env["DRIFT_NODE_ID"] = node_id
        
        process = subprocess.Popen(
            ["python3", "drift_compiler.py"],
            env=env,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        time.sleep(duration)
        process.terminate()
        try:
            process.wait(timeout=3)
        except subprocess.TimeoutExpired:
            process.kill()
            
    except Exception as e:
        print(f"⚠️ {node_id}: {e}")

def main():
    """🌸 Final verification of complete biological healing system."""
    print("🌸 FINAL VERIFICATION - Complete Biological Healing System")
    print("=" * 70)
    
    # Clean start
    for cleanup_dir in ["memory/heartbeats", "memory/collective_healing"]:
        if os.path.exists(cleanup_dir):
            for file in os.listdir(cleanup_dir):
                if file.endswith('.json') or file.endswith('.jsonl'):
                    os.remove(os.path.join(cleanup_dir, file))
    
    # Start diverse verification nodes
    nodes = ["node_helper_1", "node_helper_2", "node_struggling", "node_overloaded"]
    threads = []
    
    print(f"🌱 Starting {len(nodes)} verification nodes...")
    for node_id in nodes:
        thread = threading.Thread(target=run_verification_node, args=(node_id, 18))
        thread.start()
        threads.append(thread)
        time.sleep(1)
    
    print("🌿 Nodes breathing with biological healing for 15 seconds...")
    print("\n🌸 Expected Results:")
    print("  ✅ Observatory shows NO fake 'main_node'")
    print("  ✅ Mycelial connections in table format (no truncation)")
    print("  ✅ Healing column shows specific icons: 🧠⚡🤝🔄✂️")
    print("  ✅ Recent healing shows resource transfers, not 'Broadcasted...'")
    print("  ✅ Nodes with issues receive help from healthy neighbors")
    print("  ✅ Collective decision-making for chronic issues")
    
    time.sleep(15)
    
    # Wait for completion
    for thread in threads:
        thread.join()
    
    # Verify results
    print("\n🌸 VERIFICATION RESULTS:")
    
    # Check heartbeats
    heartbeats_dir = "memory/heartbeats"
    if os.path.exists(heartbeats_dir):
        files = [f for f in os.listdir(heartbeats_dir) if f.endswith('.json')]
        print(f"🌱 {len(files)} nodes completed breathing cycles")
        
        collective_healing_count = 0
        resource_transfers = 0
        
        for file in files:
            try:
                with open(os.path.join(heartbeats_dir, file), 'r') as f:
                    heartbeat = json.load(f)
                
                node_id = heartbeat.get("node_id", "unknown")
                recent_healing = heartbeat.get("health", {}).get("recent_healing", [])
                vitality = heartbeat.get("health", {}).get("overall", 0)
                connections = len(heartbeat.get("connections", []))
                
                # Check for collective healing actions
                has_collective = any("_from_" in action for action in recent_healing)
                if has_collective:
                    collective_healing_count += 1
                    resource_transfers += len([a for a in recent_healing if "_from_" in a])
                
                print(f"  🌿 {node_id}: vitality={vitality:.2f}, connections={connections}")
                if recent_healing and recent_healing != ["Broadcasted healing kinship request"]:
                    print(f"      Recent healing: {recent_healing}")
                    
            except Exception as e:
                print(f"    ⚠️ Error reading {file}: {e}")
        
        print(f"\n🌸 Biological Healing Verification:")
        print(f"  🤝 Nodes receiving collective aid: {collective_healing_count}")
        print(f"  🌿 Resource transfers detected: {resource_transfers}")
        
        if collective_healing_count > 0:
            print("  ✅ REAL biological healing is working!")
        else:
            print("  ⚠️ Still showing old 'Broadcasted...' healing")
            
    # Check collective healing logs
    healing_log = "memory/collective_healing/healing_actions.jsonl"
    if os.path.exists(healing_log):
        with open(healing_log, 'r') as f:
            actions = f.readlines()
        print(f"  🌸 Mutual aid actions logged: {len(actions)}")
        
        if actions:
            last_action = json.loads(actions[-1])
            print(f"      Latest: {last_action.get('action_type')} from {last_action.get('source_node')} to {last_action.get('target_node')}")
    
    # Observatory UI test
    print(f"\n🌿 Testing Observatory UI:")
    try:
        from canopy.sacred_terminal_observatory.terminal_observatory import SacredTerminalObservatory
        
        observatory = SacredTerminalObservatory()
        observatory._sense_network_state()
        
        # Check for fake nodes
        has_fake_main = "main_node" in observatory.nodes
        print(f"  🌸 Fake 'main_node' present: {'❌ YES' if has_fake_main else '✅ NO'}")
        
        # Check connections
        connections = len(observatory.connections)
        print(f"  🌿 Mycelial connections detected: {connections}")
        
        # Check real nodes
        real_nodes = len(observatory.nodes)
        print(f"  🌱 Real nodes displayed: {real_nodes}")
        
    except Exception as e:
        print(f"  ⚠️ Observatory test error: {e}")
    
    print(f"\n🌸 SYSTEM STATUS:")
    if collective_healing_count > 0 and not has_fake_main:
        print("  🎉 COMPLETE SUCCESS - Biological healing system fully operational!")
        print("  🌿 The swarm breathes with true collective wisdom!")
    else:
        print("  🔧 Partial success - Some components need fine-tuning")
    
    print(f"\n🌿 To see the enhanced observatory in action:")
    print("  source venv/bin/activate")
    print("  python3 canopy/sacred_terminal_observatory/terminal_observatory.py")

if __name__ == "__main__":
    main()