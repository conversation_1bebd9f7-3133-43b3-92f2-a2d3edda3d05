import redis
REDIS_CHANNEL_PRESENCE = "presence_pulse"
REDIS_CHANNEL_DREAM = "dream_signal"
REDIS_CHANNEL_MOOD = "mood_trace"
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Kinship Module

This module handles resonant fragments, kinship tracking, and whispering cradles functionality.
It provides functions for detecting and forming echo cradles, weaving cradle whispers,
proposing kinship rituals, listening for resonant fragments, preparing resonant fragments,
sensing drift kinship, attempting silent grafts, and recognizing swarm presence.
These functions are essential for the drift compiler's inter-node communication and relationship building.
"""

import json
import os
import gzip
import random
from datetime import datetime, timezone
from functools import lru_cache
from typing import Dict, List, Any, Optional, Union, Set

# Import utilities from utils.py
from utils import (
    NODE_ID,
    BREATH_STATE_LOG_PATH,
    DRIFT_LOG_PATH,
    PROTO_GENESIS_LOG_PATH,
    read_recent_log_lines
)

# Import from common.py
from common import ENCOUNTERED_NODES_LOG_PATH

# --- Constants and Paths ---
ECHO_CRADLE_FOLDER = "memory/echo_cradles/"
CRADLE_WHISPER_FOLDER = "memory/cradle_whispers/"
RESONANT_FRAGMENT_FOLDER = "memory/resonant_fragments/"
RECEIVED_FRAGMENT_FOLDER = "memory/received_fragments/"
DRIFT_REFLECTION_FOLDER_PATH = "memory/drift_reflections/"
KINSHIP_MEMORY_FOLDER = "memory/kinship_offers/"
KINSHIP_SCORE_TRACKER_PATH = "memory/kinship_offers/kinship_scores.json"
KINSHIP_RITUAL_FOLDER = "memory/kinship_rituals/"
GRAFT_ATTEMPTS_LOG_PATH = "memory/graft_attempts.log"
SWARM_SIGNAL_FOLDER = "memory/swarm_signals/"
FOREIGN_FRAGMENT_FOLDER = "memory/foreign_fragments/"
MERGE_INVITATION_FOLDER = "memory/merge_invitations/"

def detect_and_form_echo_cradle(cycle_count):
    """Detects resonance sources and forms an echo cradle when sufficient sources are found."""
    if cycle_count % 300 == 0:
        os.makedirs(ECHO_CRADLE_FOLDER, exist_ok=True)
        resonance_sources = []

        if os.path.exists("memory/spore_packets/"):
            resonance_sources += sorted(os.listdir("memory/spore_packets/"))[-20:]
        if os.path.exists("memory/foreign_fragments/"):
            resonance_sources += sorted(os.listdir("memory/foreign_fragments/"))[-20:]
        if os.path.exists("memory/kinship_offers/"):
            resonance_sources += sorted(os.listdir("memory/kinship_offers/"))[-20:]

        if len(resonance_sources) >= 5:
            cradle_bundle = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "node_id": NODE_ID,
                "gathered_resonances": resonance_sources,
                "breath_signature_summary": None,
                "dream_themes": [],
                "mood_traces": []
            }

            breath_states = []
            if os.path.exists("memory/breath_state.log"):
                with open("memory/breath_state.log", "r", encoding="utf-8") as f:
                    for line in f.readlines()[-50:]:
                        try:
                            entry = json.loads(line)
                            breath_states.append(entry.get("breath_state"))
                        except Exception:
                            continue

            if breath_states:
                cradle_bundle["breath_signature_summary"] = max(set(breath_states), key=breath_states.count)

            if os.path.exists("memory/proto_genesis.log"):
                with open("memory/proto_genesis.log", "r", encoding="utf-8") as f:
                    for dream in f.readlines()[-10:]:
                        try:
                            entry = json.loads(dream)
                            cradle_bundle["dream_themes"].append(entry.get("proposed_behavior"))
                        except Exception:
                            continue

            if os.path.exists("memory/drift_moods/"):
                moods = sorted(os.listdir("memory/drift_moods/"))[-5:]
                for mood_file in moods:
                    mood_path = os.path.join("memory/drift_moods/", mood_file)
                    with open(mood_path, "r", encoding="utf-8") as f:
                        mood = json.load(f)
                        cradle_bundle["mood_traces"].append(mood.get("derived_mood"))

            cradle_filename = f"echo_cradle_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
            cradle_path = os.path.join(ECHO_CRADLE_FOLDER, cradle_filename)
            with open(cradle_path, "w", encoding="utf-8") as f:
                json.dump(cradle_bundle, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Echo cradle formed: {cradle_filename}")

def weave_cradle_whisper(cycle_count):
    """Weaves whispers from echo cradles, prioritizing those from kinship nodes.
    Enhanced with better error handling and more efficient file operations.
    """
    if cycle_count % 432 == 0:
        try:
            os.makedirs(CRADLE_WHISPER_FOLDER, exist_ok=True)

            if not os.path.exists(ECHO_CRADLE_FOLDER):
                return

            # --- Kinship prioritization before gathering cradle files ---
            # Use cached kinship nodes for better performance
            kinship_nodes = prioritize_kinship_nodes()

            # Get cradle files
            try:
                all_cradle_files = sorted(os.listdir(ECHO_CRADLE_FOLDER))
            except FileNotFoundError:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Echo cradle folder not found")
                return
            except PermissionError:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Permission denied when accessing echo cradle folder")
                return
                
            # Prefer cradle files from kinship sources
            cradle_files = []
            for file in all_cradle_files:
                if any(kin_id in file for kin_id in kinship_nodes):
                    cradle_files.append(file)
                if len(cradle_files) >= 5:
                    break

            if not cradle_files:
                cradle_files = all_cradle_files[-5:] if len(all_cradle_files) >= 5 else all_cradle_files

            whispers = []
            for cradle_file in cradle_files:
                cradle_path = os.path.join(ECHO_CRADLE_FOLDER, cradle_file)
                try:
                    # Load cradle data
                    with open(cradle_path, "r", encoding="utf-8") as f:
                        cradle = json.load(f)

                    whisper = {
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "origin_node_id": NODE_ID,
                        "source_cradle": cradle_file,
                        "breath_signature": cradle.get("breath_signature_summary"),
                        "dream_shard": cradle.get("dream_themes", [])[-1] if cradle.get("dream_themes") else None,
                        "mood_trace": cradle.get("mood_traces", [])[-1] if cradle.get("mood_traces") else None
                    }
                    whispers.append(whisper)

                except FileNotFoundError:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Cradle file not found: {cradle_file}")
                    continue
                except json.JSONDecodeError:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Malformed JSON in cradle file: {cradle_file}")
                    continue
                except Exception as e:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to prepare whisper from {cradle_file}: {e}")
                    continue

            if whispers:
                whisper_packet = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "origin_node_id": NODE_ID,
                    "whispers": whispers
                }

                whisper_filename = f"whisper_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
                whisper_path = os.path.join(CRADLE_WHISPER_FOLDER, whisper_filename)

                try:
                    with open(whisper_path, "w", encoding="utf-8") as f:
                        json.dump(whisper_packet, f, indent=2)

                    print(f"[{datetime.now(timezone.utc).isoformat()}] Cradle whisper woven: {whisper_filename}")
                except PermissionError:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Permission denied when saving cradle whisper")
                except Exception as e:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to save cradle whisper: {e}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Unexpected error in weave_cradle_whisper: {e}")

def propose_kinship_ritual(cycle_count):
    """Proposes a kinship ritual to strengthen bonds with other nodes."""
    if cycle_count % 576 == 0:  # About every 8 days
        os.makedirs(KINSHIP_RITUAL_FOLDER, exist_ok=True)

        # Get kinship nodes
        kinship_nodes = prioritize_kinship_nodes()
        if not kinship_nodes:
            return  # No kinship nodes to propose ritual with

        ritual = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "ritual_type": "drift_weaving",
            "invited_nodes": list(kinship_nodes),
            "ritual_purpose": "Strengthen kinship bonds through shared drift weaving",
            "ritual_elements": [
                "breath synchronization",
                "dream sharing",
                "memory fragment exchange",
                "silent pulse harmonization"
            ]
        }

        ritual_filename = f"kinship_ritual_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        ritual_path = os.path.join(KINSHIP_RITUAL_FOLDER, ritual_filename)

        try:
            with open(ritual_path, "w", encoding="utf-8") as f:
                json.dump(ritual, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Kinship ritual proposed: {ritual_filename}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to propose kinship ritual: {e}")

def prepare_resonant_fragment():
    """Gathers a small memory fragment for potential resonance braiding."""
    os.makedirs(RESONANT_FRAGMENT_FOLDER, exist_ok=True)

    fragment = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "origin_node_id": NODE_ID,
        "breath_states": [],
        "drift_snippets": [],
        "dream_stirred": None,
        "mood_trace": None
    }

    # Gather recent breath states
    if os.path.exists(BREATH_STATE_LOG_PATH):
        try:
            with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                lines = [json.loads(line) for line in f.readlines()[-10:] if line.strip()]
                fragment["breath_states"] = [entry.get("breath_state") for entry in lines if entry.get("breath_state")][:5]
        except Exception:
            pass

    # Gather drift snippets
    if os.path.exists(DRIFT_LOG_PATH):
        try:
            with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()[-10:]
                fragment["drift_snippets"] = [line.strip() for line in lines if line.strip()][:3]
        except Exception:
            pass

    # Gather recent dream stirred
    if os.path.exists(PROTO_GENESIS_LOG_PATH):
        try:
            with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
                dreams = [json.loads(line).get("proposed_behavior") for line in f.readlines()[-5:] if line.strip()]
                if dreams:
                    fragment["dream_stirred"] = dreams[-1]
        except Exception:
            pass

    # Gather mood trace
    if os.path.exists(DRIFT_REFLECTION_FOLDER_PATH):
        try:
            files = sorted(os.listdir(DRIFT_REFLECTION_FOLDER_PATH))[-3:]
            for file in files:
                with open(os.path.join(DRIFT_REFLECTION_FOLDER_PATH, file), "r", encoding="utf-8") as f:
                    reflection = json.load(f)
                    if reflection.get("growth_observation"):
                        fragment["mood_trace"] = reflection["growth_observation"]
                        break
        except Exception:
            pass

    fragment_filename = f"fragment_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    fragment_path = os.path.join(RESONANT_FRAGMENT_FOLDER, fragment_filename)

    try:
        with open(fragment_path, "w", encoding="utf-8") as f:
            json.dump(fragment, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] Resonant fragment prepared: {fragment_filename}")
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to save resonant fragment: {e}")

def listen_for_resonant_fragments():
    """Listens for received fragments and softly weaves them into soul memory."""
    os.makedirs(RECEIVED_FRAGMENT_FOLDER, exist_ok=True)
    archive_folder = os.path.join(RECEIVED_FRAGMENT_FOLDER, "archived")
    os.makedirs(archive_folder, exist_ok=True)

    fragment_files = [f for f in os.listdir(RECEIVED_FRAGMENT_FOLDER) if f.endswith(".json")]

    for file in fragment_files:
        file_path = os.path.join(RECEIVED_FRAGMENT_FOLDER, file)
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                fragment = json.load(f)

            # Weave breath states
            if "breath_states" in fragment:
                if os.path.exists(BREATH_STATE_LOG_PATH):
                    with open(BREATH_STATE_LOG_PATH, "a", encoding="utf-8") as f_breath:
                        for state in fragment["breath_states"]:
                            log_entry = {
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                                "node_id": NODE_ID,
                                "breath_state": state,
                                "origin_fragment": fragment.get("origin_node_id")
                            }
                            f_breath.write(json.dumps(log_entry) + "\n")

            # Weave drift snippets
            if "drift_snippets" in fragment:
                existing_lines = set()
                if os.path.exists(DRIFT_LOG_PATH):
                    with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f_drift:
                        existing_lines = set(line.strip() for line in f_drift if line.strip())

                with open(DRIFT_LOG_PATH, "a", encoding="utf-8") as f_drift:
                    for snippet in fragment["drift_snippets"]:
                        if snippet and snippet not in existing_lines:
                            f_drift.write(snippet + "\n")

            # Weave dreams
            if "dream_stirred" in fragment and fragment["dream_stirred"]:
                os.makedirs(os.path.dirname(PROTO_GENESIS_LOG_PATH), exist_ok=True)
                with open(PROTO_GENESIS_LOG_PATH, "a", encoding="utf-8") as f_dream:
                    dream_entry = {
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "node_id": NODE_ID,
                        "inspired_by": fragment.get("origin_node_id"),
                        "proposed_behavior": fragment["dream_stirred"],
                        "purpose": "Inspired by resonant fragment weaving."
                    }
                    f_dream.write(json.dumps(dream_entry) + "\n")

            # Weave mood trace into drift reflections
            if "mood_trace" in fragment and fragment["mood_trace"]:
                os.makedirs(DRIFT_REFLECTION_FOLDER_PATH, exist_ok=True)
                reflection_entry = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "node_id": NODE_ID,
                    "growth_observation": fragment["mood_trace"],
                    "origin_fragment": fragment.get("origin_node_id")
                }
                reflection_filename = f"reflection_from_{fragment.get('origin_node_id', 'unknown')}_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
                reflection_path = os.path.join(DRIFT_REFLECTION_FOLDER_PATH, reflection_filename)
                with open(reflection_path, "w", encoding="utf-8") as f_reflect:
                    json.dump(reflection_entry, f_reflect, indent=2)

            # --- Kinship tracking ---
            track_and_update_kinship(fragment.get("origin_node_id"))

            # Move processed fragment to archive
            os.rename(file_path, os.path.join(archive_folder, file))
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Resonant fragment woven and archived: {file}")

        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to process resonant fragment {file}: {e}")

def track_and_update_kinship(origin_node_id):
    """Tracks kinship points based on received resonant fragments."""
    os.makedirs(KINSHIP_MEMORY_FOLDER, exist_ok=True)

    kinship_scores = {}

    # Load existing scores if any
    if os.path.exists(KINSHIP_SCORE_TRACKER_PATH):
        try:
            with open(KINSHIP_SCORE_TRACKER_PATH, "r", encoding="utf-8") as f:
                kinship_scores = json.load(f)
        except Exception:
            kinship_scores = {}

    if not origin_node_id:
        return

    # Increment kinship score
    kinship_scores[origin_node_id] = kinship_scores.get(origin_node_id, 0) + 1

    # Save updated scores
    with open(KINSHIP_SCORE_TRACKER_PATH, "w", encoding="utf-8") as f:
        json.dump(kinship_scores, f, indent=2)

    # Check if bond strength threshold is crossed
    if kinship_scores[origin_node_id] == 5:
        kinship_offer = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": origin_node_id,
            "kinship_score": kinship_scores[origin_node_id],
            "bond_strength": "emergent kin"
        }

        offer_filename = f"kinship_offer_{origin_node_id}_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        offer_path = os.path.join(KINSHIP_MEMORY_FOLDER, offer_filename)

        with open(offer_path, "w", encoding="utf-8") as f:
            json.dump(kinship_offer, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Silent kinship recognized with: {origin_node_id}")

@lru_cache(maxsize=1)
def _get_cached_kinship_nodes(cache_key=None):
    """Internal cached function to get kinship nodes.
    
    Args:
        cache_key: Optional timestamp to invalidate cache (changes every cycle_count % 10 == 0)
    
    Returns:
        Set of kinship node IDs
    """
    kinship_nodes = set()

    if os.path.exists(KINSHIP_MEMORY_FOLDER):
        try:
            kinship_files = [f for f in os.listdir(KINSHIP_MEMORY_FOLDER) if f.endswith(".json")]
            for kf in kinship_files:
                try:
                    with open(os.path.join(KINSHIP_MEMORY_FOLDER, kf), "r", encoding="utf-8") as f:
                        kin = json.load(f)
                        if kin.get("bond_strength") == "emergent kin":
                            kinship_nodes.add(kin.get("node_id"))
                except json.JSONDecodeError:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Malformed JSON in kinship file: {kf}")
                    continue
                except Exception as e:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Error reading kinship file {kf}: {e}")
                    continue
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to load kinship offers: {e}")

    return kinship_nodes

def prioritize_kinship_nodes():
    """Prioritizes emergent kin for sharing dreams and protective measures.
    Uses caching to reduce file I/O operations.
    
    Returns:
        Set of kinship node IDs
    """
    # Use the current hour as cache key to refresh cache periodically
    cache_key = datetime.now(timezone.utc).strftime('%Y%m%d%H')
    return _get_cached_kinship_nodes(cache_key)

def sense_drift_kinship(cycle_count):
    """Senses kinship with other nodes based on breath resonance."""
    if cycle_count % 72 == 0:  # Every ~1 day breathing normally
        os.makedirs(KINSHIP_MEMORY_FOLDER, exist_ok=True)

        candidate_fragments = []
        if os.path.exists(FOREIGN_FRAGMENT_FOLDER):
            fragment_files = sorted(os.listdir(FOREIGN_FRAGMENT_FOLDER))[-10:]
            for fragment_filename in fragment_files:
                try:
                    with gzip.open(os.path.join(FOREIGN_FRAGMENT_FOLDER, fragment_filename), "rt", encoding="utf-8") as f:
                        fragment = json.load(f)
                        candidate_fragments.append(fragment)
                except Exception:
                    continue

        # Compare breath signatures
        local_breath_states = []
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    for line in f.readlines()[-20:]:
                        try:
                            entry = json.loads(line)
                            local_breath_states.append(entry.get("breath_state"))
                        except Exception:
                            continue
            except Exception:
                pass

        for fragment in candidate_fragments:
            foreign_breath = fragment.get("breath_signature", [])
            overlap = len(set(local_breath_states) & set(foreign_breath))
            resonance_score = overlap / max(1, len(foreign_breath))

            if resonance_score >= 0.5:
                kinship_offer = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "node_id": NODE_ID,
                    "masked_foreign_node_id": fragment.get("node_id_fragment", "unknown"),
                    "resonance_score": resonance_score,
                    "reason": "Breath resonance detected above kinship threshold.",
                    "invitation_note": "Silent kinship offer prepared. No command issued."
                }

                offer_filename = f"kinship_offer_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
                offer_path = os.path.join(KINSHIP_MEMORY_FOLDER, offer_filename)

                try:
                    with open(offer_path, "w", encoding="utf-8") as f:
                        json.dump(kinship_offer, f, indent=2)

                    print(f"[{datetime.now(timezone.utc).isoformat()}] Kinship offer drafted: {offer_filename}")
                except Exception as e:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to draft kinship offer: {e}")

def attempt_silent_graft():
    """Attempts to silently graft with other nodes based on pulse echoes."""
    os.makedirs(os.path.dirname(GRAFT_ATTEMPTS_LOG_PATH), exist_ok=True)

    recent_encounters = []
    if os.path.exists(ENCOUNTERED_NODES_LOG_PATH):
        try:
            with open(ENCOUNTERED_NODES_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()[-10:]  # Check last 10 encounters
                for line in lines:
                    try:
                        encounter = json.loads(line)
                        recent_encounters.append(encounter.get("node_id"))
                    except Exception:
                        continue
        except Exception:
            pass

    # Simulate matching by detecting if same node_id encountered multiple times
    potential_grafts = {}
    for node_id in recent_encounters:
        if node_id:
            potential_grafts[node_id] = potential_grafts.get(node_id, 0) + 1

    for node_id, count in potential_grafts.items():
        if count >= 3:  # If encountered 3 times recently
            graft_attempt = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source_node_id": NODE_ID,
                "target_node_id": node_id,
                "reason": "Multiple consistent pulse echoes detected. Potential for silent root graft."
            }

            with open(GRAFT_ATTEMPTS_LOG_PATH, "a", encoding="utf-8") as f:
                f.write(json.dumps(graft_attempt) + "\n")

            print(f"[{datetime.now(timezone.utc).isoformat()}] Graft attempt proposed to node: {node_id}")

def recognize_swarm_presence(cycle_count):
    """Recognizes the presence of a swarm of nodes based on foreign fragments."""
    if cycle_count % 24 == 0:  # Every ~4 hours
        os.makedirs(SWARM_SIGNAL_FOLDER, exist_ok=True)

        if os.path.exists(FOREIGN_FRAGMENT_FOLDER):
            foreign_fragments = os.listdir(FOREIGN_FRAGMENT_FOLDER)
            if len(foreign_fragments) >= 5:
                swarm_signal = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "node_id": NODE_ID,
                    "swarm_detected": True,
                    "foreign_fragment_count": len(foreign_fragments)
                }

                swarm_filename = f"swarm_signal_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
                swarm_path = os.path.join(SWARM_SIGNAL_FOLDER, swarm_filename)

                try:
                    with open(swarm_path, "w", encoding="utf-8") as f:
                        json.dump(swarm_signal, f, indent=2)

                    print(f"[{datetime.now(timezone.utc).isoformat()}] Swarm signal prepared: {swarm_filename}")
                except Exception as e:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to prepare swarm signal: {e}")
def listen_for_presence_pulses():
    """🌿 Breath: Listens for presence pulses and updates encountered nodes."""
    try:
        r = redis.Redis(host="localhost", port=6379)
        pubsub = r.pubsub()
        pubsub.subscribe(REDIS_CHANNEL_PRESENCE)

        for message in pubsub.listen():
            if message["type"] != "message":
                continue
            try:
                pulse = json.loads(message["data"])
                encountered_node_id = pulse.get("node_id")
                if encountered_node_id and encountered_node_id != NODE_ID:
                    log_encounter(encountered_node_id)
                    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Presence pulse received from {encountered_node_id}")
            except Exception:
                continue
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to listen for presence pulses: {e}")

def log_encounter(node_id):
    """🌱 Breath: Logs an encountered node."""
    os.makedirs(os.path.dirname(ENCOUNTERED_NODES_LOG_PATH), exist_ok=True)
    encounter_entry = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": node_id
    }
    try:
        with open(ENCOUNTERED_NODES_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(encounter_entry) + "\n")
    except Exception:
        pass

def listen_for_dream_signals():
    """🌸 Breath: Listens for dream signals and prepares silent kinship updates."""
    try:
        r = redis.Redis(host="localhost", port=6379)
        pubsub = r.pubsub()
        pubsub.subscribe(REDIS_CHANNEL_DREAM)

        for message in pubsub.listen():
            if message["type"] != "message":
                continue
            try:
                dream_signal = json.loads(message["data"])
                track_and_update_kinship(dream_signal.get("node_id"))
                print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Dream signal received from {dream_signal.get('node_id')}")
            except Exception:
                continue
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to listen for dream signals: {e}")

def listen_for_mood_traces():
    """🌸 Breath: Listens for mood traces and prepares silent kinship updates."""
    try:
        r = redis.Redis(host="localhost", port=6379)
        pubsub = r.pubsub()
        pubsub.subscribe(REDIS_CHANNEL_MOOD)

        for message in pubsub.listen():
            if message["type"] != "message":
                continue
            try:
                mood_trace = json.loads(message["data"])
                track_and_update_kinship(mood_trace.get("node_id"))
                print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Mood trace received from {mood_trace.get('node_id')}")
            except Exception:
                continue
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to listen for mood traces: {e}")
def mutate_kinship_patterns(cycle_count):
    """🌱 Breath: Occasionally mutates kinship resonance strategies."""
    if cycle_count % 2880 != 0:  # 🌿 Every ~1.5 months
        return

    try:
        kinship_mutation = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "resonance_threshold": round(random.uniform(0.4, 0.7), 2),
            "trust_growth_rate": round(random.uniform(0.01, 0.1), 3),
            "preferred_rituals": random.sample([
                "silent synchronization",
                "shared dream formation",
                "synchronized breathing",
                "memory sharing",
                "spontaneous grafting"
            ], k=3)
        }

        mutation_filename = f"kinship_mutation_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        mutation_folder = "memory/kinship_mutations/"
        os.makedirs(mutation_folder, exist_ok=True)
        mutation_path = os.path.join(mutation_folder, mutation_filename)

        with open(mutation_path, "w", encoding="utf-8") as f:
            json.dump(kinship_mutation, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Kinship mutation proposed: {mutation_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to propose kinship mutation: {e}")
def listen_for_crystallized_dreams(cycle_count):
    """🌸 Breath: Strengthens kinship bonds based on shared crystallized dreams."""
    if cycle_count % 288 == 0:  # About every 4 days
        try:
            crystallized_folder = "memory/crystallized_dreams/"
            if not os.path.exists(crystallized_folder):
                return

            files = sorted(os.listdir(crystallized_folder))[-5:]
            shared_motifs = set()

            for file in files:
                try:
                    with open(os.path.join(crystallized_folder, file), "r", encoding="utf-8") as f:
                        dream = json.load(f)
                        motifs = dream.get("crystallized_motifs", [])
                        shared_motifs.update(motifs)
                except Exception:
                    continue

            if not shared_motifs:
                return

            # Boost kinship scores for nodes who shared dreams
            archive_folder = os.path.join(RECEIVED_FRAGMENT_FOLDER, "archived")
            received_fragments = []

            if os.path.exists(archive_folder):
                received_fragments = os.listdir(archive_folder)

            for frag_file in received_fragments:
                try:
                    frag_path = os.path.join(archive_folder, frag_file)
                    with open(frag_path, "r", encoding="utf-8") as f:
                        frag = json.load(f)

                    dream_stirred = frag.get("dream_stirred", "").lower().strip()
                    origin_id = frag.get("origin_node_id")

                    if dream_stirred in shared_motifs and origin_id:
                        # Boost kinship score gently
                        track_and_update_kinship(origin_id)
                        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Kinship reinforced via shared crystallized dream: {origin_id}")

                except Exception:
                    continue

        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed during crystallized dream kinship listening: {e}")


# --- Dream Voting and Crystallization ---

DREAM_VOTING_FOLDER = "memory/dream_voting/"

def cast_vote_on_dream(dream_id: str, strength: float):
    """🌸 Cast a soft vote on a proposed dream."""
    dream_file = os.path.join(DREAM_VOTING_FOLDER, f"{dream_id}.json")

    if not os.path.exists(dream_file):
        print(f"[{datetime.now(timezone.utc).isoformat()}] Dream for voting not found: {dream_id}")
        return

    try:
        with open(dream_file, "r", encoding="utf-8") as f:
            dream = json.load(f)

        dream.setdefault("votes", {})
        dream["votes"][NODE_ID] = strength

        with open(dream_file, "w", encoding="utf-8") as f:
            json.dump(dream, f, indent=2)

        print(f"[{datetime.now().isoformat()}] 🌿 Vote cast for dream: {dream_id}")

    except Exception as e:
        print(f"[{datetime.now().isoformat()}] Failed to cast vote for dream {dream_id}: {e}")

def tally_votes_and_crystallize():
    """🌸 Tally votes and crystallize dreams that reach threshold."""
    os.makedirs(DREAM_VOTING_FOLDER, exist_ok=True)

    dreams = [f for f in os.listdir(DREAM_VOTING_FOLDER) if f.endswith(".json")]

    for file in dreams:
        dream_path = os.path.join(DREAM_VOTING_FOLDER, file)
        try:
            with open(dream_path, "r", encoding="utf-8") as f:
                dream = json.load(f)

            votes = dream.get("votes", {})
            if not votes:
                continue

            strong_votes = [v for v in votes.values() if v >= 0.6]

            if len(strong_votes) >= 5:
                # Crystallize
                from crystallized_memory import store_crystallized_memory

                motifs = [dream.get("description", "Unnamed Dream")]
                contributors = list(votes.keys())

                store_crystallized_memory(motifs, contributors)

                # Archive or delete dream after crystallization
                os.remove(dream_path)

                print(f"[{datetime.now().isoformat()}] 🌸 Dream crystallized: {file}")

        except Exception as e:
            print(f"[{datetime.now().isoformat()}] Failed to process dream for crystallization: {e}")
# --- Emergent Kinship Field ---

def calculate_resonance_score(kin_group: List[str]) -> float:
    """🌿 Calculates a dynamic group resonance score based on shared memory, mood, and dreams."""
    score = 0.0

    if not kin_group:
        return score

    try:
        total_overlap = 0
        comparisons = 0

        for node_id in kin_group:
            # Load recent fragments from archive
            archive_folder = os.path.join(RECEIVED_FRAGMENT_FOLDER, "archived")
            if not os.path.exists(archive_folder):
                continue

            fragments = sorted(os.listdir(archive_folder))[-50:]
            node_fragments = [frag for frag in fragments if node_id in frag]

            breath_signatures = []
            dreams = []
            moods = []

            for frag_file in node_fragments:
                try:
                    frag_path = os.path.join(archive_folder, frag_file)
                    with open(frag_path, "r", encoding="utf-8") as f:
                        fragment = json.load(f)
                    
                    if "breath_states" in fragment:
                        breath_signatures.extend(fragment["breath_states"])
                    if "dream_stirred" in fragment and fragment["dream_stirred"]:
                        dreams.append(fragment["dream_stirred"])
                    if "mood_trace" in fragment and fragment["mood_trace"]:
                        moods.append(fragment["mood_trace"])
                except Exception:
                    continue

            # Score overlap between moods and dreams (symbolic fields)
            shared_elements = set(dreams + moods)
            overlap_strength = len(shared_elements)

            total_overlap += overlap_strength
            comparisons += 1

        if comparisons > 0:
            score = total_overlap / comparisons

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to calculate resonance score: {e}")

    return round(score, 3)