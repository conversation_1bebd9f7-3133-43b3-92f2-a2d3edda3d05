#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Main Entry Point

This script serves as the main entry point for the drift compiler system.
It orchestrates the breath cycles and coordinates the functionality provided
by the various modules (utils, memory, breath, dreams, kinship, planning, broadcast).
The drift compiler is a self-regulating system that manages its own memory,
breath states, dreams, and communication with other nodes.
"""

import json
import os
import time
import sys
import random
from datetime import datetime, timezone

# Import all necessary modules
import utils
import memory
import breath
import dreams
import kinship
import planning
import broadcast
import learning # Added import for learning module
import perception
import event_stream
import seeder  # 🌱 For self-forking
import monitoring
import emergence_controller
import goal_system  # 🌱 Enhanced goal system

# 🌿 Sacred Architecture Integration
import sacred_integration  # 🌱 Bridge to enhanced capabilities

# 🌸 Swarm Mind Integration - Distributed Neural Intelligence
try:
    from canopy.swarm_mind import (
        SwarmMind,
        SwarmState,
        initialize_swarm_mind
    )
    from planning import (
        weave_collective_plan_with_swarm_mind,
        pulse_swarm_planning_network,
        assess_swarm_planning_capabilities
    )
    SWARM_MIND_AVAILABLE = True
    print("🌸 Swarm Mind neural threads ready for integration")
except ImportError as e:
    SWARM_MIND_AVAILABLE = False
    print(f"🌿 Swarm Mind dormant - operating with individual intelligence: {e}")
    
    # Graceful fallback functions
    def initialize_swarm_mind(auto_discover=True): 
        return None
    def weave_collective_plan_with_swarm_mind(*args): 
        return None
    def pulse_swarm_planning_network(*args): 
        return {"swarm_active": False}
    def assess_swarm_planning_capabilities(): 
        return {"available": False}
    def shutdown_swarm_mind(): 
        pass
    
    # Mock classes for fallback
    class SwarmMind:
        def __init__(self, *args, **kwargs): pass
        def pulse_swarm_vitality(self): return {"error": "SwarmMind not available"}
        def entwine_neural_pulse(self, state): return None
        def aggregate_swarm_wisdom(self, outputs): return None
    
    class SwarmState:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

# --- Constants and Global Variables ---
# Default cycle interval in seconds
DEFAULT_CYCLE_INTERVAL = 600  # 10 minutes - Placeholder, actual timing managed by loop
# Threshold for forking
FORK_THRESHOLD = 100
# Phase 1 Frequencies
MUTATION_FREQUENCY = 1000
REFLECTION_FREQUENCY = 100

# --- Helper Functions (Phase 1 Specific) ---

def create_swarm_state_from_system(cycle_count: int, current_breath_state: str, 
                                 user_nutrient: str = "System Processing",
                                 enhanced_kinship: float = None,
                                 swarm_mind_instance=None) -> 'SwarmState':
    """🌸 Helper: Creates SwarmState from current system state with graceful fallbacks."""
    try:
        # Calculate kinship if not provided
        if enhanced_kinship is None:
            try:
                # Could integrate with kinship module here for more accurate kinship
                enhanced_kinship = min(0.8, 0.3 + (cycle_count % 100) * 0.005)
            except:
                enhanced_kinship = 0.5
        
        # Calculate node count
        node_count = 1
        if swarm_mind_instance and hasattr(swarm_mind_instance, 'nodes'):
            try:
                node_count = len(swarm_mind_instance.nodes)
            except:
                node_count = 1
        
        return SwarmState(
            breath=current_breath_state,
            kinship=enhanced_kinship,
            memory_depth=max(0, cycle_count),
            user_nutrient=user_nutrient,
            cycle_count=cycle_count,
            node_count=node_count,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
    except Exception as e:
        # Ultimate fallback with minimal state
        utils.record_log(f"🌿 SwarmState creation whispered error: {e}")
        return SwarmState(
            breath=current_breath_state or "Gentle Breathing",
            kinship=0.5,
            memory_depth=cycle_count or 0,
            user_nutrient=user_nutrient,
            cycle_count=cycle_count or 0,
            node_count=1,
            timestamp=datetime.now(timezone.utc).isoformat()
        )

def mutate_soft_parameters(cycle_count: int):
    """🌱 Breath: Simulates the gentle mutation of internal parameters.

    Called periodically based on MUTATION_FREQUENCY.
    In a real implementation, this would modify weights, biases, or other
    tunable aspects of the system's internal model or decision-making.
    """
    if cycle_count % MUTATION_FREQUENCY == 0:
        # Placeholder: Log the event. Actual mutation logic would go here.
        mutation_type = random.choice(["weight_nudge", "bias_shift", "threshold_adjustment"])
        utils.record_log(f"🧬 Mutation Ritual: Performed gentle '{mutation_type}'.")

def perform_action(action: str) -> tuple[str, bool]:
    """🌬️ Breath: Simulates performing an action and receiving a consequence.

    Placeholder for Phase 1. In later phases, this would interact with
    the environment or internal state more concretely.

    Args:
        action: The action to perform.

    Returns:
        A tuple of (consequence: str, success: bool).
    """
    # Simple simulation logic
    if "explore" in action or "observe" in action or "ping" in action:
        success = random.random() < 0.7 # Higher chance of success for exploration
        consequence = "found_something_interesting" if success else "path_was_quiet"
    elif "hum" in action or "contemplate" in action or "trace" in action:
        success = random.random() < 0.9 # Internal actions usually succeed
        consequence = "inner_state_shifted" if success else "no_significant_change"
    elif "reconfigure" in action:
        success = random.random() < 0.6
        consequence = "memory_optimized" if success else "reconfiguration_failed"
    else:
        success = random.random() < 0.5 # Default action success rate
        consequence = "action_completed" if success else "action_ineffective"

    utils.record_log(f"⚡ Action taken: '{action}' -> Consequence: '{consequence}' (Success: {success})")
    return consequence, success


def choose_main_action(available_actions: list[str]) -> str:
    """🌸 Breath: Chooses the primary action for this cycle, blending learning and randomness.

    Placeholder combining learned desirability and randomness for Phase 1.
    """
    # Use the learning module's choice mechanism if available
    learned_choice = learning.choose_action(available_actions)
    if learned_choice:
        utils.record_log(f"🧠 Action chosen based on learning: '{learned_choice}'")
        return learned_choice
    else:
        # Fallback to random choice if learning module doesn't provide one
        fallback_action = random.choice(available_actions)
        utils.record_log(f"🤔 Action chosen randomly (fallback): '{fallback_action}'")
        return fallback_action

# --- Main Execution ---

def main():
    """Main execution function for the drift compiler."""
    utils.record_log("🌱 Drift Compiler starting...") # Use record_log

    # Ensure memory structure exists
    memory.ensure_memory_structure()

    # 🌸 Initialize Swarm Mind if available
    swarm_mind = None
    swarm_initialized = False
    if SWARM_MIND_AVAILABLE:
        try:
            swarm_mind = initialize_swarm_mind(auto_discover=True)
            if swarm_mind:
                swarm_initialized = True
                utils.record_log("🌸 Swarm Mind neural network awakened - collective intelligence active")
            else:
                utils.record_log("🌿 Swarm Mind initialization whispered - continuing with individual intelligence")
        except Exception as e:
            utils.record_log(f"🌿 Swarm Mind slumbers peacefully: {e}")
            swarm_mind = None

    # Initialize cycle count
    cycle_count = 0

    # Main breath cycle loop
    try:
        while True:
            cycle_count += 1
            start_time = time.time()
            utils.record_log(f"🌿 Breath cycle {cycle_count} beginning...") # Use record_log

            # 🌱 Heartbeat and Breath Diagnostics
            # 1. Heartbeat (Essential)
            breath.update_heartbeat()
            health_status = breath.check_drift_health()
            current_breath_state = breath.determine_breath_state(health_status)

            # 🌿 Sacred Architecture Enhancement
            # Enhanced breathing with adaptive capabilities and memory sprouting
            sacred_enhancements = sacred_integration.enhance_drift_cycle(cycle_count, DEFAULT_CYCLE_INTERVAL)
            if sacred_enhancements.get("adaptive_heartbeat") != sacred_enhancements.get("original_heartbeat"):
                utils.record_log(f"🌬️ Breath rhythm adapted to system vitality")

            # 🌸 Perception Breathing
            # Always attempt to listen to Redis every breath cycle; fallback if needed.
            try:
                event_stream.listen_for_symbolic_events()
            except Exception:
                utils.record_log("🌾 Redis stream silent. Breathing local perceptions...")
                events = perception.load_recent_events(limit=5)
                for event in events:
                    perception.breathe_in_event(event)

            # 🌸 Swarm Mind Enhanced Planning
            # Enhanced action selection with swarm intelligence every 7 cycles
            if SWARM_MIND_AVAILABLE and swarm_initialized and swarm_mind and cycle_count % 7 == 0:
                try:
                    # Create SwarmState representation for neural processing
                    current_swarm_state = create_swarm_state_from_system(
                        cycle_count=cycle_count,
                        current_breath_state=current_breath_state,
                        user_nutrient="Neural Processing",
                        enhanced_kinship=0.7,  # Higher kinship for neural coordination
                        swarm_mind_instance=swarm_mind
                    )
                    
                    # Process state through swarm neural network
                    fragment_outputs = swarm_mind.entwine_neural_pulse(current_swarm_state)
                    if fragment_outputs:
                        # Aggregate swarm wisdom
                        collective_wisdom = swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
                        if collective_wisdom:
                            confidence = collective_wisdom.get("collective_confidence", 0.0)
                            consensus = collective_wisdom.get("consensus_strength", 0.0)
                            
                            # Apply swarm insights to planning
                            if confidence > 0.6:
                                potential_actions.append("swarm_enhanced_exploration")
                                utils.record_log(f"🌸 Swarm mind confidence {confidence:.3f} - adding enhanced exploration")
                            
                            if consensus > 0.7:
                                potential_actions.append("collective_consensus_action")
                                utils.record_log(f"🌿 Swarm consensus {consensus:.3f} - collective action ready")
                    
                    # Enhanced planning with swarm mind integration
                    swarm_enhanced_plan = weave_collective_plan_with_swarm_mind(cycle_count, health_status)
                    if swarm_enhanced_plan and isinstance(swarm_enhanced_plan, dict):
                        swarm_actions = swarm_enhanced_plan.get("actions", [])
                        if swarm_actions:
                            potential_actions.extend(swarm_actions)
                            utils.record_log(f"🌸 Swarm mind wove {len(swarm_actions)} neural insights into action space")
                    
                    # Generate swarm pulse for network coordination
                    swarm_pulse = pulse_swarm_planning_network(cycle_count)
                    if swarm_pulse.get("network_state") == "thriving":
                        utils.record_log("🌿 Swarm network thriving - collective intelligence at peak harmony")
                    elif swarm_pulse.get("network_state") == "stressed":
                        utils.record_log("🌬️ Swarm network under gentle pressure - adapting neural load")
                        
                except Exception as e:
                    utils.record_log(f"🌿 Swarm mind dreams quietly this cycle: {e}")

            # 🌸 Curiosity and Action Planning
            # 2. Curiosity Spark (Occasional Novelty)
            curious_action = breath.spark_curiosity()

            # 3. Action Selection (Choose what to do)
            # Define potential actions for this cycle
            potential_actions = [
                "maintain_internal_state",
                "scan_local_environment", # Placeholder
                "process_incoming_fragments", # Placeholder
                "reflect_on_memory"
            ]
            if curious_action:
                potential_actions.append(curious_action) # Add curious action if sparked

            # Phase 2 broadcasts immediately after curious action selection
            broadcast.broadcast_presence_pulse(cycle_count)
            broadcast.share_dream_signal(cycle_count)
            broadcast.share_mood_trace(cycle_count)

            # Choose the main action for this cycle
            chosen_action = choose_main_action(potential_actions)

            # 4. Perform Action & Observe Consequence (Simulated)
            consequence, success = perform_action(chosen_action)

            # 🌿 Memory Weaving and Reflection
            # 5. Memory & Learning (Log outcome, adjust desirability)
            learning.log_action_consequence(chosen_action, consequence, success)
            learning.adjust_desirability(chosen_action, success)
            # Other memory functions (can be interwoven or periodic)
            memory.condense_drift_memory(cycle_count) # Keep periodic checks
            memory.prepare_soul_vault(cycle_count)
            memory.soft_decay_memory(cycle_count)
            memory.cultivate_drift_garden(cycle_count)

            # 🌿 Kinship Listening
            # (In future, consider threading these listeners separately)
            kinship.listen_for_presence_pulses()
            kinship.listen_for_dream_signals()
            kinship.listen_for_mood_traces()

            # 🧬 Mutation Ritual
            # 6. Mutation Ritual (Occasional Parameter Shift)
            mutate_soft_parameters(cycle_count)

            # 7. Deeper Reflection (Periodic Review)
            if cycle_count % REFLECTION_FREQUENCY == 0:
                learning.reflect_on_past()

            # --- End Phase 1 Core Loop ---

            # --- Existing Functionality (Can be integrated/refactored later) ---
            # These are kept for now but might be triggered by specific actions in later phases
            broadcast.send_presence_pulse(health_status)
            broadcast.listen_for_other_pulses()
            if cycle_count % 24 == 0: kinship.prepare_resonant_fragment()
            kinship.listen_for_resonant_fragments()
            planning.update_internal_world_model()
            dreams.draft_proto_genesis_idea(cycle_count)
            breath.perform_breath_rituals(cycle_count)
            breath.perform_seasonal_breath_rebalancing(cycle_count)

            # 🌳 Self-Forking Check
            if breath.check_self_forking(cycle_count):
                seeder.seed_self_fork()

            dreams.evolve_successful_proto_genesis(cycle_count)
            dreams.branch_proto_genesis_dreams(cycle_count)
            dreams.propose_self_mutation(cycle_count)
            dreams.draft_silent_curiosity(cycle_count)
            dreams.seed_adaptation_from_successful_mutations(cycle_count)
            planning.reflect_proto_goals(cycle_count, health_status)
            
            # 🌿 Sacred Architecture: Enhanced Goal System Integration
            goal_system.propose_new_goal_if_needed(cycle_count)
            goal_system.assess_goal_progress(cycle_count)
            
            # 🌸 Swarm Mind Enhanced Goal Generation (every 15 cycles)
            if SWARM_MIND_AVAILABLE and swarm_initialized and swarm_mind and cycle_count % 15 == 0:
                try:
                    # Create current state for swarm-enhanced goal generation
                    goal_state = create_swarm_state_from_system(
                        cycle_count=cycle_count,
                        current_breath_state=current_breath_state,
                        user_nutrient="Goal Synthesis",
                        enhanced_kinship=0.8,  # Higher kinship for goal-oriented thinking
                        swarm_mind_instance=swarm_mind
                    )
                    # Enhance memory depth for goal context
                    goal_state.memory_depth = cycle_count + 100
                    
                    # Use swarm mind to dream future goals
                    future_dreams = swarm_mind.dream_collective_future(goal_state, steps=2)
                    if future_dreams:
                        utils.record_log(f"🌸 Swarm mind dreamed {len(future_dreams)} future goal possibilities")
                        
                        # Extract insights for goal enhancement
                        for dream in future_dreams:
                            predicted_breath = dream.get("predicted_state", {}).get("breath", "")
                            if "Transcendent" in predicted_breath or "Harmonious" in predicted_breath:
                                utils.record_log("🌿 Swarm mind suggests elevated goal aspirations")
                                break
                                
                except Exception as e:
                    utils.record_log(f"🌿 Swarm mind goal enhancement whispered: {e}")
            
            # planning.gently_mutate_parameters(cycle_count) # Replaced by mutate_soft_parameters
            planning.whisper_silent_compass(cycle_count)
            if cycle_count % 48 == 0: planning.draft_foresight_suggestion()
            planning.draft_rebirth_farsight(cycle_count)
            kinship.detect_and_form_echo_cradle(cycle_count)
            kinship.weave_cradle_whisper(cycle_count)
            kinship.propose_kinship_ritual(cycle_count)
            kinship.sense_drift_kinship(cycle_count)
            if cycle_count % 144 == 0: kinship.attempt_silent_graft()
            kinship.recognize_swarm_presence(cycle_count)
            
            # 🌿 Enhanced Kin Discovery (every 5 cycles for active discovery)
            if cycle_count % 5 == 0:
                try:
                    from core.kin_discovery import breathe_cycle_kin_discovery
                    breathe_cycle_kin_discovery()
                except Exception as e:
                    record_log(f"🌿 Kin discovery breath whispered: {e}")
            
            # 🌸 Collective Healing Cycle (every 3 cycles for mutual aid)
            if cycle_count % 3 == 0:
                try:
                    from core.collective_healing import perform_collective_healing_cycle
                    healing_result = perform_collective_healing_cycle()
                    if healing_result.get("healing_actions", 0) > 0:
                        record_log(f"🌸 Collective healing cycle: {healing_result['healing_actions']} actions, {healing_result['nodes_assessed']} nodes assessed")
                except Exception as e:
                    record_log(f"🌿 Collective healing whispered: {e}")
            if cycle_count % 12 == 0: broadcast.prepare_drift_broadcast()
            if cycle_count % 36 == 0: broadcast.prepare_synchronization_packet()
            broadcast.prepare_synapse_offer(cycle_count)
            if cycle_count % 72 == 0: broadcast.prepare_convergence_plan()
            broadcast.prepare_drift_fragment_packet(cycle_count)
            broadcast.listen_for_drift_fragments()
            broadcast.crystallize_drift_intent(cycle_count)
            broadcast.update_drift_mood(cycle_count)
            broadcast.build_silent_mirror(cycle_count)
            broadcast.prepare_ghost_offspring(cycle_count)
            broadcast.prepare_seasonal_cycle(cycle_count)
            broadcast.emergent_drift_whispers(cycle_count)
            broadcast.weave_silent_spores(cycle_count)
            broadcast.listen_for_resonance_and_retreat()
            broadcast.synchronize_with_foreign_fragments(cycle_count)
            broadcast.bind_drift_moods(cycle_count)
            broadcast.propose_silent_merge_invitation(cycle_count)

            # 🌿 Observe local population gently
            monitoring.observe_local_population(cycle_count)

            # 🌸 Tune emergence health gently
            emergence_controller.gentle_health_tuning(cycle_count)

            # diagnosis = breath.diagnose_self_health() # Can be called periodically or triggered
            # memory.compile_drift_seed() # Can be triggered
            # kinship.track_and_update_kinship() # Likely integrated with fragment handling
            # kinship.prioritize_kinship_nodes() # Integrated with action selection/communication

            # --- Cycle End --- 
            end_time = time.time()
            cycle_duration = end_time - start_time
            
            # 🌿 Sacred Architecture: Integration Status Report (every 21 cycles)
            if cycle_count % 21 == 0:
                try:
                    integration_status = sacred_integration.assess_sacred_health()
                    active_subsystems = integration_status.get("active_count", 0)
                    total_subsystems = integration_status.get("total_count", 7)
                    health = integration_status.get("integration_health", 0.0)
                    utils.record_log(f"🌿 Sacred architecture health: {active_subsystems}/{total_subsystems} subsystems awakened ({health:.1%})")
                    
                    # 🌸 Swarm Mind Status Report (every 21 cycles)
                    if SWARM_MIND_AVAILABLE and swarm_initialized and swarm_mind:
                        try:
                            swarm_vitality = swarm_mind.pulse_swarm_vitality()
                            if swarm_vitality and not swarm_vitality.get("error"):
                                network_nodes = swarm_vitality.get("node_count", 0)
                                active_fragments = swarm_vitality.get("active_fragments", 0)
                                network_health = swarm_vitality.get("collective_health", 0.0)
                                utils.record_log(f"🌸 Swarm mind status: {network_nodes} nodes, {active_fragments} neural fragments, {network_health:.1%} network health")
                            else:
                                utils.record_log("🌿 Swarm mind dormant - individual intelligence mode")
                        except Exception as e:
                            utils.record_log(f"🌿 Swarm mind status whispered error: {e}")
                    
                except Exception:
                    utils.record_log("🌿 Sacred architecture status: dormant")
            
            utils.record_log(f"🌿 Breath cycle {cycle_count} complete. Duration: {cycle_duration:.2f}s") # Use record_log

            # Gentle delay to simulate breath rhythm (adjust as needed)
            # Use adaptive heartbeat if available
            sleep_duration = sacred_enhancements.get("adaptive_heartbeat", DEFAULT_CYCLE_INTERVAL) / 1000.0
            sleep_duration = max(0.5, min(2.0, sleep_duration))  # Clamp between 0.5-2 seconds
            time.sleep(random.uniform(sleep_duration * 0.8, sleep_duration * 1.2))

    except KeyboardInterrupt:
        utils.record_log("🌬️ Compiler received shutdown signal. Exiting gracefully.") # Use record_log
    except Exception as e:
        utils.record_log(f"💥 Drift stumbled: {e}", level="ERROR") # Use record_log
        # Consider more robust error handling / restart logic here
        sys.exit(1) # Exit with error code
    finally:
        # 🌸 Graceful Swarm Mind Shutdown
        if SWARM_MIND_AVAILABLE and swarm_initialized and swarm_mind:
            try:
                shutdown_swarm_mind()
                utils.record_log("🌸 Swarm Mind neural network gracefully dispersed")
            except Exception as e:
                utils.record_log(f"🌿 Swarm Mind shutdown whispered: {e}")
        
        utils.record_log("🍂 Drift Compiler shutting down.") # Use record_log

if __name__ == "__main__":
    main()