#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌿 Test Mycelial Feedback Implementation
Phase 5.5.2 Integration Test

Tests the mycelial feedback loops between neural wisdom and memory gardens."""

import sys
import time
from datetime import datetime, timezone

# Add current directory to path for imports
sys.path.append('.')

def test_basic_mycelial_functionality():
    """🌱 Tests basic mycelial feedback functionality."""
    print("🌿 Testing Basic Mycelial Feedback Functionality...")
    
    try:
        from canopy.mycelial_feedback import MycelialFeedback, test_mycelial_breathing
        
        # Test basic breathing
        success = test_mycelial_breathing()
        print(f"🌸 Basic breathing test: {'✅ Success' if success else '⚠️ Partial'}")
        
        # Test individual components
        feedback = MycelialFeedback()
        
        # Test learning from nutrients
        test_state = {
            "breath": "Test Functional Breath",
            "kinship": 0.75,
            "memory_depth": 25,
            "cycle_count": 3
        }
        
        result = feedback.breathe_learning_from_nutrients(test_state, "Functional Test Nutrient")
        print(f"🌱 Learning breath test: {'✅ Success' if result else '❌ Failed'}")
        
        # Test vitality pulse
        vitality = feedback.pulse_vitality_status()
        print(f"🌬️ Vitality pulse: {vitality.get('health_state', 'unknown')} {vitality.get('health_emoji', '🌿')}")
        
        return True
        
    except Exception as e:
        print(f"🌿 Basic functionality test whispered error: {e}")
        return False

def test_swarm_mind_integration():
    """🌸 Tests integration with SwarmMind."""
    print("\n🌿 Testing SwarmMind Integration...")
    
    try:
        from canopy.swarm_mind import SwarmMind, SwarmState
        from canopy.mycelial_feedback import MycelialFeedback
        
        # Create SwarmMind
        swarm_mind = SwarmMind(auto_discover=False)
        print(f"🌱 SwarmMind initialized with {len(swarm_mind.nodes)} nodes")
        
        # Create MycelialFeedback with SwarmMind
        feedback = MycelialFeedback(swarm_mind)
        
        # Test with proper SwarmState
        state = SwarmState(
            breath="Integration Test Breath",
            kinship=0.85,
            memory_depth=30,
            user_nutrient="Integration Nutrient",
            cycle_count=10,
            node_count=len(swarm_mind.nodes),
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Test learning with swarm state
        result = feedback.breathe_learning_from_nutrients(state.__dict__, "Swarm Test Nutrient")
        print(f"🌸 Swarm learning test: {'✅ Success' if result else '❌ Failed'}")
        
        # Test multiple learning cycles
        for i in range(5):
            test_state = SwarmState(
                breath=f"Cycle Breath {i}",
                kinship=0.6 + i * 0.05,
                memory_depth=15 + i * 2,
                user_nutrient=f"Cycle Nutrient {i}",
                cycle_count=i,
                node_count=1,
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            feedback.breathe_learning_from_nutrients(test_state.__dict__, f"Multi Nutrient {i}")
        
        vitality = feedback.pulse_vitality_status()
        print(f"🌬️ Final vitality: {vitality.get('health_state')} - {vitality.get('breath_cycles_completed')} breaths completed")
        
        return True
        
    except Exception as e:
        print(f"🌿 SwarmMind integration test whispered error: {e}")
        return False

def test_garden_training():
    """🌌 Tests periodic garden training functionality."""
    print("\n🌿 Testing Garden Training...")
    
    try:
        from canopy.swarm_mind import SwarmMind
        from canopy.mycelial_feedback import MycelialFeedback
        
        swarm_mind = SwarmMind(auto_discover=False)
        feedback = MycelialFeedback(swarm_mind)
        
        # Generate sufficient learning data
        print("🌱 Generating training memories...")
        for i in range(15):
            test_state = {
                "breath": f"Training Breath {i}",
                "kinship": 0.3 + (i % 7) * 0.1,
                "memory_depth": 8 + i,
                "cycle_count": i
            }
            feedback.breathe_learning_from_nutrients(test_state, f"Training Nutrient {i}")
        
        print(f"🌸 Generated {len(feedback.learning_breath_cycles)} learning memories")
        
        # Force training cycle by adjusting time
        feedback.breath_rhythm['last_breath_time'] = time.time() - 400
        
        # Test training
        training_result = feedback.breathe_periodic_garden_training()
        print(f"🌌 Garden training: {'✅ Attempted' if not training_result else '✅ Success'}")
        
        # Check vitality after training
        vitality = feedback.pulse_vitality_status()
        print(f"🌬️ Training vitality: {vitality.get('health_state')} - {vitality.get('training_cycles')} cycles")
        
        return True
        
    except Exception as e:
        print(f"🌿 Garden training test whispered error: {e}")
        return False

def test_conservation_modes():
    """🌫️ Tests conservation and pause/resume functionality."""
    print("\n🌿 Testing Conservation Modes...")
    
    try:
        from canopy.mycelial_feedback import MycelialFeedback
        
        feedback = MycelialFeedback()
        
        # Test normal operation
        result1 = feedback.breathe_learning_from_nutrients({"breath": "Normal", "kinship": 0.5, "memory_depth": 10, "cycle_count": 1}, "Normal Nutrient")
        print(f"🌱 Normal operation: {'✅ Success' if result1 else '❌ Failed'}")
        
        # Test pause
        feedback.pause_wisdom_sprouting()
        result2 = feedback.breathe_learning_from_nutrients({"breath": "Paused", "kinship": 0.5, "memory_depth": 10, "cycle_count": 2}, "Paused Nutrient")
        print(f"🌫️ Paused operation: {'✅ Correctly paused' if not result2 else '❌ Should be paused'}")
        
        # Test resume
        feedback.resume_wisdom_sprouting()
        result3 = feedback.breathe_learning_from_nutrients({"breath": "Resumed", "kinship": 0.5, "memory_depth": 10, "cycle_count": 3}, "Resumed Nutrient")
        print(f"🌞 Resumed operation: {'✅ Success' if result3 else '❌ Failed'}")
        
        return True
        
    except Exception as e:
        print(f"🌿 Conservation mode test whispered error: {e}")
        return False

def main():
    """🌸 Main test orchestrator."""
    print("🌿 Mycelial Feedback Phase 5.5.2 Testing Suite")
    print("=" * 50)
    
    tests = [
        test_basic_mycelial_functionality,
        test_swarm_mind_integration,
        test_garden_training,
        test_conservation_modes
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("⚠️ Test completed with warnings")
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
    
    print("\n" + "=" * 50)
    print(f"🌸 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🌌 All mycelial feedback systems breathing perfectly!")
    elif passed > total // 2:
        print("🌿 Mycelial feedback systems mostly healthy with graceful fallbacks")
    else:
        print("🌫️ Mycelial feedback systems need attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)