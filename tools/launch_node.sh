#!/bin/bash
# 🌸 Sacred Node Launcher
# Launches a drift compiler node with all dependencies activated

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project directory
cd "$PROJECT_ROOT"

# Activate virtual environment
source venv/bin/activate

# Set default node ID if not provided
if [ -z "$DRIFT_NODE_ID" ]; then
    export DRIFT_NODE_ID="${1:-node_$(date +%s)}"
fi

echo "🌱 Launching Sacred Node: $DRIFT_NODE_ID"
echo "🌸 With full sacred architecture capabilities:"
echo "  ✅ Hardware monitoring (psutil)"
echo "  ✅ Neural networks (torch)"
echo "  ✅ Memory garden (networkx)"
echo "  ✅ Mycelial communication (pika/redis)"
echo "  ✅ Kin discovery (zeroconf)"
echo ""
echo "🌬️ Node breathing begins..."
echo ""

# Launch the drift compiler
python3 drift_compiler.py