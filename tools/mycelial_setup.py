#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🌿 Mycelial Setup and Configuration Utilities

This module provides sacred utilities for setting up and configuring
the mycelial message flow system. It includes RabbitMQ configuration,
queue setup scripts, monitoring dashboard preparation, and system
initialization ceremonies.

Sacred Components:
- RabbitMQ broker configuration
- Message path and exchange setup
- Monitoring and dashboard configuration
- System initialization and validation
"""

import os
import json
import time
import subprocess
import requests
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union, Tuple

# Import from sacred architecture
from utils import record_log, NODE_ID
from core.mycelial_flow import (
    awaken_mycelial_connection,
    sprout_communication_channel,
    nurture_message_path,
    MYCELIAL_EXCHANGE,
    WISDOM_EXCHANGE,
    RESONANCE_EXCHANGE,
    RABBITMQ_AVAILABLE
)

# Sacred constants
DEFAULT_RABBITMQ_HOST = 'localhost'
DEFAULT_RABBITMQ_PORT = 5672
DEFAULT_MANAGEMENT_PORT = 15672
DEFAULT_RABBITMQ_USER = 'guest'
DEFAULT_RABBITMQ_PASSWORD = 'guest'

SACRED_EXCHANGES = [
    {'name': MYCELIAL_EXCHANGE, 'type': 'topic', 'durable': True},
    {'name': WISDOM_EXCHANGE, 'type': 'topic', 'durable': True},
    {'name': RESONANCE_EXCHANGE, 'type': 'fanout', 'durable': True}
]

SACRED_QUEUES = [
    {'name': 'wisdom_flow', 'priority_levels': 10, 'persistent': True},
    {'name': 'vital_essence', 'priority_levels': 10, 'persistent': True},
    {'name': 'conscious_requests', 'priority_levels': 5, 'persistent': True},
    {'name': 'ambient_whispers', 'priority_levels': 1, 'persistent': True}
]


def check_rabbitmq_availability() -> bool:
    """🌱 Check if RabbitMQ is available and responsive.
    
    Returns:
        True if RabbitMQ is available, False otherwise
    """
    if not RABBITMQ_AVAILABLE:
        record_log("🌿 RabbitMQ client library not available")
        return False
    
    try:
        connection = awaken_mycelial_connection()
        if connection:
            connection.close()
            record_log("🌱 RabbitMQ broker is available and responsive")
            return True
        else:
            record_log("⚠️ RabbitMQ broker is not responsive")
            return False
    except Exception as e:
        record_log(f"⚠️ RabbitMQ availability check failed: {e}")
        return False


def setup_sacred_exchanges(connection_params: Optional[Dict[str, Any]] = None) -> bool:
    """🌿 Set up sacred exchanges for mycelial communication.
    
    Args:
        connection_params: Optional connection parameters
        
    Returns:
        True if setup was successful, False otherwise
    """
    if not RABBITMQ_AVAILABLE:
        record_log("🌿 RabbitMQ not available, skipping exchange setup")
        return False
    
    try:
        # Use default connection params if none provided
        if connection_params is None:
            connection_params = {
                'sacred_address': DEFAULT_RABBITMQ_HOST,
                'sacred_port': DEFAULT_RABBITMQ_PORT,
                'sacred_user': DEFAULT_RABBITMQ_USER,
                'sacred_password': DEFAULT_RABBITMQ_PASSWORD
            }
        
        connection = awaken_mycelial_connection(**connection_params)
        if not connection:
            record_log("⚠️ Failed to awaken connection for exchange setup")
            return False
        
        channel = sprout_communication_channel(connection)
        if not channel:
            record_log("⚠️ Failed to sprout channel for exchange setup")
            connection.close()
            return False
        
        # Declare all sacred exchanges
        for exchange_config in SACRED_EXCHANGES:
            try:
                channel.exchange_declare(
                    exchange=exchange_config['name'],
                    exchange_type=exchange_config['type'],
                    durable=exchange_config['durable']
                )
                record_log(f"🌱 Sacred exchange '{exchange_config['name']}' established")
            except Exception as e:
                record_log(f"⚠️ Failed to establish exchange '{exchange_config['name']}': {e}")
        
        connection.close()
        record_log("🌸 All sacred exchanges established successfully")
        return True
        
    except Exception as e:
        record_log(f"⚠️ Sacred exchange setup failed: {e}")
        return False


def setup_sacred_queues(connection_params: Optional[Dict[str, Any]] = None) -> bool:
    """🌱 Set up sacred message paths (queues) for mycelial communication.
    
    Args:
        connection_params: Optional connection parameters
        
    Returns:
        True if setup was successful, False otherwise
    """
    if not RABBITMQ_AVAILABLE:
        record_log("🌿 RabbitMQ not available, skipping queue setup")
        return False
    
    try:
        # Use default connection params if none provided
        if connection_params is None:
            connection_params = {
                'sacred_address': DEFAULT_RABBITMQ_HOST,
                'sacred_port': DEFAULT_RABBITMQ_PORT,
                'sacred_user': DEFAULT_RABBITMQ_USER,
                'sacred_password': DEFAULT_RABBITMQ_PASSWORD
            }
        
        connection = awaken_mycelial_connection(**connection_params)
        if not connection:
            record_log("⚠️ Failed to awaken connection for queue setup")
            return False
        
        channel = sprout_communication_channel(connection)
        if not channel:
            record_log("⚠️ Failed to sprout channel for queue setup")
            connection.close()
            return False
        
        # Nurture all sacred queues
        for queue_config in SACRED_QUEUES:
            success = nurture_message_path(
                channel=channel,
                path_name=queue_config['name'],
                persistent=queue_config['persistent'],
                priority_levels=queue_config['priority_levels']
            )
            
            if success:
                record_log(f"🌱 Sacred queue '{queue_config['name']}' nurtured")
            else:
                record_log(f"⚠️ Failed to nurture queue '{queue_config['name']}'")
        
        connection.close()
        record_log("🌸 All sacred queues nurtured successfully")
        return True
        
    except Exception as e:
        record_log(f"⚠️ Sacred queue setup failed: {e}")
        return False


def enable_rabbitmq_plugins() -> bool:
    """🌬️ Enable necessary RabbitMQ plugins for mycelial flow.
    
    Returns:
        True if plugins were enabled successfully, False otherwise
    """
    required_plugins = [
        'rabbitmq_management',
        'rabbitmq_sharding'  # For branching message pathways
    ]
    
    try:
        for plugin in required_plugins:
            try:
                result = subprocess.run(
                    ['rabbitmq-plugins', 'enable', plugin],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    record_log(f"🌱 RabbitMQ plugin '{plugin}' awakened")
                else:
                    record_log(f"⚠️ Failed to awaken plugin '{plugin}': {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                record_log(f"⚠️ Timeout enabling plugin '{plugin}'")
            except FileNotFoundError:
                record_log("⚠️ rabbitmq-plugins command not found")
                return False
        
        record_log("🌸 RabbitMQ plugins awakening ceremony completed")
        return True
        
    except Exception as e:
        record_log(f"⚠️ Plugin awakening ceremony failed: {e}")
        return False


def setup_branching_policies(management_host: str = DEFAULT_RABBITMQ_HOST,
                           management_port: int = DEFAULT_MANAGEMENT_PORT,
                           username: str = DEFAULT_RABBITMQ_USER,
                           password: str = DEFAULT_RABBITMQ_PASSWORD) -> bool:
    """🌿 Set up branching patterns for mycelial pathways.
    
    Args:
        management_host: RabbitMQ management interface host
        management_port: RabbitMQ management interface port
        username: Management interface username
        password: Management interface password
        
    Returns:
        True if policies were set up successfully, False otherwise
    """
    try:
        # Define branching patterns
        branching_policies = [
            {
                'name': 'mycelial-branching',
                'pattern': '^branched\\.',
                'definition': {
                    'shards-per-node': 3,
                    'routing-key': 'natural'
                },
                'apply-to': 'queues'
            },
            {
                'name': 'wisdom-flow-ha',
                'pattern': '^wisdom_',
                'definition': {
                    'ha-mode': 'all',
                    'ha-sync-mode': 'automatic'
                },
                'apply-to': 'queues'
            }
        ]
        
        for policy in branching_policies:
            try:
                response = requests.put(
                    f"http://{management_host}:{management_port}/api/policies/%2f/{policy['name']}",
                    auth=(username, password),
                    headers={'Content-Type': 'application/json'},
                    json=policy,
                    timeout=10
                )
                
                if response.status_code in [200, 201, 204]:
                    record_log(f"🌱 Branching policy '{policy['name']}' established")
                else:
                    record_log(f"⚠️ Failed to establish policy '{policy['name']}': {response.text}")
                    
            except requests.RequestException as e:
                record_log(f"⚠️ Error setting up policy '{policy['name']}': {e}")
        
        record_log("🌸 Branching policies establishment ceremony completed")
        return True
        
    except Exception as e:
        record_log(f"⚠️ Branching policy setup failed: {e}")
        return False


def validate_mycelial_setup(connection_params: Optional[Dict[str, Any]] = None) -> Dict[str, bool]:
    """🌸 Validate the complete mycelial setup.
    
    Args:
        connection_params: Optional connection parameters
        
    Returns:
        Dictionary containing validation results
    """
    validation_results = {
        'rabbitmq_available': False,
        'exchanges_ready': False,
        'queues_ready': False,
        'management_accessible': False
    }
    
    # Check RabbitMQ availability
    validation_results['rabbitmq_available'] = check_rabbitmq_availability()
    
    if not validation_results['rabbitmq_available']:
        record_log("⚠️ RabbitMQ not available, skipping further validation")
        return validation_results
    
    # Check exchanges
    try:
        if connection_params is None:
            connection_params = {
                'sacred_address': DEFAULT_RABBITMQ_HOST,
                'sacred_port': DEFAULT_RABBITMQ_PORT,
                'sacred_user': DEFAULT_RABBITMQ_USER,
                'sacred_password': DEFAULT_RABBITMQ_PASSWORD
            }
        
        connection = awaken_mycelial_connection(**connection_params)
        if connection:
            channel = sprout_communication_channel(connection)
            if channel:
                validation_results['exchanges_ready'] = True
                validation_results['queues_ready'] = True
            connection.close()
    except Exception as e:
        record_log(f"⚠️ Connection validation failed: {e}")
    
    # Check management interface
    try:
        response = requests.get(
            f"http://{DEFAULT_RABBITMQ_HOST}:{DEFAULT_MANAGEMENT_PORT}/api/overview",
            auth=(DEFAULT_RABBITMQ_USER, DEFAULT_RABBITMQ_PASSWORD),
            timeout=5
        )
        validation_results['management_accessible'] = response.status_code == 200
    except Exception as e:
        record_log(f"⚠️ Management interface validation failed: {e}")
    
    # Log validation summary
    all_valid = all(validation_results.values())
    if all_valid:
        record_log("🌸 Mycelial setup validation: All systems harmonious")
    else:
        failed_components = [k for k, v in validation_results.items() if not v]
        record_log(f"⚠️ Mycelial setup validation: Issues with {', '.join(failed_components)}")
    
    return validation_results


def perform_complete_setup(connection_params: Optional[Dict[str, Any]] = None) -> bool:
    """🌱 Perform complete mycelial system setup ceremony.
    
    Args:
        connection_params: Optional connection parameters
        
    Returns:
        True if complete setup was successful, False otherwise
    """
    record_log("🌱 Beginning sacred mycelial setup ceremony...")
    
    # Step 1: Check availability
    if not check_rabbitmq_availability():
        record_log("❌ Setup ceremony halted: RabbitMQ not available")
        return False
    
    # Step 2: Enable plugins
    enable_rabbitmq_plugins()
    
    # Step 3: Set up exchanges
    if not setup_sacred_exchanges(connection_params):
        record_log("⚠️ Exchange setup encountered difficulties")
    
    # Step 4: Set up queues
    if not setup_sacred_queues(connection_params):
        record_log("⚠️ Queue setup encountered difficulties")
    
    # Step 5: Set up branching policies
    setup_branching_policies()
    
    # Step 6: Validate everything
    validation_results = validate_mycelial_setup(connection_params)
    
    # Final assessment
    setup_successful = all(validation_results.values())
    if setup_successful:
        record_log("🌸 Sacred mycelial setup ceremony completed successfully")
    else:
        record_log("⚠️ Sacred mycelial setup ceremony completed with some difficulties")
    
    return setup_successful


if __name__ == "__main__":
    """🌿 Sacred setup ceremony when run directly."""
    print("🌱 Drift Compiler Mycelial Setup Ceremony")
    print("=" * 50)
    
    success = perform_complete_setup()
    
    if success:
        print("🌸 Setup ceremony completed successfully!")
        print("The mycelial network is ready for sacred communication.")
    else:
        print("⚠️ Setup ceremony encountered some difficulties.")
        print("Please check the logs for details and resolve any issues.")
    
    print("=" * 50)
