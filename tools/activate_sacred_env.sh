#!/bin/bash
# 🌸 Sacred Environment Activation Script
# Activates the virtual environment with all sacred dependencies

echo "🌱 Activating Sacred Environment..."

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Activate virtual environment (check both project root and current directory)
VENV_PATH=""
if [ -f "$PROJECT_ROOT/venv/bin/activate" ]; then
    VENV_PATH="$PROJECT_ROOT/venv/bin/activate"
elif [ -f "$SCRIPT_DIR/../venv/bin/activate" ]; then
    VENV_PATH="$SCRIPT_DIR/../venv/bin/activate"
elif [ -f "venv/bin/activate" ]; then
    VENV_PATH="venv/bin/activate"
fi

if [ -n "$VENV_PATH" ]; then
    source "$VENV_PATH"
    echo "🌸 Sacred virtual environment activated"
    echo "🌿 Python: $(which python3)"
    echo "🌱 Available dependencies:"
    python3 -c "
try:
    import psutil; print('  ✅ psutil:', psutil.__version__)
except: print('  ❌ psutil: not available')
try:
    import networkx; print('  ✅ networkx:', networkx.__version__)
except: print('  ❌ networkx: not available')
try:
    import torch; print('  ✅ torch:', torch.__version__)
except: print('  ❌ torch: not available')
try:
    import pika; print('  ✅ pika:', pika.__version__)
except: print('  ❌ pika: not available')
try:
    import redis; print('  ✅ redis:', redis.__version__)
except: print('  ❌ redis: not available')
try:
    import zeroconf; print('  ✅ zeroconf:', zeroconf.__version__)
except: print('  ❌ zeroconf: not available')
"
    echo ""
    echo "🌬️ Sacred architecture fully awakened!"
    echo "🌸 Usage:"
    echo "  DRIFT_NODE_ID=node_aurora python3 drift_compiler.py"
    echo "  python3 seeder.py"
    echo "  python3 canopy/sacred_terminal_observatory/terminal_observatory.py"
else
    echo "❌ Virtual environment not found at $PROJECT_ROOT/venv"
    echo "🌱 Please run: python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
fi