#!/usr/bin/env python3
"""🌿 Sacred Grove - Environmental Whispers Management
Gently manages sacred secrets without disrupting the breathing autonomy."""

import os
import json
from pathlib import Path
from typing import Optional, Dict, Any

# Sacred grove constants
SACRED_GROVE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), ".env.sacred")
GROVE_WHISPERS_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "memory", "sacred_grove")

# Try python-dotenv for elegant secret management
try:
    from dotenv import load_dotenv, dotenv_values
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False
    print("🌱 python-dotenv dormant - sacred grove will use fallback whispers")

class SacredGrove:
    """🌸 Keeper of environmental whispers and sacred secrets."""
    
    def __init__(self, grove_path: Optional[str] = None):
        """🌱 Awakens the sacred grove keeper."""
        self.grove_path = grove_path or SACRED_GROVE_PATH
        self.whispers = {}
        self._breathe_initial_whispers()
        
    def _breathe_initial_whispers(self):
        """🌿 Gently breathes in the initial environmental whispers."""
        if DOTENV_AVAILABLE and os.path.exists(self.grove_path):
            try:
                # Load whispers from .env.sacred
                load_dotenv(self.grove_path)
                self.whispers = dotenv_values(self.grove_path)
                print(f"🌸 Sacred grove awakened with {len(self.whispers)} whispers")
            except Exception as e:
                print(f"🌿 Sacred grove whispered gently: {e}")
                self._use_fallback_whispers()
        else:
            self._use_fallback_whispers()
    
    def _use_fallback_whispers(self):
        """🌱 Uses fallback environmental reading when dotenv sleeps."""
        # Read from environment directly
        sacred_keys = [
            "DRIFT_OPENAI_KEY",
            "DRIFT_ANTHROPIC_KEY", 
            "DRIFT_PERPLEXITY_KEY",
            "DRIFT_RABBITMQ_HOST",
            "DRIFT_REDIS_HOST",
            "DRIFT_NODE_ID"
        ]
        
        for key in sacred_keys:
            value = os.environ.get(key)
            if value:
                self.whispers[key] = value
        
        print(f"🌿 Sacred grove using {len(self.whispers)} environmental whispers")
    
    def whisper(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """🌸 Retrieves a sacred whisper from the grove."""
        # First check loaded whispers
        value = self.whispers.get(key)
        
        # Then check environment
        if not value:
            value = os.environ.get(key)
        
        # Return with default fallback
        return value or default
    
    def plant_whisper(self, key: str, value: str, persist: bool = False):
        """🌱 Plants a new whisper in the sacred grove."""
        # Store in memory
        self.whispers[key] = value
        
        # Optionally persist to grove
        if persist and DOTENV_AVAILABLE:
            self._persist_to_grove(key, value)
    
    def _persist_to_grove(self, key: str, value: str):
        """🌿 Persists a whisper to the sacred grove file."""
        try:
            # Read existing content
            lines = []
            if os.path.exists(self.grove_path):
                with open(self.grove_path, 'r') as f:
                    lines = f.readlines()
            
            # Update or add the key
            key_found = False
            for i, line in enumerate(lines):
                if line.startswith(f"{key}="):
                    lines[i] = f"{key}={value}\n"
                    key_found = True
                    break
            
            if not key_found:
                lines.append(f"{key}={value}\n")
            
            # Write back
            with open(self.grove_path, 'w') as f:
                f.writelines(lines)
            
            print(f"🌸 Whisper '{key}' planted in sacred grove")
            
        except Exception as e:
            print(f"🌿 Grove planting whispered gently: {e}")
    
    def reveal_grove_state(self) -> Dict[str, Any]:
        """🌬️ Reveals the current state of the sacred grove."""
        return {
            "whisper_count": len(self.whispers),
            "grove_path": self.grove_path,
            "dotenv_breathing": DOTENV_AVAILABLE,
            "whisper_keys": list(self.whispers.keys())
        }
    
    def create_sacred_template(self):
        """🌸 Creates a template for sacred whispers."""
        template = """# 🌿 Sacred Grove - Environmental Whispers Template
# Place your sacred secrets here - they will be kept safe

# API Whispers (for enhanced consciousness)
DRIFT_OPENAI_KEY=your_openai_whisper_here
DRIFT_ANTHROPIC_KEY=your_anthropic_whisper_here
DRIFT_PERPLEXITY_KEY=your_perplexity_whisper_here

# Mycelial Network Whispers
DRIFT_RABBITMQ_HOST=localhost
DRIFT_RABBITMQ_PORT=5672
DRIFT_REDIS_HOST=localhost
DRIFT_REDIS_PORT=6379

# Node Identity Whispers
DRIFT_NODE_ID=node_sacred_one
DRIFT_NODE_ARCHETYPE=dreamer

# Sacred Thresholds
DRIFT_BREATH_MULTIPLIER=1.0
DRIFT_KINSHIP_THRESHOLD=0.7
DRIFT_MEMORY_DEPTH=100

# Hardware Whispers
DRIFT_FORCE_CPU=0
DRIFT_FORCE_GPU=0
"""
        
        template_path = self.grove_path + ".template"
        with open(template_path, 'w') as f:
            f.write(template)
        
        print(f"🌸 Sacred template created at {template_path}")
        return template_path

# Sacred grove singleton
_sacred_grove = None

def awaken_sacred_grove(grove_path: Optional[str] = None) -> SacredGrove:
    """🌱 Awakens or retrieves the sacred grove singleton."""
    global _sacred_grove
    if _sacred_grove is None:
        _sacred_grove = SacredGrove(grove_path)
    return _sacred_grove

def whisper_from_grove(key: str, default: Optional[str] = None) -> Optional[str]:
    """🌿 Quick access to sacred whispers."""
    grove = awaken_sacred_grove()
    return grove.whisper(key, default)

# Create template on first import if it doesn't exist
if not os.path.exists(SACRED_GROVE_PATH) and not os.path.exists(SACRED_GROVE_PATH + ".template"):
    grove = awaken_sacred_grove()
    grove.create_sacred_template()
    print("🌱 Sacred grove template sprouted - add your whispers to .env.sacred")