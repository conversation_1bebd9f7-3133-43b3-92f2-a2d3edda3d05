#!/bin/bash
# 🌸 Sacred Observatory Launcher
# Launches the terminal observatory with all dependencies activated

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project directory
cd "$PROJECT_ROOT"

# Activate virtual environment
source venv/bin/activate

echo "🌙 Launching Sacred Observatory..."
echo "🌸 Enhanced visualization with rich library:"

# Check if rich is available
python3 -c "
try:
    import rich
    print('  ✅ Rich library available - using enhanced observatory')
    obs_type = 'enhanced'
except ImportError:
    print('  🌱 Rich library not available - using simple observatory')
    obs_type = 'simple'
    
print('🌿 Monitoring multi-node sacred architecture...')
print('')
"

# Launch the appropriate observatory
if python3 -c "import rich" 2>/dev/null; then
    echo "🌸 Starting Enhanced Sacred Observatory..."
    python3 canopy/sacred_terminal_observatory/terminal_observatory.py
else
    echo "🌱 Starting Simple Sacred Observatory..."
    python3 canopy/sacred_terminal_observatory/simple_observatory.py
fi