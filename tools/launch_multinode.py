#!/usr/bin/env python3
"""🌸 Multi-Node Launcher
Simple launcher for testing multi-node sacred architecture without disk space issues."""

import os
import sys
import time
import subprocess
import signal
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class MultiNodeLauncher:
    """🌱 Sacred multi-node launcher for testing."""
    
    def __init__(self):
        self.processes = []
        self.node_ids = ["node_aurora", "node_sage", "node_willow"]
        
    def cleanup(self, signum=None, frame=None):
        """🌿 Graceful cleanup of all node processes."""
        print("🌸 Gracefully stopping all nodes...")
        for proc in self.processes:
            try:
                proc.terminate()
                proc.wait(timeout=5)
            except:
                proc.kill()
        print("🌿 All nodes have returned to peaceful sleep")
        sys.exit(0)
        
    def launch_observatory_only(self):
        """🌱 Launch just the observatory for existing heartbeats."""
        print("🌸 Sacred Observatory - Multi-Node Monitor")
        print("=" * 50)
        
        from canopy.sacred_terminal_observatory.simple_observatory import SimpleSacredObservatory
        
        try:
            obs = SimpleSacredObservatory(debug_mode=True)
            obs.breathe_simple_display()
        except KeyboardInterrupt:
            print("\n🌿 Observatory resting peacefully...")
            
    def simulate_nodes(self):
        """🌿 Simulate multiple nodes with in-memory heartbeats (disk-friendly)."""
        print("🌱 Simulating multi-node network (no disk writes)...")
        
        # Create heartbeat directory
        heartbeats_dir = project_root / "memory" / "heartbeats"
        heartbeats_dir.mkdir(exist_ok=True)
        
        import json
        from datetime import datetime, timezone
        
        # Create fresh heartbeat files
        base_time = datetime.now(timezone.utc)
        
        nodes_config = {
            "node_aurora": {
                "health": {"overall": 0.92},
                "memory_count": 45,
                "cycle_count": 120,
                "connections": ["node_sage"],
                "neural_fragments": 8
            },
            "node_sage": {
                "health": {"overall": 0.78}, 
                "memory_count": 67,
                "cycle_count": 115,
                "connections": ["node_aurora", "node_willow"],
                "neural_fragments": 12
            },
            "node_willow": {
                "health": {"overall": 0.65},
                "memory_count": 23,
                "cycle_count": 110,
                "connections": ["node_sage"],
                "neural_fragments": 4
            }
        }
        
        for node_id, config in nodes_config.items():
            heartbeat = {
                "timestamp": base_time.isoformat(),
                "node_id": node_id,
                "type": "heartbeat",
                "version": "1.0",
                **config
            }
            
            heartbeat_file = heartbeats_dir / f"{node_id}.json"
            with open(heartbeat_file, 'w') as f:
                json.dump(heartbeat, f, indent=2)
                
        print(f"🌸 Created {len(nodes_config)} simulated nodes")
        
        # Launch observatory
        self.launch_observatory_only()

def main():
    """🌸 Main launcher function."""
    launcher = MultiNodeLauncher()
    
    # Setup signal handling
    signal.signal(signal.SIGINT, launcher.cleanup)
    signal.signal(signal.SIGTERM, launcher.cleanup)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--simulate":
        launcher.simulate_nodes()
    else:
        print("🌸 Multi-Node Sacred Architecture Launcher")
        print("=" * 50)
        print("🌱 Usage:")
        print("  python3 tools/launch_multinode.py --simulate   # Simulate nodes (disk-friendly)")
        print("  python3 tools/launch_multinode.py             # Show this help")
        print()
        print("🌿 Due to disk space constraints, using simulation mode is recommended")
        print("🌸 The observatory will monitor simulated breathing patterns")

if __name__ == "__main__":
    main()