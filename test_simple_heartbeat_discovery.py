#!/usr/bin/env python3
"""🌸 Test Simple Heartbeat-Based Discovery"""

import os
import json
from datetime import datetime, timezone

def simple_heartbeat_discovery():
    """🌿 Simple discovery by scanning heartbeat files."""
    discovered_nodes = []
    
    heartbeats_dir = "memory/heartbeats"
    if os.path.exists(heartbeats_dir):
        from utils import NODE_ID
        
        for filename in os.listdir(heartbeats_dir):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(heartbeats_dir, filename), 'r') as f:
                        heartbeat = json.load(f)
                    
                    node_id = heartbeat.get("node_id")
                    timestamp_str = heartbeat.get("timestamp", "")
                    
                    # Skip our own heartbeat
                    if node_id == NODE_ID:
                        continue
                    
                    # Check if heartbeat is fresh (within 5 minutes)
                    if timestamp_str and 'T' in timestamp_str:
                        heartbeat_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        age = datetime.now(timezone.utc) - heartbeat_time
                        
                        if age.total_seconds() < 300:  # 5 minutes
                            discovered_nodes.append(node_id)
                            print(f"🌱 Discovered fresh node: {node_id}")
                        else:
                            print(f"🌿 Skipped stale node: {node_id} (age: {int(age.total_seconds()/60)}min)")
                    
                except Exception as e:
                    print(f"⚠️ Error reading {filename}: {e}")
    
    return discovered_nodes

def test_simple_discovery():
    """🌸 Tests simple heartbeat discovery."""
    print("🌸 Testing Simple Heartbeat Discovery...")
    
    discovered = simple_heartbeat_discovery()
    
    print(f"🌿 Discovery found {len(discovered)} nodes: {discovered}")
    
    if len(discovered) > 0:
        print("✅ Simple discovery is working!")
    else:
        print("⚠️ No nodes discovered - they may be stale or not exist")

if __name__ == "__main__":
    test_simple_discovery()