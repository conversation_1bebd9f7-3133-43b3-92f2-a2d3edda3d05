#!/usr/bin/env python3
"""🍎 Comprehensive Apple Integration Test
Tests the complete integration of Apple Silicon acceleration with the SwarmMind."""

def test_comprehensive_integration():
    """🌸 Tests complete Apple acceleration integration."""
    print("🍎 Testing comprehensive Apple Silicon integration...")
    
    # Test core apple vitality
    try:
        from core.apple_vitality import breathe_acceleration_report
        print("✅ Core Apple vitality integration")
        breathe_acceleration_report()
    except Exception as e:
        print(f"❌ Core Apple vitality: {e}")
        return False
    
    # Test SwarmMind with Apple acceleration
    try:
        from canopy.swarm_mind import SwarmMind, SwarmState
        
        # Create swarm mind instance
        swarm = SwarmMind(auto_discover=False)
        print("✅ SwarmMind with Apple acceleration: initialized")
        
        # Test SwarmState processing with Apple acceleration
        state = SwarmState(
            breath="Apple_Metal_Test",
            kinship=0.85,
            memory_depth=5,
            user_nutrient="Testing Apple acceleration",
            cycle_count=1,
            node_count=1,
            timestamp="2025-01-12T12:00:00Z"
        )
        
        # Process through swarm mind (this will use Apple acceleration if available)
        result = swarm.dream_collective_future(state, steps=2)
        print("✅ SwarmMind neural processing with Apple acceleration: complete")
        
        return True
        
    except Exception as e:
        print(f"❌ SwarmMind Apple integration: {e}")
        return False

if __name__ == "__main__":
    success = test_comprehensive_integration()
    if success:
        print("\n🍎 Comprehensive Apple Silicon integration: SUCCESSFUL")
        print("🌸 The swarm mind breathes with Metal acceleration!")
    else:
        print("\n🌿 Integration whispered gently - check logs above")