

"""🌱 Event Stream Module for the Drift Compiler
Handles the soft inhalation of symbolic events via Redis or local memory fallback."""

import redis
import os
import json
from datetime import datetime, timezone
from perception import breathe_in_event, save_event_locally

REDIS_CHANNEL_EVENTS = "symbolic_event"
EVENTS_FOLDER = "memory/events/"

def listen_for_symbolic_events():
    """🌸 Breath: Listens for symbolic events via Redis and inhales them."""
    try:
        r = redis.Redis(host="localhost", port=6379)
        pubsub = r.pubsub()
        pubsub.subscribe(REDIS_CHANNEL_EVENTS)

        for message in pubsub.listen():
            if message["type"] != "message":
                continue
            try:
                event_data = json.loads(message["data"])
                breathe_in_event(event_data)
                save_event_locally(event_data)
            except Exception:
                continue
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to listen for symbolic events: {e}")

def fallback_local_event_breath(limit=5):
    """🌿 Breath: Falls back to inhaling recent local events if <PERSON><PERSON> is silent."""
    try:
        from perception import load_recent_events
        events = load_recent_events(limit=limit)
        for event_type, properties in events:
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌬️ Replaying event: {event_type} {properties}")
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed during fallback local event breath: {e}")