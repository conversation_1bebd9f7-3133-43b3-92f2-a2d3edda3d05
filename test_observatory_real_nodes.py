#!/usr/bin/env python3
"""🌸 Test Observatory with Real Nodes"""

import os
import json
from datetime import datetime, timezone
from canopy.sacred_terminal_observatory.terminal_observatory import SacredTerminalObservatory

def test_real_node_detection():
    """🌿 Tests if observatory detects real nodes from heartbeat files."""
    print("🌱 Testing Real Node Detection...")
    
    # Check heartbeat files
    heartbeats_dir = "memory/heartbeats"
    if os.path.exists(heartbeats_dir):
        files = [f for f in os.listdir(heartbeats_dir) if f.endswith('.json')]
        print(f"🌸 Found {len(files)} heartbeat files:")
        
        for file in files:
            file_path = os.path.join(heartbeats_dir, file)
            try:
                with open(file_path, 'r') as f:
                    heartbeat = json.load(f)
                
                timestamp = heartbeat.get("timestamp", "")
                node_id = heartbeat.get("node_id", "unknown")
                
                # Calculate age
                if 'T' in timestamp:
                    heartbeat_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    age = datetime.now(timezone.utc) - heartbeat_time
                    age_minutes = int(age.total_seconds() / 60)
                    
                    healing_performed = heartbeat.get("health", {}).get("healing_performed", False)
                    recent_healing = heartbeat.get("health", {}).get("recent_healing", [])
                    
                    print(f"  🌿 {node_id}: age {age_minutes}min, healing: {healing_performed}, actions: {recent_healing}")
                    
            except Exception as e:
                print(f"  ⚠️ Error reading {file}: {e}")
    
    # Test observatory detection
    print("\n🌸 Testing Observatory Detection...")
    
    try:
        observatory = SacredTerminalObservatory()
        observatory._sense_network_state()
        
        print(f"🌿 Observatory detected {len(observatory.nodes)} nodes:")
        for node_id, node_data in observatory.nodes.items():
            healing = node_data.get('healing_performed', False)
            vitality = node_data.get('vitality', 0)
            
            print(f"  🌱 {node_id}: vitality {vitality:.2f}, healing: {healing}")
            
    except ImportError:
        print("🌿 Rich not available, using simple observatory...")
        from canopy.sacred_terminal_observatory.simple_observatory import SimpleSacredObservatory
        
        simple_obs = SimpleSacredObservatory()
        simple_obs._sense_network_state()
        
        print(f"🌿 Simple observatory detected {len(simple_obs.nodes)} nodes:")
        for node_id in simple_obs.nodes:
            print(f"  🌱 {node_id}")

if __name__ == "__main__":
    test_real_node_detection()