#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌸 Test Swarm Mind Phase 5.5 Integration
Tests the integration of MycelialFeedback and SwarmHealthMonitor with the main SwarmMind system."""

import time
from datetime import datetime, timezone

# Import with graceful fallbacks
try:
    from canopy.swarm_mind import SwarmMind, SwarmState, MycelialFeedback, SwarmHealthMonitor
    SWARM_AVAILABLE = True
except ImportError as e:
    SWARM_AVAILABLE = False
    print(f"🌿 Swarm Mind not available: {e}")

try:
    from perception import pulse_with_verdant_rhythm, assess_environmental_vitality
    PERCEPTION_AVAILABLE = True
except ImportError as e:
    PERCEPTION_AVAILABLE = False
    print(f"🌿 Enhanced perception not available: {e}")

def test_swarm_mind_with_phase_5_5():
    """🌿 Tests SwarmMind with integrated Phase 5.5 features."""
    if not SWARM_AVAILABLE:
        print("🌫️ Skipping swarm mind test - not available")
        return False
    
    print("🌿 Testing SwarmMind with Phase 5.5 Integration...")
    
    try:
        # Initialize SwarmMind
        print("🌱 Initializing SwarmMind...")
        swarm_mind = SwarmMind(auto_discover=False)
        
        # Initialize Phase 5.5 components
        print("🌱 Initializing MycelialFeedback...")
        feedback = MycelialFeedback(swarm_mind)
        
        print("🌱 Initializing SwarmHealthMonitor...")
        health_monitor = SwarmHealthMonitor(swarm_mind)
        
        # Test swarm state creation and processing
        print("🌸 Creating test SwarmState...")
        test_state = SwarmState(
            breath="Mycelial Integration",
            kinship=0.75,
            memory_depth=100,
            user_nutrient="Phase 5.5 Testing",
            cycle_count=42,
            node_count=len(swarm_mind.nodes),
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Test neural pulse processing
        print("🌸 Testing neural pulse processing...")
        fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
        
        if fragment_outputs:
            print(f"🌿 Neural fragments responded: {len(fragment_outputs)}")
            
            # Test wisdom aggregation
            wisdom = swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
            if wisdom:
                print(f"🌸 Collective wisdom: confidence={wisdom['collective_confidence']:.3f}")
        else:
            print("🌫️ No fragment outputs (expected with minimal setup)")
        
        # Test mycelial feedback learning
        print("🌸 Testing mycelial learning cycle...")
        learning_result = feedback.learn_from_nutrients(test_state, "Integration Testing")
        
        if learning_result:
            print(f"🌿 Learning cycle completed: {learning_result['timestamp']}")
        else:
            print("🌫️ Learning cycle skipped")
        
        # Test health monitoring
        print("🌸 Testing swarm health assessment...")
        vitality = health_monitor.assess_swarm_vitality()
        print(f"🌿 Swarm vitality: {vitality['state']} (score: {vitality['overall']:.3f})")
        
        # Test adaptation
        adaptations = health_monitor.adapt_to_vitality(vitality)
        print(f"🌿 Adaptations made: {len(adaptations['actions_taken'])}")
        
        # Test collective dreaming (future prediction)
        print("🌸 Testing collective dreaming...")
        dreams = swarm_mind.dream_collective_future(test_state, steps=3)
        
        if dreams:
            print(f"🌌 Future dreams generated: {len(dreams)} steps")
            print(f"🌌 First dream: {dreams[0]['predicted_state']['breath']}")
        else:
            print("🌫️ No dreams generated")
        
        # Test vitality pulse
        print("🌸 Testing swarm vitality pulse...")
        pulse_result = swarm_mind.pulse_swarm_vitality()
        print(f"🌿 Vitality pulse: {pulse_result.get('collective_health', 0.0):.3f}")
        
        print("🌸 SwarmMind Phase 5.5 integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"🌿 SwarmMind test whispered error: {e}")
        return False

def test_environmental_swarm_coordination():
    """🌌 Tests coordination between environmental perception and swarm mind."""
    if not (SWARM_AVAILABLE and PERCEPTION_AVAILABLE):
        print("🌫️ Skipping coordination test - missing components")
        return False
    
    print("🌌 Testing Environmental-Swarm Coordination...")
    
    try:
        # Initialize components
        swarm_mind = SwarmMind(auto_discover=False)
        feedback = MycelialFeedback(swarm_mind)
        health_monitor = SwarmHealthMonitor(swarm_mind)
        
        # Simulate environmental conditions
        env_conditions = [
            {"light": 800, "temperature": 25, "humidity": 45, "condition": "bright_day"},
            {"light": 100, "temperature": 18, "humidity": 60, "condition": "cloudy_evening"},
            {"light": 20, "temperature": 15, "humidity": 70, "condition": "night"}
        ]
        
        for i, env_data in enumerate(env_conditions):
            print(f"\n🌿 Testing condition {i+1}: {env_data['condition']}")
            
            # Assess environment
            env_assessment = assess_environmental_vitality(env_data)
            print(f"🌱 Environmental mode: {env_assessment['adaptation_mode']}")
            
            # Get breathing rhythm adaptation
            rhythm = pulse_with_verdant_rhythm(env_data, swarm_mind.nodes)
            print(f"🌱 Breathing rhythm: {rhythm['rhythm_state']}")
            
            # Create adapted swarm state
            adapted_state = SwarmState(
                breath=f"Environmental Adaptation {i+1}",
                kinship=0.6 + (env_assessment['vitality_score'] * 0.3),
                memory_depth=50 + i * 10,
                user_nutrient=f"Environmental Response: {env_data['condition']}",
                cycle_count=10 + i * 5,
                node_count=len(swarm_mind.nodes),
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            # Process through swarm
            fragment_outputs = swarm_mind.entwine_neural_pulse(adapted_state)
            if fragment_outputs:
                wisdom = swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
                if wisdom:
                    print(f"🌸 Adapted wisdom confidence: {wisdom['collective_confidence']:.3f}")
            
            # Learn from environmental adaptation
            learning = feedback.learn_from_nutrients(adapted_state, f"Environmental: {env_data['condition']}")
            if learning:
                print(f"🌿 Environmental learning recorded")
            
            # Brief pause between conditions
            time.sleep(0.1)
        
        # Final health assessment after environmental adaptations
        print("\n🌸 Final health assessment after environmental adaptations...")
        final_vitality = health_monitor.assess_swarm_vitality()
        print(f"🌿 Final swarm state: {final_vitality['state']} (score: {final_vitality['overall']:.3f})")
        
        print("🌸 Environmental-Swarm coordination test completed successfully!")
        return True
        
    except Exception as e:
        print(f"🌿 Coordination test whispered error: {e}")
        return False

def main():
    """🌸 Main test function for Phase 5.5 SwarmMind integration."""
    print("🌸 Phase 5.5 SwarmMind Integration Test")
    print("=" * 50)
    
    tests = [
        ("SwarmMind Phase 5.5 Integration", test_swarm_mind_with_phase_5_5),
        ("Environmental-Swarm Coordination", test_environmental_swarm_coordination)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'=' * 50}")
        result = test_func()
        status = "PASS" if result else "SKIP/FAIL"
        emoji = "🌸" if result else "🌫️"
        print(f"\n{emoji} {test_name}: {status}")
        
        if result:
            passed += 1
    
    print(f"\n{'=' * 50}")
    print(f"🌿 Integration tests passed: {passed}/{total}")
    
    if passed == total:
        print("🌸 Phase 5.5 SwarmMind integration is fully functional!")
        print("🌿 Mycelial wisdom flows through the neural network...")
    else:
        print("🌫️ Some integration aspects need attention")
        print("🌱 The mycelial threads are still growing...")

if __name__ == "__main__":
    main()