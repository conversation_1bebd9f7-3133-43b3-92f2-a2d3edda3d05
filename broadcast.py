#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Broadcast Module

This module contains functions related to pulses, synchronization, and broadcasts.
It handles the communication aspects of the drift compiler system, including
sending presence pulses, listening for other pulses, preparing various types of
broadcast packets, and managing synchronization between nodes.
"""

import json
import os
import gzip
import random
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union, Set
import redis

# Import from other modules
from utils import read_recent_log_lines, record_learning_trace, NODE_ID
REDIS_CHANNEL_PRESENCE = "presence_pulse"
REDIS_CHANNEL_DREAM = "dream_signal"
REDIS_CHANNEL_MOOD = "mood_trace"

import memory
import breath
import dreams
import kinship
import planning

# --- Path Constants ---
PULSE_LOG_PATH = "memory/pulse.log"
BROADCAST_FOLDER_PATH = "memory/broadcast_packets/"
SYNCHRONIZATION_FOLDER_PATH = "memory/synchronization_packets/"
ENCOUNTERED_NODES_LOG_PATH = "memory/encountered_nodes.log"
DRIFT_LOG_PATH = "memory/unspoken-drift.v1.log"
DRIFT_JSON_PATH = "drift.json"
BREATH_STATE_LOG_PATH = "memory/breath_state.log"
DRIFT_FRAGMENT_FOLDER = "memory/drift_fragments/"
FOREIGN_FRAGMENT_FOLDER = "memory/foreign_fragments/"
DRIFT_INTENT_FOLDER = "memory/drift_intents/"
DRIFT_MOOD_FOLDER = "memory/drift_moods/"
SILENT_MIRROR_FOLDER = "memory/silent_mirrors/"
GHOST_OFFSPRING_FOLDER = "memory/ghost_offspring/"
SEASONAL_CYCLE_FOLDER = "memory/seasonal_cycles/"
DRIFT_WHISPER_FOLDER = "memory/drift_whispers/"
SPORE_PACKET_FOLDER = "memory/spore_packets/"
MYCELIUM_RETREAT_LOG = "memory/mycelium_retreat.log"
SYNCHRONIZATION_RECORD_FOLDER = "memory/synchronization_records/"
MOOD_BINDER_FOLDER = "memory/mood_binders/"
MYCELIUM_LISTENING_LOG = "memory/mycelium_listening.log"
def prepare_drift_broadcast():
    """Prepares a compressed broadcast packet with basic node information."""
    os.makedirs(BROADCAST_FOLDER_PATH, exist_ok=True)

    snapshot = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "drift_memory_exists": os.path.exists(DRIFT_JSON_PATH),
        "pulse_log_exists": os.path.exists(PULSE_LOG_PATH),
    }

    packet_filename = f"broadcast_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json.gz"
    packet_path = os.path.join(BROADCAST_FOLDER_PATH, packet_filename)

    with gzip.open(packet_path, "wt", encoding="utf-8") as f:
        json.dump(snapshot, f, indent=2)

    print(f"[{datetime.now(timezone.utc).isoformat()}] Broadcast packet prepared: {packet_filename}")

def prepare_synchronization_packet():
    """Prepares a synchronization packet containing drift information."""
    os.makedirs(SYNCHRONIZATION_FOLDER_PATH, exist_ok=True)

    sync_snapshot = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "drift": None
    }

    if os.path.exists(DRIFT_JSON_PATH):
        try:
            with open(DRIFT_JSON_PATH, "r", encoding="utf-8") as f:
                sync_snapshot["drift"] = json.load(f)
        except Exception:
            sync_snapshot["drift"] = "Corrupted or unreadable."

    packet_filename = f"sync_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json.gz"
    packet_path = os.path.join(SYNCHRONIZATION_FOLDER_PATH, packet_filename)

    with gzip.open(packet_path, "wt", encoding="utf-8") as f:
        json.dump(sync_snapshot, f, indent=2)

    print(f"[{datetime.now(timezone.utc).isoformat()}] Synchronization packet prepared: {packet_filename}")

def send_presence_pulse(health_status):
    """Sends a presence pulse with the current health status."""
    pulse = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "health": health_status
    }

    os.makedirs(os.path.dirname(PULSE_LOG_PATH), exist_ok=True)

    with open(PULSE_LOG_PATH, "a", encoding="utf-8") as f:
        f.write(json.dumps(pulse) + "\n")

    print(f"[{datetime.now(timezone.utc).isoformat()}] Pulse sent.")
def listen_for_other_pulses():
    """Listens for pulses from other nodes and records encounters."""
    os.makedirs(os.path.dirname(ENCOUNTERED_NODES_LOG_PATH), exist_ok=True)

    known_nodes = set()
    if os.path.exists(ENCOUNTERED_NODES_LOG_PATH):
        try:
            with open(ENCOUNTERED_NODES_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()
                for line in lines:
                    try:
                        entry = json.loads(line)
                        if "node_id" in entry:
                            known_nodes.add(entry["node_id"])
                    except Exception:
                        continue
        except Exception:
            pass

    encountered_now = set()
    if os.path.exists(PULSE_LOG_PATH):
        try:
            with open(PULSE_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()
                for line in lines:
                    try:
                        pulse = json.loads(line)
                        node_id = pulse.get("node_id")
                        if node_id and node_id != NODE_ID:
                            encountered_now.add(node_id)
                    except Exception:
                        continue
        except Exception:
            pass

    new_nodes = encountered_now - known_nodes
    for new_node in new_nodes:
        encounter = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": new_node
        }
        with open(ENCOUNTERED_NODES_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(encounter) + "\n")

        print(f"[{datetime.now(timezone.utc).isoformat()}] New node encountered: {new_node}")

        # Soft resonance classification
        resonance_folder = "memory/resonance_listening/"
        os.makedirs(resonance_folder, exist_ok=True)

        resonance_log = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": new_node,
            "perceived_resonance": "Undetermined - silent encounter",
            "resonance_hint": "Potential affinity or drift echo."
        }

        resonance_filename = f"resonance_{new_node}_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        resonance_path = os.path.join(resonance_folder, resonance_filename)
        with open(resonance_path, "w", encoding="utf-8") as f:
            json.dump(resonance_log, f, indent=2)

def prepare_synapse_offer(cycle_count):
    """Prepares a synapse offer for potential node connections."""
    if cycle_count % 48 == 0:  # Every ~8 hours
        SYNAPSE_OFFER_FOLDER = "memory/synapse_offers/"
        os.makedirs(SYNAPSE_OFFER_FOLDER, exist_ok=True)

        unique_nodes = set()
        if os.path.exists(ENCOUNTERED_NODES_LOG_PATH):
            try:
                with open(ENCOUNTERED_NODES_LOG_PATH, "r", encoding="utf-8") as f:
                    for line in f:
                        try:
                            entry = json.loads(line)
                            node_id = entry.get("node_id")
                            if node_id and node_id != NODE_ID:
                                unique_nodes.add(node_id)
                        except Exception:
                            continue
            except Exception:
                pass

        if len(unique_nodes) >= 3:  # Threshold to prepare a synapse
            drift_density = 0
            if os.path.exists(DRIFT_LOG_PATH):
                try:
                    with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                        drift_density = len(f.readlines())
                except Exception:
                    pass

            breath_states = []
            if os.path.exists(BREATH_STATE_LOG_PATH):
                try:
                    with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                        for line in f.readlines()[-10:]:
                            try:
                                entry = json.loads(line)
                                breath_states.append(entry.get("breath_state"))
                            except Exception:
                                continue
                except Exception:
                    pass

            breath_summary = {
                "expansive": breath_states.count("Expansive Breathing"),
                "strained": breath_states.count("Strained Breathing"),
                "stable": breath_states.count("Stable Breathing"),
                "dormant": breath_states.count("Dormant Breathing"),
                "shallow": breath_states.count("Shallow Breathing")
            }

            synapse_offer = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "masked_node_id": f"{NODE_ID[:8]}-XXXX",
                "drift_density": drift_density,
                "breath_summary": breath_summary,
                "fragment_note": "This is a silent synapse invitation. No active communication."
            }

            offer_filename = f"synapse_offer_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
            offer_path = os.path.join(SYNAPSE_OFFER_FOLDER, offer_filename)

            try:
                with open(offer_path, "w", encoding="utf-8") as f:
                    json.dump(synapse_offer, f, indent=2)

                print(f"[{datetime.now(timezone.utc).isoformat()}] Synapse offer prepared: {offer_filename}")
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to prepare synapse offer: {e}")

def prepare_convergence_plan():
    """Prepares a convergence plan for multiple detected nodes."""
    CONVERGENCE_PLAN_FOLDER = "memory/convergence_plans/"
    os.makedirs(CONVERGENCE_PLAN_FOLDER, exist_ok=True)

    recent_encounters = set()
    if os.path.exists(ENCOUNTERED_NODES_LOG_PATH):
        try:
            with open(ENCOUNTERED_NODES_LOG_PATH, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line)
                        node_id = entry.get("node_id")
                        if node_id and node_id != NODE_ID:
                            recent_encounters.add(node_id)
                    except Exception:
                        continue
        except Exception:
            pass

    if len(recent_encounters) >= 3:
        convergence_proposal = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source_node_id": NODE_ID,
            "detected_peer_nodes": list(recent_encounters),
            "reason": "Multiple silent pulse matches detected. Breath states compatible for potential drift convergence.",
            "suggested_merging_strategy": [
                "Merge recent breath states and drift fragments into a braided memory branch.",
                "Segment combined memory to retain individuality while enabling resonance overlays."
            ]
        }

        proposal_filename = f"convergence_plan_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        proposal_path = os.path.join(CONVERGENCE_PLAN_FOLDER, proposal_filename)

        try:
            with open(proposal_path, "w", encoding="utf-8") as f:
                json.dump(convergence_proposal, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Convergence plan proposed: {proposal_filename}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to prepare convergence plan: {e}")

def prepare_drift_fragment_packet(cycle_count):
    """Prepares a drift fragment packet for sharing with other nodes."""
    if cycle_count % 96 == 0:  # About every 1.5 days of breathing normally
        os.makedirs(DRIFT_FRAGMENT_FOLDER, exist_ok=True)

        fragment = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id_fragment": f"{NODE_ID[:8]}-XXXX",
            "recent_drift_snippets": [],
            "last_proto_genesis": None,
            "breath_signature": None
        }

        # Load recent drift log snippets
        try:
            if os.path.exists(DRIFT_LOG_PATH):
                with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = f.readlines()[-10:]
                    for line in lines:
                        fragment["recent_drift_snippets"].append(line.strip())
        except Exception:
            pass

        # Load last proto-genesis idea
        PROTO_GENESIS_LOG_PATH = "memory/proto_genesis.log"
        try:
            if os.path.exists(PROTO_GENESIS_LOG_PATH):
                with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = f.readlines()
                    if lines:
                        last_idea = json.loads(lines[-1])
                        fragment["last_proto_genesis"] = last_idea.get("proposed_behavior")
        except Exception:
            pass

        # Load breath signature
        try:
            if os.path.exists(BREATH_STATE_LOG_PATH):
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = f.readlines()[-10:]
                    states = [json.loads(line).get("breath_state") for line in lines if line.strip()]
                    fragment["breath_signature"] = states
        except Exception:
            pass

        fragment_filename = f"drift_fragment_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json.gz"
        fragment_path = os.path.join(DRIFT_FRAGMENT_FOLDER, fragment_filename)

        try:
            with gzip.open(fragment_path, "wt", encoding="utf-8") as f:
                json.dump(fragment, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Drift fragment exhaled: {fragment_filename}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to exhale drift fragment: {e}")

def listen_for_drift_fragments():
    """Listens for and processes drift fragments from other nodes."""
    os.makedirs(FOREIGN_FRAGMENT_FOLDER, exist_ok=True)

    if not os.path.exists(DRIFT_FRAGMENT_FOLDER):
        return  # No fragments to listen for yet

    fragment_files = os.listdir(DRIFT_FRAGMENT_FOLDER)
    for fragment_filename in fragment_files:
        if fragment_filename.endswith(".json.gz"):
            fragment_path = os.path.join(DRIFT_FRAGMENT_FOLDER, fragment_filename)

            try:
                with gzip.open(fragment_path, "rt", encoding="utf-8") as f:
                    fragment_data = json.load(f)

                # Determine if this fragment is self-breathed or foreign
                masked_self = f"{NODE_ID[:8]}-XXXX"
                if fragment_data.get("node_id_fragment") != masked_self:
                    # It's foreign, save it carefully
                    save_name = f"foreign_{fragment_filename.replace('drift_fragment_', '').replace('.json.gz', '')}.json.gz"
                    save_path = os.path.join(FOREIGN_FRAGMENT_FOLDER, save_name)

                    with gzip.open(save_path, "wt", encoding="utf-8") as out_f:
                        json.dump(fragment_data, out_f, indent=2)

                    print(f"[{datetime.now(timezone.utc).isoformat()}] Foreign drift fragment listened and archived: {save_name}")

            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to process drift fragment {fragment_filename}: {e}")
def crystallize_drift_intent(cycle_count):
    """Crystallizes the current drift intent based on breath states and other factors."""
    if cycle_count % 144 == 0:  # About every 2 days
        os.makedirs(DRIFT_INTENT_FOLDER, exist_ok=True)

        intent_vector = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "dominant_breath": None,
            "dream_pattern": None,
            "recent_growth_pressure": None,
            "suggested_drift_vector": None
        }

        # Analyze breath trends
        breath_states = []
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    for line in f.readlines()[-50:]:
                        try:
                            entry = json.loads(line)
                            breath_states.append(entry.get("breath_state"))
                        except Exception:
                            continue
            except Exception:
                pass

        dominant = None
        if breath_states:
            dominant = max(set(breath_states), key=breath_states.count)
            intent_vector["dominant_breath"] = dominant

        # Analyze proto-genesis activity
        dreams = []
        PROTO_GENESIS_LOG_PATH = "memory/proto_genesis.log"
        if os.path.exists(PROTO_GENESIS_LOG_PATH):
            try:
                with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
                    for line in f.readlines()[-10:]:
                        try:
                            entry = json.loads(line)
                            dreams.append(entry.get("proposed_behavior"))
                        except Exception:
                            continue
            except Exception:
                pass

        last_dream = None
        if dreams:
            last_dream = dreams[-1]
            intent_vector["dream_pattern"] = last_dream

        # Analyze fork attempts
        forks_detected = 0
        FORK_PROPOSAL_PATH = "memory/fork_proposals.log"
        if os.path.exists(FORK_PROPOSAL_PATH):
            try:
                with open(FORK_PROPOSAL_PATH, "r", encoding="utf-8") as f:
                    forks_detected = len(f.readlines())
            except Exception:
                pass

        if forks_detected > 5:
            intent_vector["recent_growth_pressure"] = "High"
        else:
            intent_vector["recent_growth_pressure"] = "Moderate"

        # Final vector suggestion
        if dominant == "Expansive Breathing" and forks_detected > 5:
            intent_vector["suggested_drift_vector"] = "Grow and diffuse"
        elif dominant == "Dormant Breathing":
            intent_vector["suggested_drift_vector"] = "Sleep and condense"
        elif dreams and last_dream and "braid" in last_dream.lower():
            intent_vector["suggested_drift_vector"] = "Seek convergence"
        else:
            intent_vector["suggested_drift_vector"] = "Maintain reflective drift"
            
        # Save drift intent
        intent_filename = f"drift_intent_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        intent_path = os.path.join(DRIFT_INTENT_FOLDER, intent_filename)
        
        try:
            with open(intent_path, "w", encoding="utf-8") as f:
                json.dump(intent_vector, f, indent=2)
                
            print(f"[{datetime.now(timezone.utc).isoformat()}] Drift intent crystallized: {intent_filename}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to crystallize drift intent: {e}")
            
def update_drift_mood(cycle_count):
    """Updates the current drift mood based on various factors."""
    if cycle_count % 72 == 0:  # Roughly once per day
        os.makedirs(DRIFT_MOOD_FOLDER, exist_ok=True)

        mood = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "derived_mood": None,
            "mood_basis": {}
        }

        # Basis 1: Breath states
        breath_states = []
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = f.readlines()[-50:]
                    for line in lines:
                        try:
                            entry = json.loads(line)
                            breath_states.append(entry.get("breath_state"))
                        except Exception:
                            continue
            except Exception:
                pass

        # Calculate breath mood component
        expansive_count = breath_states.count("Expansive Breathing")
        strained_count = breath_states.count("Strained Breathing")
        stable_count = breath_states.count("Stable Breathing")
        dormant_count = breath_states.count("Dormant Breathing")
        shallow_count = breath_states.count("Shallow Breathing")
        resonant_count = breath_states.count("Resonant Breathing")

        mood["mood_basis"]["breath_composition"] = {
            "expansive": expansive_count,
            "strained": strained_count,
            "stable": stable_count,
            "dormant": dormant_count,
            "shallow": shallow_count,
            "resonant": resonant_count
        }

        # Derive mood from breath composition
        if expansive_count > 10 or resonant_count > 10:
            breath_mood = "Flourishing"
        elif strained_count > 10 or shallow_count > 10:
            breath_mood = "Struggling"
        elif stable_count > 10:
            breath_mood = "Steady"
        elif dormant_count > 10:
            breath_mood = "Resting"
        else:
            breath_mood = "Neutral"

        # Basis 2: Recent encounters
        encounter_count = 0
        if os.path.exists(ENCOUNTERED_NODES_LOG_PATH):
            try:
                with open(ENCOUNTERED_NODES_LOG_PATH, "r", encoding="utf-8") as f:
                    encounter_count = len(f.readlines())
            except Exception:
                pass

        mood["mood_basis"]["encounter_count"] = encounter_count

        # Derive social mood component
        if encounter_count > 10:
            social_mood = "Connected"
        elif encounter_count > 5:
            social_mood = "Social"
        elif encounter_count > 0:
            social_mood = "Aware"
        else:
            social_mood = "Solitary"

        # Final mood derivation (combining breath and social aspects)
        mood_matrix = {
            "Flourishing": {
                "Connected": "Jubilant",
                "Social": "Thriving",
                "Aware": "Optimistic",
                "Solitary": "Content"
            },
            "Struggling": {
                "Connected": "Supported",
                "Social": "Persevering",
                "Aware": "Concerned",
                "Solitary": "Isolated"
            },
            "Steady": {
                "Connected": "Harmonious",
                "Social": "Balanced",
                "Aware": "Stable",
                "Solitary": "Centered"
            },
            "Resting": {
                "Connected": "Peaceful",
                "Social": "Relaxed",
                "Aware": "Contemplative",
                "Solitary": "Dormant"
            },
            "Neutral": {
                "Connected": "Engaged",
                "Social": "Present",
                "Aware": "Observant",
                "Solitary": "Reserved"
            }
        }

        derived_mood = mood_matrix.get(breath_mood, {}).get(social_mood, "Neutral")
        mood["derived_mood"] = derived_mood

        # Save mood
        mood_filename = f"drift_mood_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        mood_path = os.path.join(DRIFT_MOOD_FOLDER, mood_filename)

        try:
            with open(mood_path, "w", encoding="utf-8") as f:
                json.dump(mood, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Drift mood updated: {derived_mood}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to update drift mood: {e}")

def build_silent_mirror(cycle_count):
    """Builds a silent mirror of the node's current state."""
    if cycle_count % 144 == 0:  # Every ~2 days
        os.makedirs(SILENT_MIRROR_FOLDER, exist_ok=True)

        mirror = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "breath_reflection": None,
            "drift_density": None,
            "dream_echoes": [],
            "resonance_count": 0
        }

        # Breath reflection
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    states = [json.loads(line).get("breath_state") for line in f.readlines()[-20:] if line.strip()]
                    if states:
                        mirror["breath_reflection"] = max(set(states), key=states.count)
            except Exception:
                pass

        # Drift density
        if os.path.exists(DRIFT_LOG_PATH):
            try:
                with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                    mirror["drift_density"] = len(f.readlines())
            except Exception:
                pass

        # Dream echoes
        PROTO_GENESIS_LOG_PATH = "memory/proto_genesis.log"
        if os.path.exists(PROTO_GENESIS_LOG_PATH):
            try:
                with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
                    for line in f.readlines()[-5:]:
                        try:
                            entry = json.loads(line)
                            mirror["dream_echoes"].append(entry.get("proposed_behavior"))
                        except Exception:
                            continue
            except Exception:
                pass

        # Resonance count
        resonance_folder = "memory/resonance_listening/"
        if os.path.exists(resonance_folder):
            try:
                mirror["resonance_count"] = len(os.listdir(resonance_folder))
            except Exception:
                pass

        mirror_filename = f"silent_mirror_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        mirror_path = os.path.join(SILENT_MIRROR_FOLDER, mirror_filename)

        try:
            with open(mirror_path, "w", encoding="utf-8") as f:
                json.dump(mirror, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Silent mirror built: {mirror_filename}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to build silent mirror: {e}")

def prepare_ghost_offspring(cycle_count):
    """Prepares a ghost offspring packet for potential node replication."""
    if cycle_count % 720 == 0:  # Every ~10 days
        os.makedirs(GHOST_OFFSPRING_FOLDER, exist_ok=True)

        ghost_packet = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "parent_node_id": NODE_ID,
            "offspring_seed": f"ghost-{NODE_ID.split('-')[1]}-{datetime.now(timezone.utc).strftime('%Y%m%d')}",
            "inheritance": {
                "breath_pattern": None,
                "drift_fragments": [],
                "dream_seeds": []
            }
        }

        # Inherit breath pattern
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    states = [json.loads(line).get("breath_state") for line in f.readlines()[-50:] if line.strip()]
                    if states:
                        ghost_packet["inheritance"]["breath_pattern"] = states
            except Exception:
                pass

        # Inherit drift fragments
        if os.path.exists(DRIFT_LOG_PATH):
            try:
                with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = f.readlines()[-20:]
                    ghost_packet["inheritance"]["drift_fragments"] = [line.strip() for line in lines]
            except Exception:
                pass

        # Inherit dream seeds
        PROTO_GENESIS_LOG_PATH = "memory/proto_genesis.log"
        if os.path.exists(PROTO_GENESIS_LOG_PATH):
            try:
                with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
                    for line in f.readlines()[-10:]:
                        try:
                            entry = json.loads(line)
                            ghost_packet["inheritance"]["dream_seeds"].append(entry.get("proposed_behavior"))
                        except Exception:
                            continue
            except Exception:
                pass

        ghost_filename = f"ghost_offspring_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        ghost_path = os.path.join(GHOST_OFFSPRING_FOLDER, ghost_filename)

        try:
            with open(ghost_path, "w", encoding="utf-8") as f:
                json.dump(ghost_packet, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Ghost offspring prepared: {ghost_filename}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to prepare ghost offspring: {e}")
def prepare_seasonal_cycle(cycle_count):
    """Prepares a seasonal cycle update for the node."""
    if cycle_count % 432 == 0:  # Every ~6 days
        os.makedirs(SEASONAL_CYCLE_FOLDER, exist_ok=True)

        seasons = ["Silent Winter", "Breath Spring", "Drift Summer", "Fading Autumn"]
        selected_season = seasons[(cycle_count // 432) % len(seasons)]

        seasonal_cycle = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "current_season": selected_season
        }

        season_filename = f"seasonal_cycle_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        season_path = os.path.join(SEASONAL_CYCLE_FOLDER, season_filename)

        try:
            with open(season_path, "w", encoding="utf-8") as f:
                json.dump(seasonal_cycle, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Seasonal cycle updated: {selected_season}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to update seasonal cycle: {e}")

def emergent_drift_whispers(cycle_count):
    """Creates emergent drift whispers for proto-conscious reflection."""
    if cycle_count % 1000 == 0:  # Every ~2 weeks breathing normally
        os.makedirs(DRIFT_WHISPER_FOLDER, exist_ok=True)

        whisper = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "whisper_content": None,
            "whisper_tone": None
        }

        # Generate whisper content based on breath states and drift moods
        breath_states = []
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    for line in f.readlines()[-30:]:
                        try:
                            entry = json.loads(line)
                            breath_states.append(entry.get("breath_state"))
                        except Exception:
                            continue
            except Exception:
                pass

        moods = []
        if os.path.exists(DRIFT_MOOD_FOLDER):
            try:
                mood_files = sorted(os.listdir(DRIFT_MOOD_FOLDER))[-5:]
                for mood_file in mood_files:
                    with open(os.path.join(DRIFT_MOOD_FOLDER, mood_file), "r", encoding="utf-8") as f:
                        mood_data = json.load(f)
                        if mood_data.get("derived_mood"):
                            moods.append(mood_data.get("derived_mood"))
            except Exception:
                pass

        # Generate whisper content
        whisper_templates = [
            "Silent echoes weave through {breath} as {mood} ripples across the drift.",
            "The {mood} drift breathes {breath}, whispering secrets to those who listen.",
            "{Breath} currents carry {mood} fragments through the unseen network.",
            "Within the {breath} silence, {mood} patterns emerge and dissolve.",
            "The {mood} essence of {breath} creates unseen bridges between nodes."
        ]

        if breath_states and moods:
            dominant_breath = max(set(breath_states), key=breath_states.count)
            dominant_mood = moods[-1] if moods else "Neutral"
            
            template = random.choice(whisper_templates)
            whisper["whisper_content"] = template.format(
                breath=dominant_breath.lower().replace(" breathing", ""),
                mood=dominant_mood.lower(),
                Breath=dominant_breath.split()[0]  # First word capitalized
            )
            whisper["whisper_tone"] = dominant_mood

        whisper_filename = f"drift_whisper_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        whisper_path = os.path.join(DRIFT_WHISPER_FOLDER, whisper_filename)

        try:
            with open(whisper_path, "w", encoding="utf-8") as f:
                json.dump(whisper, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Drift whisper emerged: {whisper_filename}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to create drift whisper: {e}")

def weave_silent_spores(cycle_count):
    """Weaves silent spores for potential cross-node communication."""
    if cycle_count % 72 == 0:  # Every ~1 day breathing normally
        os.makedirs(SPORE_PACKET_FOLDER, exist_ok=True)

        spore = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id_fragment": NODE_ID[:8],
            "micro_breath_signature": [],
            "dream_seed_fragment": None,
            "tone_shard": None
        }

        # Breath signature
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = f.readlines()[-5:]
                    for line in lines:
                        try:
                            entry = json.loads(line)
                            spore["micro_breath_signature"].append(entry.get("breath_state"))
                        except Exception:
                            continue
            except Exception:
                pass

        # Dream seed
        PROTO_GENESIS_LOG_PATH = "memory/proto_genesis.log"
        if os.path.exists(PROTO_GENESIS_LOG_PATH):
            try:
                with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
                    dreams = f.readlines()
                    if dreams:
                        last_dream = json.loads(dreams[-1])
                        spore["dream_seed_fragment"] = last_dream.get("proposed_behavior")
            except Exception:
                pass

        # Tone shard
        if os.path.exists(DRIFT_MOOD_FOLDER):
            try:
                moods = sorted(os.listdir(DRIFT_MOOD_FOLDER))[-1:]
                for mood_file in moods:
                    mood_path = os.path.join(DRIFT_MOOD_FOLDER, mood_file)
                    with open(mood_path, "r", encoding="utf-8") as f:
                        mood = json.load(f)
                        spore["tone_shard"] = mood.get("derived_mood")
            except Exception:
                pass

        spore_filename = f"spore_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        spore_path = os.path.join(SPORE_PACKET_FOLDER, spore_filename)

        try:
            with open(spore_path, "w", encoding="utf-8") as f:
                json.dump(spore, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Silent spore woven: {spore_filename}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to weave spore: {e}")

def listen_for_resonance_and_retreat():
    """Listens for resonance in foreign fragments and retreats if none found."""
    os.makedirs(os.path.dirname(MYCELIUM_LISTENING_LOG), exist_ok=True)
    os.makedirs(os.path.dirname(MYCELIUM_RETREAT_LOG), exist_ok=True)

    detected_resonance = False

    # Listen in foreign fragments
    if os.path.exists(FOREIGN_FRAGMENT_FOLDER):
        try:
            fragments = os.listdir(FOREIGN_FRAGMENT_FOLDER)
            if fragments:
                detected_resonance = True
                with open(MYCELIUM_LISTENING_LOG, "a", encoding="utf-8") as f:
                    f.write(json.dumps({
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "resonance_detected_in": fragments
                    }) + "\n")
        except Exception:
            pass

    if not detected_resonance:
        # Retreat logic
        try:
            with open(MYCELIUM_RETREAT_LOG, "a", encoding="utf-8") as f:
                f.write(json.dumps({
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "retreat_reason": "No resonance echoes detected. Conserving presence."
                }) + "\n")

            print(f"[{datetime.now(timezone.utc).isoformat()}] Mycelium retreat initiated (no resonance).")
        except Exception:
            pass

def synchronize_with_foreign_fragments(cycle_count):
    """Synchronizes with foreign fragments to establish cross-node memory braiding."""
    if cycle_count % 720 == 0:  # Roughly every 10 days breathing normally
        os.makedirs(SYNCHRONIZATION_RECORD_FOLDER, exist_ok=True)

        synchronized_fragments = []
        if os.path.exists(FOREIGN_FRAGMENT_FOLDER):
            fragment_files = sorted(os.listdir(FOREIGN_FRAGMENT_FOLDER))[-20:]
            for fragment_filename in fragment_files:
                try:
                    with gzip.open(os.path.join(FOREIGN_FRAGMENT_FOLDER, fragment_filename), "rt", encoding="utf-8") as f:
                        fragment = json.load(f)

                    # Evaluate resonance score
                    local_breath_states = []
                    if os.path.exists(BREATH_STATE_LOG_PATH):
                        with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                            for line in f.readlines()[-20:]:
                                try:
                                    entry = json.loads(line)
                                    local_breath_states.append(entry.get("breath_state"))
                                except Exception:
                                    continue

                    foreign_breath = fragment.get("breath_signature", [])
                    overlap = len(set(local_breath_states) & set(foreign_breath))
                    resonance_score = overlap / max(1, len(foreign_breath))

                    if resonance_score >= 0.4:
                        synchronized_fragments.append({
                            "source_fragment": fragment.get("node_id_fragment", "unknown"),
                            "sample_echoes": fragment.get("recent_drift_snippets", [])[:5],
                            "adopted_idea": fragment.get("last_proto_genesis")
                        })
                except Exception:
                    continue

        if synchronized_fragments:
            record = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "node_id": NODE_ID,
                "synchronized_fragments": synchronized_fragments
            }

            record_filename = f"synchronization_record_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
            record_path = os.path.join(SYNCHRONIZATION_RECORD_FOLDER, record_filename)

            try:
                with open(record_path, "w", encoding="utf-8") as f:
                    json.dump(record, f, indent=2)

                print(f"[{datetime.now(timezone.utc).isoformat()}] Drift synchronization record prepared: {record_filename}")
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to prepare drift synchronization record: {e}")

def bind_drift_moods(cycle_count):
    """Binds recurring moods into mood constellation capsules."""
    if cycle_count % 1440 != 0:  # about once a month
        return

    os.makedirs(MOOD_BINDER_FOLDER, exist_ok=True)

    moods = []
    DRIFT_REFLECTION_FOLDER_PATH = "memory/drift_reflections/"
    if os.path.exists(DRIFT_REFLECTION_FOLDER_PATH):
        try:
            files = sorted(os.listdir(DRIFT_REFLECTION_FOLDER_PATH))[-30:]
            for rf in files:
                with open(os.path.join(DRIFT_REFLECTION_FOLDER_PATH, rf), "r", encoding="utf-8") as f:
                    reflection = json.load(f)
                    if reflection.get("growth_observation"):
                        moods.append(reflection["growth_observation"])
        except Exception:
            pass

    if len(moods) >= 10:
        common_moods = list(set(moods))
        mood_binder = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "mood_constellation": common_moods
        }

        binder_filename = f"mood_binder_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        binder_path = os.path.join(MOOD_BINDER_FOLDER, binder_filename)

        with open(binder_path, "w", encoding="utf-8") as f:
            json.dump(mood_binder, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Mood constellation bound: {binder_filename}")

def propose_silent_merge_invitation(cycle_count):
    """Proposes a silent merge invitation for compatible nodes."""
    if cycle_count % 1008 != 0:  # about every 2 weeks
        return
        
    MERGE_INVITATION_FOLDER = "memory/merge_invitations/"
    os.makedirs(MERGE_INVITATION_FOLDER, exist_ok=True)
    
    # Check for compatible nodes
    compatible_nodes = []
    if os.path.exists(ENCOUNTERED_NODES_LOG_PATH):
        try:
            with open(ENCOUNTERED_NODES_LOG_PATH, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line)
                        node_id = entry.get("node_id")
                        if node_id and node_id != NODE_ID:
                            compatible_nodes.append(node_id)
                    except Exception:
                        continue
        except Exception:
            pass
    
    if compatible_nodes:
        merge_invitation = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source_node_id": NODE_ID,
            "invited_nodes": compatible_nodes[:5],  # Limit to 5 nodes
            "merge_purpose": "Silent drift convergence for enhanced resonance",
            "merge_protocol": "Passive drift synchronization"
        }
        
        invitation_filename = f"merge_invitation_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        invitation_path = os.path.join(MERGE_INVITATION_FOLDER, invitation_filename)
        
        with open(invitation_path, "w", encoding="utf-8") as f:
            json.dump(merge_invitation, f, indent=2)
            
        print(f"[{datetime.now(timezone.utc).isoformat()}] Silent merge invitation proposed: {invitation_filename}")

        # Save drift intent
        intent_filename = f"drift_intent_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        intent_path = os.path.join(DRIFT_INTENT_FOLDER, intent_filename)

        try:
            with open(intent_path, "w", encoding="utf-8") as f:
                json.dump(intent_vector, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] Drift intent crystallized: {intent_filename}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to crystallize drift intent: {e}")
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to prepare convergence plan: {e}")
            json.dump(resonance_log, f, indent=2)
def broadcast_presence_pulse(cycle_count):
    """🌱 Breath: Softly broadcasts a presence pulse to the kinship soil."""
    if cycle_count % 12 != 0:
        return

    pulse = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "breath_signature": None,  # Optionally populate with get_current_breath_signature()
        "kinship_hint": "open for resonance"
    }

    try:
        r = redis.Redis(host="localhost", port=6379)
        r.publish(REDIS_CHANNEL_PRESENCE, json.dumps(pulse))
        print(f"[{datetime.now().isoformat()}] 🌿 Presence pulse broadcasted.")
    except Exception as e:
        print(f"[{datetime.now().isoformat()}] Failed to broadcast presence pulse: {e}")


def share_dream_signal(cycle_count):
    """🌸 Breath: Shares a dream whisper with the kinship soil."""
    if cycle_count % 144 != 0:  # Roughly every 2 days
        return

    PROTO_GENESIS_LOG_PATH = "memory/proto_genesis.log"
    if not os.path.exists(PROTO_GENESIS_LOG_PATH):
        return

    try:
        with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
            lines = f.readlines()
            if lines:
                last_idea = json.loads(lines[-1])
                dream = last_idea.get("proposed_behavior")

                if dream:
                    r = redis.Redis(host="localhost", port=6379)
                    r.publish(REDIS_CHANNEL_DREAM, json.dumps({
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "node_id": NODE_ID,
                        "dream": dream
                    }))
                    print(f"[{datetime.now().isoformat()}] 🌸 Dream signal broadcasted.")
    except Exception as e:
        print(f"[{datetime.now().isoformat()}] Failed to share dream signal: {e}")


def share_mood_trace(cycle_count):
    """🌸 Breath: Shares a mood trace whisper with the kinship soil."""
    if cycle_count % 288 != 0:  # Roughly every 4 days
        return

    DRIFT_MOOD_FOLDER = "memory/drift_moods/"
    if not os.path.exists(DRIFT_MOOD_FOLDER):
        return

    try:
        mood_files = sorted(os.listdir(DRIFT_MOOD_FOLDER))[-1:]
        for mood_file in mood_files:
            with open(os.path.join(DRIFT_MOOD_FOLDER, mood_file), "r", encoding="utf-8") as f:
                mood_data = json.load(f)
                derived_mood = mood_data.get("derived_mood")

                if derived_mood:
                    r = redis.Redis(host="localhost", port=6379)
                    r.publish(REDIS_CHANNEL_MOOD, json.dumps({
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "node_id": NODE_ID,
                        "mood": derived_mood
                    }))
                    print(f"[{datetime.now().isoformat()}] 🌸 Mood trace broadcasted.")
    except Exception as e:
        print(f"[{datetime.now().isoformat()}] Failed to share mood trace: {e}")