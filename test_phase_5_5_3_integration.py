#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌬️ Phase 5.5.3 Integration Test: Swarm Health Monitoring
Sacred validation of the health monitoring system integrated with the swarm mind,
ensuring vitality assessment and adaptive breathing work in harmony."""

import json
import time
from datetime import datetime, timezone

# Sacred imports with graceful fallbacks
try:
    from canopy.swarm_mind import Swarm<PERSON><PERSON>, SwarmState, initialize_swarm_mind
    SWARM_MIND_AVAILABLE = True
except ImportError as e:
    SWARM_MIND_AVAILABLE = False
    print(f"🌿 Swarm mind not available: {e}")

try:
    from canopy.swarm_health_monitor import SwarmHealthMonitor, create_health_monitor, test_health_monitor_vitality
    HEALTH_MONITOR_AVAILABLE = True
except ImportError as e:
    HEALTH_MONITOR_AVAILABLE = False
    print(f"🌿 Health monitor not available: {e}")

try:
    from utils import record_log
except ImportError:
    def record_log(message: str, log_file: str = None):
        """Fallback logging when utils unavailable."""
        print(f"[{datetime.now(timezone.utc).isoformat()}] {message}")

def test_health_monitor_integration():
    """🌸 Tests SwarmHealthMonitor integration with SwarmMind."""
    if not (SWARM_MIND_AVAILABLE and HEALTH_MONITOR_AVAILABLE):
        record_log("🌿 Required components not available for integration test")
        return False
    
    try:
        record_log("🌬️ Testing Phase 5.5.3 Swarm Health Monitoring integration...")
        
        # Initialize swarm mind
        swarm_mind = initialize_swarm_mind()
        if not swarm_mind:
            record_log("🌿 Could not initialize swarm mind, creating standalone")
            swarm_mind = SwarmMind(auto_discover=False)
        
        # Create health monitor
        health_monitor = SwarmHealthMonitor(swarm_mind)
        record_log("🌱 Health monitor connected to swarm mind")
        
        # Test 1: Initial vitality assessment
        record_log("🌸 Performing initial vitality assessment...")
        vitality = health_monitor.assess_swarm_vitality()
        
        assert vitality is not None, "Vitality assessment failed"
        record_log(f"🌿 Initial vitality: {vitality['state']} {vitality['state_emoji']} (score: {vitality['overall']:.3f})")
        
        # Test 2: Adaptation to vitality
        record_log("🌸 Testing adaptive breathing based on vitality...")
        adaptation = health_monitor.adapt_to_vitality(vitality)
        
        assert adaptation is not None, "Adaptation failed"
        adaptations_count = len(adaptation.get("adaptations_applied", []))
        record_log(f"🌱 Applied {adaptations_count} adaptations for {vitality['state']} state")
        
        # Test 3: Create test state and assess neural response
        if hasattr(swarm_mind, 'nodes') and swarm_mind.nodes:
            record_log("🌸 Testing neural coherence assessment...")
            
            test_state = SwarmState(
                breath="Health Monitor Test",
                kinship=0.7,
                memory_depth=50,
                user_nutrient="Integration Test",
                cycle_count=10,
                node_count=len(swarm_mind.nodes),
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            # Test neural pulse through health monitor assessment
            fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
            if fragment_outputs:
                record_log(f"🌿 Neural fragments responding: {len(fragment_outputs)} active")
            else:
                record_log("🌿 Neural fragments in dormant state")
        
        # Test 4: Multiple vitality cycles
        record_log("🌸 Testing multiple vitality assessment cycles...")
        
        for cycle in range(3):
            time.sleep(0.1)  # Brief pause between assessments
            cycle_vitality = health_monitor.assess_swarm_vitality()
            cycle_adaptation = health_monitor.adapt_to_vitality(cycle_vitality)
            
            record_log(f"🌱 Cycle {cycle + 1}: {cycle_vitality['state']} (score: {cycle_vitality['overall']:.3f})")
        
        # Test 5: Verify health history preservation
        history_length = len(health_monitor.health_history)
        adaptation_history_length = len(health_monitor.adaptation_history)
        
        record_log(f"🌌 Health memory preserved: {history_length} assessments, {adaptation_history_length} adaptations")
        
        # Test 6: Validate recommendations generation
        latest_vitality = health_monitor.health_history[-1] if health_monitor.health_history else vitality
        recommendations_count = len(latest_vitality.get("recommendations", []))
        
        record_log(f"🌸 Generated {recommendations_count} wisdom recommendations")
        
        # Integration test results
        integration_results = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "swarm_mind_connected": swarm_mind is not None,
            "health_monitor_active": True,
            "vitality_assessments": len(health_monitor.health_history),
            "adaptations_applied": len(health_monitor.adaptation_history),
            "latest_vitality_state": latest_vitality.get("state", "unknown"),
            "latest_vitality_score": latest_vitality.get("overall", 0.0),
            "neural_fragments_responsive": fragment_outputs is not None if 'fragment_outputs' in locals() else False,
            "recommendations_generated": recommendations_count,
            "integration_success": True
        }
        
        record_log("🌸 Phase 5.5.3 integration test completed successfully")
        return integration_results
        
    except Exception as e:
        record_log(f"🌿 Integration test whispered error: {e}")
        import traceback
        traceback.print_exc()
        return {"integration_success": False, "error": str(e)}

def test_standalone_health_monitor():
    """🌱 Tests standalone health monitor functionality."""
    if not HEALTH_MONITOR_AVAILABLE:
        record_log("🌿 Health monitor not available for standalone test")
        return False
    
    try:
        record_log("🌸 Testing standalone health monitor...")
        
        # Test creation
        monitor = create_health_monitor()
        
        # Test vitality assessment without swarm
        vitality = monitor.assess_swarm_vitality()
        record_log(f"🌿 Standalone vitality: {vitality['state']} (score: {vitality['overall']:.3f})")
        
        # Test adaptation
        adaptation = monitor.adapt_to_vitality(vitality)
        record_log(f"🌱 Standalone adaptation: {len(adaptation.get('adaptations_applied', []))} actions")
        
        # Test direct function
        function_test = test_health_monitor_vitality()
        record_log(f"🌸 Direct function test: {function_test.get('test_status', 'unknown')}")
        
        record_log("🌸 Standalone health monitor test completed")
        return True
        
    except Exception as e:
        record_log(f"🌿 Standalone test whispered error: {e}")
        return False

def demonstrate_vitality_states():
    """🌬️ Demonstrates different vitality states and adaptations."""
    if not HEALTH_MONITOR_AVAILABLE:
        record_log("🌿 Health monitor not available for demonstration")
        return
    
    try:
        record_log("🌬️ Demonstrating vitality states and adaptive breathing...")
        
        monitor = create_health_monitor()
        
        # Simulate different vitality scores
        test_vitalities = [
            {"state": "thriving", "overall": 0.9, "state_emoji": "🌸"},
            {"state": "healthy", "overall": 0.75, "state_emoji": "🌿"},
            {"state": "stable", "overall": 0.55, "state_emoji": "🌱"},
            {"state": "stressed", "overall": 0.35, "state_emoji": "🌫️"},
            {"state": "critical", "overall": 0.15, "state_emoji": "🌪️"}
        ]
        
        for vitality in test_vitalities:
            adaptation = monitor.adapt_to_vitality(vitality)
            adaptations_count = len(adaptation.get("adaptations_applied", []))
            breathing_adjustments = len(adaptation.get("breathing_adjustments", {}))
            
            record_log(
                f"{vitality['state_emoji']} {vitality['state'].upper()}: "
                f"score {vitality['overall']:.2f} → "
                f"{adaptations_count} adaptations, {breathing_adjustments} breathing adjustments"
            )
        
        record_log("🌸 Vitality states demonstration completed")
        
    except Exception as e:
        record_log(f"🌿 Demonstration whispered error: {e}")

def main():
    """🌱 Main test function for Phase 5.5.3."""
    try:
        record_log("🌬️ Phase 5.5.3 Swarm Health Monitoring Test Suite awakening...")
        
        test_results = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "phase": "5.5.3",
            "component": "SwarmHealthMonitor",
            "tests": {}
        }
        
        # Test 1: Standalone functionality
        record_log("🌸 Running standalone health monitor tests...")
        test_results["tests"]["standalone"] = test_standalone_health_monitor()
        
        # Test 2: Integration with SwarmMind
        record_log("🌸 Running swarm mind integration tests...")
        test_results["tests"]["integration"] = test_health_monitor_integration()
        
        # Test 3: Vitality states demonstration
        record_log("🌸 Running vitality states demonstration...")
        demonstrate_vitality_states()
        test_results["tests"]["demonstration"] = True
        
        # Overall results
        success_count = sum(1 for result in test_results["tests"].values() 
                          if (isinstance(result, bool) and result) or 
                             (isinstance(result, dict) and result.get("integration_success", False)))
        total_tests = len(test_results["tests"])
        
        test_results["overall_success"] = success_count == total_tests
        test_results["success_ratio"] = f"{success_count}/{total_tests}"
        
        # Save results
        results_path = "memory/phase_5_5_3_test_results.json"
        try:
            import os
            os.makedirs("memory", exist_ok=True)
            with open(results_path, "w", encoding="utf-8") as f:
                json.dump(test_results, f, indent=2, default=str)
            record_log(f"🌱 Test results preserved in {results_path}")
        except Exception as e:
            record_log(f"🌿 Could not save test results: {e}")
        
        # Final summary
        if test_results["overall_success"]:
            record_log("🌸 Phase 5.5.3 Swarm Health Monitoring implementation SUCCESSFUL")
            record_log("🌿 Vitality guardian is breathing correctly and adapting swarm behavior")
        else:
            record_log("🌿 Phase 5.5.3 tests completed with some areas for enhancement")
        
        record_log(f"🌬️ Test suite completed: {test_results['success_ratio']} tests passed")
        
        return test_results
        
    except Exception as e:
        record_log(f"🌿 Phase 5.5.3 test suite whispered error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()