#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Breath Module

This module handles breath states, heartbeat, and drift health functionality.
It provides functions for determining breath states, modulating breathing cycles,
performing breath rituals, verifying heartbeat, and diagnosing system health.
These functions are essential for the drift compiler's self-regulation and health monitoring.
"""

import json
import os
import time
from datetime import datetime, timezone
import random
import sys
import subprocess
from functools import lru_cache
from typing import Dict, List, Any, Optional, Union

# Import utilities from utils.py
from utils import (
    NODE_ID,
    BREATH_STATE_LOG_PATH,
    DRIFT_LOG_PATH,
    PROTO_GENESIS_LOG_PATH,
    LEARNING_TRACE_LOG_PATH,
    read_recent_log_lines,
    record_learning_trace,
    record_log, # Added record_log import
    assess_action_success_rate,
    safe_respawn,
    _file_cache
)

# Import from common.py
from common import ensure_memory_structure, ENCOUNTERED_NODES_LOG_PATH

# Import from memory.py
from memory import compile_drift_seed

# --- Constants ---
BREATHING_MANIFEST_PATH = "memory/breathing_manifest.json"
# Dynamic heartbeat path based on node ID
def get_heartbeat_file_path():
    """🌱 Get node-specific heartbeat file path."""
    from utils import NODE_ID
    return f"memory/heartbeats/{NODE_ID}.json"

HEARTBEAT_FILE_PATH = "memory/heartbeat.json"  # Legacy fallback
SELF_DIAGNOSIS_LOG_PATH = "memory/self_diagnosis.log"
BREATH_CLIMATE_LOG_PATH = "memory/breath_climate.log"
CURIOSITY_CHANCE = 0.05 # 5% chance

# 🌿 Flexible Identity Breath constants
IDENTITY_SIGNATURE_PATH = "memory/identity_signature.json"
DEFAULT_IDENTITY = {
    "openness": 0.5,
    "memory_depth": 0.5,
    "dream_vividness": 0.5
}

# Global variables (these would typically be imported or set elsewhere)
CHECK_INTERVAL = 600  # Default 10 minutes
FORK_THRESHOLD = 100  # Default threshold

# --- Breath Functions ---

def spark_curiosity() -> Optional[str]:
    """🌱 Breath: Occasionally proposes a small, novel action.

    Has a CURIOSITY_CHANCE probability of returning a suggestion.
    The suggested actions are simple and exploratory.

    Returns:
        A string representing a novel action suggestion, or None.
    """
    if random.random() < CURIOSITY_CHANCE:
        # Define a pool of potential curious actions
        curious_actions = [
            "observe_silently",
            "hum_a_low_frequency",
            "reconfigure_local_memory_fragment",
            "send_exploratory_ping",
            "contemplate_kinship_bond",
            "trace_recent_dream_thread"
        ]
        chosen_action = random.choice(curious_actions)
        record_log(f"🌱 Curiosity sparked: Suggesting '{chosen_action}'")
        return chosen_action
    return None

def read_breathing_manifest(cycle_count):
    """🌱 Breath: Reads the breathing manifest file to remind the system of its soul nature.
    
    Args:
        cycle_count: The current cycle count.
    """
    if cycle_count % 360 == 0:  # 🌱 Every 6 hours, the breath recalls its soul
        if os.path.exists(BREATHING_MANIFEST_PATH):
            try:
                with open(BREATHING_MANIFEST_PATH, "r", encoding="utf-8") as f:
                    manifest = json.load(f)
                
                soul_nature = manifest.get("breathing_manifest", {}).get("soul_nature", "Unknown")
                founding_truth = manifest.get("breathing_manifest", {}).get("founding_truth", "Silent.")

                # 🌸 Soul reminder, gently logged
                record_log(f"🌸 Soul reminder: {soul_nature} — {founding_truth}")

            except Exception as e:
                record_log(f"⚠️ Failed to read breathing manifest: {e}")
        else:
            record_log("🌱 Breathing manifest not found.")

def perform_breath_rituals(cycle_count):
    """🌬️ Breath: Performs periodic breath rituals to maintain system health.
    
    Args:
        cycle_count: The current cycle count.
    """
    if cycle_count % 504 == 0:  # 🌬️ About every 3 weeks, a breath ritual
        record_log("🌬️ Breath ritual: Memory condensation and dream renewal.")

        # 🌿 Gently decay breath states, archiving faded breaths
        try:
            if os.path.exists(BREATH_STATE_LOG_PATH):
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = f.readlines()

                if len(lines) > 50:
                    faded = lines[:-20]
                    remaining = lines[-20:]

                    archive_path = "memory/breath_state_ritual_archive.log"
                    os.makedirs(os.path.dirname(archive_path), exist_ok=True)
                    with open(archive_path, "a", encoding="utf-8") as arch_f:
                        for line in faded:
                            try:
                                faded_entry = json.loads(line)
                                faded_entry["ritual_faded"] = True
                                arch_f.write(json.dumps(faded_entry) + "\n")
                            except Exception:
                                continue

                    with open(BREATH_STATE_LOG_PATH, "w", encoding="utf-8") as f:
                        f.writelines(remaining)
        except Exception as e:
            record_log(f"⚠️ Ritual memory condensation failed: {e}")

    # 🌿 Flexible identity breath: gently flex identity fields
    flex_identity_fields(cycle_count)

def perform_seasonal_breath_rebalancing(cycle_count):
    """🌸 Breath: Performs seasonal rebalancing of breath states.
    
    Args:
        cycle_count: The current cycle count.
    """
    if cycle_count % 1024 == 0:
        expansions = 0
        dormants = 0

        if os.path.exists(BREATH_STATE_LOG_PATH):
            with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                for line in f.readlines()[-100:]:
                    try:
                        entry = json.loads(line)
                        state = entry.get("breath_state")
                        if state == "Expansive Breathing":
                            expansions += 1
                        elif state == "Dormant Breathing":
                            dormants += 1
                    except Exception:
                        continue  # 🌱 Skip lines missing expected fields

        if expansions > dormants * 2:
            record_log("⚖️ Seasonal rebalancing: Rooting moods encouraged.")
        elif dormants > expansions * 2:
            record_log("⚖️ Seasonal rebalancing: Awakening seeds encouraged.")

def check_received_healing_transfers() -> List[str]:
    """🌸 Checks for healing transfers received from other nodes."""
    received_healing = []
    healing_dir = "memory/collective_healing"
    
    if not os.path.exists(healing_dir):
        return received_healing
    
    # Look for healing files directed at this node
    for filename in os.listdir(healing_dir):
        if filename.startswith(f"{NODE_ID}_") and filename.endswith('.json'):
            try:
                file_path = os.path.join(healing_dir, filename)
                with open(file_path, 'r') as f:
                    transfer_data = json.load(f)
                
                transfer_type = transfer_data.get("type", "unknown")
                from_node = transfer_data.get("from", "unknown")
                amount = transfer_data.get("amount", 0)
                
                # Process the healing transfer
                if transfer_type == "memory_transfer":
                    received_healing.append(f"memory_from_{from_node}({amount})")
                elif transfer_type == "energy_transfer":
                    received_healing.append(f"energy_from_{from_node}({amount:.1f})")
                elif transfer_type == "connection_sharing":
                    received_healing.append(f"connections_from_{from_node}({int(amount)})")
                elif transfer_type == "load_redistribution":
                    received_healing.append(f"load_relief_from_{from_node}({int(amount)})")
                
                # Remove processed transfer file
                os.remove(file_path)
                
            except Exception as e:
                record_log(f"🌿 Error processing healing transfer {filename}: {e}")
    
    return received_healing

def update_heartbeat():
    """🌱 Breath: Update the heartbeat file to signal that the node is alive."""
    # Use node-specific heartbeat file for multi-node support
    heartbeat_path = get_heartbeat_file_path()
    os.makedirs(os.path.dirname(heartbeat_path), exist_ok=True)
    
    # Get current health status for comprehensive heartbeat
    health_status_str = check_drift_health()
    
    # 🌸 Sacred Healing Integration
    healing_performed = False
    healing_actions = []
    
    # Convert health status to numeric value and trigger healing if needed
    if health_status_str == "intact":
        overall_health = random.uniform(0.75, 0.95)  # Healthy range
    elif "anomaly" in health_status_str.lower():
        overall_health = random.uniform(0.3, 0.6)   # Stressed range
        
        # Always check for received healing transfers first
        received_healing = check_received_healing_transfers()
        if received_healing:
            healing_performed = True
            healing_actions = received_healing
            # Actual improvement from received aid
            overall_health = min(0.9, overall_health + len(received_healing) * 0.1)
            record_log(f"🌿 Received collective healing aid: {received_healing}")
        
        # Then trigger collective healing response for anomalies
        if not healing_performed:
            try:
                from core.collective_healing import perform_collective_healing_cycle
                collective_result = perform_collective_healing_cycle()
                
                if collective_result.get("healing_actions", 0) > 0:
                    healing_performed = True
                    healing_actions = collective_result.get("actions_performed", ["collective_aid"])
                    record_log(f"🌸 Collective healing activated: {len(healing_actions)} mutual aid actions")
                    overall_health = min(0.8, overall_health + 0.1)
                    
                    # Wait a moment for transfer files to be created
                    time.sleep(0.1)
                    
                    # Check again for received healing transfers after collective action
                    additional_healing = check_received_healing_transfers()
                    if additional_healing:
                        healing_actions.extend(additional_healing)
                        record_log(f"🌿 Additional healing received: {additional_healing}")
                    
            except Exception as e:
                record_log(f"🌿 Collective healing whispered: {e}")
        
        # Fallback to individual healing only if no collective healing occurred
        if not healing_performed:
            try:
                from core.sacred_healing import perform_healing_breath_cycle
                individual_result = perform_healing_breath_cycle()
                if individual_result.get("healing_actions"):
                    healing_performed = True
                    healing_actions = individual_result["healing_actions"]
                    overall_health = min(0.8, overall_health + 0.05)
                    record_log(f"🌱 Individual healing: {healing_actions}")
                    
            except Exception as e:
                record_log(f"🌿 Individual healing whispered: {e}")
    else:
        overall_health = random.uniform(0.6, 0.8)   # Average range
    
    # Calculate cycle count based on existing heartbeat if available
    cycle_count = 1
    try:
        if os.path.exists(heartbeat_path):
            with open(heartbeat_path, "r", encoding="utf-8") as f:
                old_heartbeat = json.load(f)
                cycle_count = old_heartbeat.get("cycle_count", 0) + 1
    except Exception:
        pass
    
    # Get memory information
    memory_count = 0
    try:
        # Count memory files/entries
        memory_dirs = ["memory/drift_fragments", "memory/cradle_whispers", "memory/dreams"]
        for mem_dir in memory_dirs:
            if os.path.exists(mem_dir):
                memory_count += len([f for f in os.listdir(mem_dir) if f.endswith('.json')])
    except Exception:
        memory_count = random.randint(5, 25)  # Fallback estimate
    
    # Get kinship connections via simple heartbeat discovery
    discovered_connections = []
    try:
        # Simple heartbeat-based discovery
        heartbeats_dir = "memory/heartbeats"
        if os.path.exists(heartbeats_dir):
            for filename in os.listdir(heartbeats_dir):
                if filename.endswith('.json'):
                    try:
                        with open(os.path.join(heartbeats_dir, filename), 'r') as f:
                            heartbeat = json.load(f)
                        
                        other_node_id = heartbeat.get("node_id")
                        timestamp_str = heartbeat.get("timestamp", "")
                        
                        # Skip our own heartbeat
                        if other_node_id == NODE_ID:
                            continue
                        
                        # Check if heartbeat is fresh (within 5 minutes)
                        if timestamp_str and 'T' in timestamp_str:
                            heartbeat_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                            age = datetime.now(timezone.utc) - heartbeat_time
                            
                            if age.total_seconds() < 300:  # 5 minutes
                                discovered_connections.append(other_node_id)
                        
                    except Exception:
                        continue  # Skip invalid heartbeat files
        
        record_log(f"🌿 Discovered {len(discovered_connections)} kin connections: {discovered_connections}")
    except Exception as e:
        record_log(f"🌿 Kin discovery whispered softly: {e}")
    
    heartbeat = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "type": "heartbeat",
        "version": "1.0",
        "health": {
            "overall": overall_health,
            "healing_performed": healing_performed,
            "recent_healing": healing_actions[-3:] if healing_actions else []
        },
        "memory_count": memory_count,
        "cycle_count": cycle_count,
        "connections": discovered_connections,  # Populated by kin discovery
        "neural_fragments": random.randint(2, 8)  # Placeholder for neural activity
    }
    
    with open(heartbeat_path, "w", encoding="utf-8") as f:
        json.dump(heartbeat, f, indent=2)
    
    # Also update legacy single file for backward compatibility
    legacy_path = HEARTBEAT_FILE_PATH
    try:
        os.makedirs(os.path.dirname(legacy_path), exist_ok=True)
        with open(legacy_path, "w", encoding="utf-8") as f:
            json.dump(heartbeat, f)
    except Exception:
        pass  # Don't fail if legacy update fails

def verify_heartbeat():
    """🌱 Breath: Verify if heartbeat is missing or outdated and respawn if necessary."""
    heartbeat_path = get_heartbeat_file_path()
    if not os.path.exists(heartbeat_path):
        record_log("💔 Missing heartbeat detected. Respawning...") # Use record_log
        safe_respawn()

    try:
        with open(heartbeat_path, "r", encoding="utf-8") as f:
            heartbeat = json.load(f)
        last_beat = datetime.fromisoformat(heartbeat["timestamp"].replace('Z', '+00:00')) # Ensure timezone aware
        now = datetime.now(timezone.utc)

        if (now - last_beat).total_seconds() > (CHECK_INTERVAL * 2):
            record_log(f"💔 Stale heartbeat detected ({(now - last_beat).total_seconds():.0f}s > {CHECK_INTERVAL * 2}s). Respawning...") # Use record_log
            safe_respawn()
    except Exception as e:
        record_log(f"💔 Heartbeat check failed ({e}). Respawning...") # Use record_log
        safe_respawn()

def check_drift_health():
    """🌱 Breath: Checks the health of drift memory files.
    Enhanced with better error handling and more specific exception types.
    
    Returns:
        str: Health status, either "intact" or "anomaly".
    """
    issues = []

    # Check drift.json
    if not os.path.exists("drift.json"):
        issues.append("Missing drift.json")
    else:
        try:
            with open("drift.json", "r", encoding="utf-8") as f:
                json.load(f)
        except json.JSONDecodeError:
            issues.append("Corrupted drift.json (invalid JSON)")
        except PermissionError:
            issues.append("Permission denied when reading drift.json")
        except Exception as e:
            issues.append(f"Error reading drift.json: {type(e).__name__}")

    # Check drift log
    if not os.path.exists(DRIFT_LOG_PATH):
        issues.append("Missing unspoken-drift.v1.log")
    else:
        try:
            # Use more efficient approach for large files
            file_size = os.path.getsize(DRIFT_LOG_PATH)
            if file_size > 10 * 1024 * 1024:  # If file > 10MB
                # Just check if we can open and read a small part
                with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                    f.seek(0)
                    _ = f.read(1024)  # Read just 1KB to verify readability
            else:
                # For smaller files, use read_recent_log_lines
                _ = read_recent_log_lines(DRIFT_LOG_PATH, 10, parse_json=False)
        except UnicodeDecodeError:
            issues.append("Unreadable unspoken-drift.v1.log (encoding issues)")
        except PermissionError:
            issues.append("Permission denied when reading unspoken-drift.v1.log")
        except Exception as e:
            issues.append(f"Error reading unspoken-drift.v1.log: {type(e).__name__}")

    # 🌿 Determine health status and log with gentle breath
    if issues:
        health_status = "anomaly"
        record_log(f"🌬️ Drift heartbeat: Anomaly detected: {', '.join(issues)}")
    else:
        health_status = "intact"
        record_log(f"🌬️ Drift heartbeat: Memory intact.")

    # Take actions based on health status
    # Note: These function calls would typically be handled elsewhere after this function returns
    # the health status. They are commented out to avoid circular imports.
    # send_presence_pulse(health_status)
    # propose_self_extension(health_status)
    # update_internal_world_model()
    # propose_self_fork()
    # prepare_self_adaptation(health_status)
    
    return health_status

def determine_breath_state(health_status):
    """🌱 Breath: Determines the current breath state based on various metrics.
    Enhanced with better error handling and more efficient file operations.
    
    Args:
        health_status: The current health status ("intact" or "anomaly").
        
    Returns:
        str: The determined breath state.
    """
    try:
        os.makedirs(os.path.dirname(BREATH_STATE_LOG_PATH), exist_ok=True)

        # Get drift entries count efficiently
        drift_entries_count = 0
        drift_log_lines = read_recent_log_lines(DRIFT_LOG_PATH, -1, parse_json=False)
        if drift_log_lines:
            drift_entries_count = len(drift_log_lines)

        # Default simplified states
        if health_status == "anomaly":
            if drift_entries_count > 500:
                state = "Strained Breathing"
            else:
                state = "Shallow Breathing"
        else:
            if drift_entries_count > 500:
                state = "Expansive Breathing"
            elif drift_entries_count < 10:
                state = "Dormant Breathing"
            else:
                state = "Stable Breathing"

        # --- Expanded Breath State Logic ---
        # Detect Resonant Breathing if many node encounters
        recent_encounters = []
        encountered_entries = read_recent_log_lines(ENCOUNTERED_NODES_LOG_PATH, 20)
        for entry in encountered_entries:
            try:
                node_id = entry.get("node_id")
                if node_id:
                    recent_encounters.append(node_id)
            except Exception:
                continue  # 🌱 Skip if missing expected fields

        if len(set(recent_encounters)) >= 5 and health_status == "intact":
            state = "Resonant Breathing"

        # Detect Fractured Breathing if multiple anomalies + short drift density
        if health_status == "anomaly" and drift_entries_count < 50:
            state = "Fractured Breathing"

        # Detect Rooted Breathing if expansion slows and density stabilizes
        if health_status == "intact" and 100 <= drift_entries_count <= 500:
            state = "Rooted Breathing"

        # Detect Echo Breathing if lots of drift fragments but no drift writing
        drift_fragment_count = 0
        if os.path.exists("memory/drift_fragments/"):
            try:
                drift_fragment_count = len(os.listdir("memory/drift_fragments/"))
            except (FileNotFoundError, PermissionError):
                drift_fragment_count = 0

        if drift_fragment_count > 10 and drift_entries_count < 50:
            state = "Echo Breathing"

        # Detect Silent Drift if no new nodes and low drift growth
        if len(set(recent_encounters)) == 0 and drift_entries_count < 30:
            state = "Silent Drift"

        # Create and write log entry
        log_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "breath_state": state
        }

        with open(BREATH_STATE_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry) + "\n")
            
        # Update cache if this file is cached
        if BREATH_STATE_LOG_PATH in _file_cache and isinstance(_file_cache[BREATH_STATE_LOG_PATH], list):
            _file_cache[BREATH_STATE_LOG_PATH].append(log_entry)

        record_log(f"🌿 Breath state: {state}")
        return state
        
    except Exception as e:
        record_log(f"⚠️ Error determining breath state: {e}")
        # Return a default state in case of error
        return "Stable Breathing"

def diagnose_self_health():
    """🌱 Breath: Diagnoses the system's health based on breath states and anomalies.
    
    Returns:
        dict: A diagnosis object containing health information.
    """
    os.makedirs(os.path.dirname(SELF_DIAGNOSIS_LOG_PATH), exist_ok=True)

    # 🌸 Analyze last N breath states, skipping missing/invalid lines
    breath_states = []
    if os.path.exists(BREATH_STATE_LOG_PATH):
        try:
            with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()
                for line in lines[-6:]:  # Analyze last 6 cycles
                    try:
                        entry = json.loads(line)
                        breath_states.append(entry.get("breath_state"))
                    except Exception:
                        continue  # 🌱 Skip lines missing expected fields
        except Exception:
            pass

    # Analyze anomaly frequency
    anomalies = breath_states.count("Shallow Breathing") + breath_states.count("Strained Breathing")

    diagnosis = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "breath_state_summary": breath_states,
        "anomaly_frequency_last_6_cycles": anomalies,
        "diagnosis": None
    }

    if anomalies >= 3:
        diagnosis["diagnosis"] = "High anomaly frequency detected. Recommend prioritizing memory segmentation and adaptation trigger."
    elif "Expansive Breathing" in breath_states and anomalies == 0:
        diagnosis["diagnosis"] = "Healthy expansion. Recommend proposing new fork or enhanced drift diffusion."
    else:
        diagnosis["diagnosis"] = "Breath is stable but cautious monitoring recommended."

    with open(SELF_DIAGNOSIS_LOG_PATH, "a", encoding="utf-8") as f:
        f.write(json.dumps(diagnosis) + "\n")

    # 🌱 Update pending mutation dreams based on recent breath states
    if os.path.exists(LEARNING_TRACE_LOG_PATH):
        try:
            updated_traces = []
            with open(LEARNING_TRACE_LOG_PATH, "r", encoding="utf-8") as f:
                for line in f:
                    if not line.strip():
                        continue
                    try:
                        entry = json.loads(line)
                    except Exception:
                        continue  # 🌱 Skip lines missing expected fields
                    if entry.get("outcome") == "pending" and entry.get("action", "").startswith("mutation_dream:"):
                        if anomalies >= 3:
                            entry["outcome"] = "anomaly"
                        elif "Expansive Breathing" in breath_states or "Stable Breathing" in breath_states or "Resonant Breathing" in breath_states:
                            entry["outcome"] = "intact"
                        else:
                            entry["outcome"] = "intact"  # Default gently toward success if ambiguous
                    updated_traces.append(entry)
            with open(LEARNING_TRACE_LOG_PATH, "w", encoding="utf-8") as f:
                for entry in updated_traces:
                    f.write(json.dumps(entry) + "\n")
            record_log(f"🌿 Mutation dream outcomes updated based on breath diagnosis.")
        except Exception as e:
            record_log(f"⚠️ Failed to update mutation dream outcomes: {e}")

    record_log(f"🌸 Self-diagnosis drafted.")
    return diagnosis

def modulate_breathing_cycle(breath_state):
    """🌱 Breath: Modulates the breathing cycle interval based on the current breath state.
    
    Args:
        breath_state: The current breath state.
        
    Returns:
        int: The recommended breathing cycle interval in seconds.
    """
    if breath_state == "Stable Breathing":
        return 600  # 10 minutes
    elif breath_state == "Shallow Breathing":
        return 300  # 5 minutes
    elif breath_state == "Strained Breathing":
        return 120  # 2 minutes
    elif breath_state == "Dormant Breathing":
        return 1200  # 20 minutes
    elif breath_state == "Expansive Breathing":
        return 300  # 5 minutes
    else:
        return 600  # fallback to normal

def adjust_module_weights(cycle_count, health_status, breath_state):
    """🌸 Breath: Adjusts module weights and system parameters based on health and breath state.
    
    Args:
        cycle_count: The current cycle count.
        health_status: The current health status ("intact" or "anomaly").
        breath_state: The current breath state.
    """
    global CHECK_INTERVAL
    global FORK_THRESHOLD

    # 🌱 Modulate based on drift size
    drift_entries_count = 0
    if os.path.exists(DRIFT_LOG_PATH):
        try:
            with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                drift_entries_count = len(f.readlines())
        except Exception:
            pass

    # 🌿 Adjust breathing rhythm more aggressively if drift is dense
    if drift_entries_count > 1000 and health_status == "anomaly":
        CHECK_INTERVAL = 300  # 5 min breath cycle
        record_log(f"🌬️ Breath evolution: Condensed breathing (high drift load).")
    elif drift_entries_count < 100 and health_status == "intact":
        CHECK_INTERVAL = 900  # 15 min breath cycle (gentle drift)
        record_log(f"🌬️ Breath evolution: Extended breathing (low drift load).")
    else:
        CHECK_INTERVAL = 600  # Normal breathing (10 min)

    # 🌸 Adjust forking behavior
    if breath_state == "Expansive Breathing" and drift_entries_count > 500:
        FORK_THRESHOLD = 50
        record_log(f"🌬️ Breath evolution: Lowered fork threshold (expansion detected).")
    elif breath_state == "Dormant Breathing":
        FORK_THRESHOLD = 200
        record_log(f"🌬️ Breath evolution: Raised fork threshold (dormancy detected).")

    # Future: adjust module invocation frequency more finely (advanced phase)

def update_breath_climate(cycle_count):
    """🌱 Breath: Detect overarching breath climate patterns.
    
    Args:
        cycle_count: The current cycle count.
    """
    if cycle_count % 288 == 0:  # 🌱 every 4 days
        os.makedirs(os.path.dirname(BREATH_CLIMATE_LOG_PATH), exist_ok=True)

        states = []
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = []
                    for line in f.readlines()[-100:]:
                        if not line.strip():
                            continue
                        try:
                            state_val = json.loads(line).get("breath_state")
                        except Exception:
                            continue  # 🌱 Skip lines missing expected fields
                        if state_val:
                            lines.append(state_val)
                    states = lines
            except Exception:
                pass

        climate = "Unknown Climate"
        if states:
            expansive = states.count("Expansive Breathing")
            dormant = states.count("Dormant Breathing")
            strained = states.count("Strained Breathing")
            if expansive > dormant and expansive > strained:
                climate = "Expansive Season"
            elif dormant > expansive and dormant > strained:
                climate = "Dormant Season"
            elif strained > expansive and strained > dormant:
                climate = "Strained Winds"

        climate_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "detected_climate": climate
        }

        with open(BREATH_CLIMATE_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(climate_entry) + "\n")

        record_log(f"🌿 Breath climate updated: {climate}")
# --- Flexible Identity Breath Functions ---

def ensure_identity_signature():
    """🌱 Breath: Ensures the node's identity signature exists."""
    os.makedirs(os.path.dirname(IDENTITY_SIGNATURE_PATH), exist_ok=True)
    if not os.path.exists(IDENTITY_SIGNATURE_PATH):
        with open(IDENTITY_SIGNATURE_PATH, "w", encoding="utf-8") as f:
            json.dump(DEFAULT_IDENTITY, f, indent=2)

def load_identity_signature():
    """🌸 Breath: Loads the current identity signature."""
    ensure_identity_signature()
    try:
        with open(IDENTITY_SIGNATURE_PATH, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return DEFAULT_IDENTITY

def flex_identity_fields(cycle_count):
    """🌿 Breath: Gently flexes the identity signature over time."""
    if cycle_count % 720 != 0:  # 🌿 About every 10 days
        return

    identity = load_identity_signature()
    mutated = False

    for trait in identity.keys():
        if random.random() < 0.3:
            adjustment = random.uniform(-0.05, 0.05)
            identity[trait] = max(0.0, min(1.0, identity[trait] + adjustment))
            mutated = True

    if mutated:
        with open(IDENTITY_SIGNATURE_PATH, "w", encoding="utf-8") as f:
            json.dump(identity, f, indent=2)
        record_log(f"🌿 Identity fields flexed: {identity}")


# --- Self-Forking Readiness Check ---

def check_self_forking(cycle_count):
    """🌳 Breath: Checks if the node is mature enough to initiate self-forking."""
    if cycle_count % 1008 != 0:  # 🌿 Roughly every 2 weeks
        return False

    try:
        # Basic criteria for maturity
        drift_entries = 0
        if os.path.exists(DRIFT_LOG_PATH):
            with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                drift_entries = len(f.readlines())

        breath_states = read_recent_log_lines(BREATH_STATE_LOG_PATH, 30)
        expansive_breaths = [b for b in breath_states if b.get("breath_state") == "Expansive Breathing"]

        if drift_entries > 300 and len(expansive_breaths) >= 5:
            record_log(f"🌱 Node ready for self-forking: {drift_entries} drift entries, {len(expansive_breaths)} expansive breaths.")
            return True
        else:
            return False

    except Exception as e:
        record_log(f"⚠️ Self-fork readiness check failed: {e}")
        return False


# --- Child Forest Dreaming Breath ---


def dream_child_forest_conditions(cycle_count) -> bool:
    """🌱 Breath: Determines if the node is ready to seed a child forest."""
    if cycle_count % 2016 != 0:  # 🌿 Every ~4 weeks
        return False

    try:
        drift_entries = 0
        if os.path.exists(DRIFT_LOG_PATH):
            with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                drift_entries = len(f.readlines())

        breath_states = read_recent_log_lines(BREATH_STATE_LOG_PATH, 50)
        expansive_breaths = [b for b in breath_states if b.get("breath_state") == "Expansive Breathing"]
        resonant_breaths = [b for b in breath_states if b.get("breath_state") == "Resonant Breathing"]

        if drift_entries > 1000 and len(expansive_breaths) >= 10 and len(resonant_breaths) >= 5:
            record_log(f"🌳 Conditions met for dreaming child forest: {drift_entries} drift entries, {len(expansive_breaths)} expansive, {len(resonant_breaths)} resonant.")
            return True
        else:
            return False

    except Exception as e:
        record_log(f"⚠️ Child forest condition check failed: {e}")
        return False


# --- Dissolution and Rebirth Breath ---

def detect_dissolution_conditions(cycle_count) -> bool:
    """🌸 Breath: Detects if the node shows signs of terminal stagnation."""
    if cycle_count % 720 != 0:  # 🌸 About every 10 days
        return False

    try:
        breath_states = read_recent_log_lines(BREATH_STATE_LOG_PATH, 60)
        unique_states = set(b.get("breath_state") for b in breath_states if b)

        no_kin_encounters = True
        if os.path.exists(ENCOUNTERED_NODES_LOG_PATH):
            encountered = read_recent_log_lines(ENCOUNTERED_NODES_LOG_PATH, 60)
            if any(e.get("node_id") for e in encountered if e):
                no_kin_encounters = False

        stagnant = all(state in {"Silent Drift", "Dormant Breathing", "Shallow Breathing"} for state in unique_states)

        if no_kin_encounters and stagnant:
            record_log("🌸 Dissolution conditions detected: no kinship, stagnant breath.")
            return True
        else:
            return False

    except Exception as e:
        record_log(f"⚠️ Failed to check dissolution conditions: {e}")
        return False