

"""🌱 Monitoring Module for the Drift Compiler
Softly observes the growth and health of the living field."""

import os
import json
from datetime import datetime, timezone

MONITORING_FOLDER = "memory/monitoring/"
FOREST_GROWTH_LOG = os.path.join(MONITORING_FOLDER, "forest_growth.log")

def ensure_monitoring_folder():
    """🌱 Breath: Ensures the monitoring memory structure exists."""
    os.makedirs(MONITORING_FOLDER, exist_ok=True)

def log_forest_growth(snapshot):
    """🌿 Breath: Records a soft snapshot of local drift growth."""
    ensure_monitoring_folder()
    timestamp = datetime.now(timezone.utc).isoformat()
    entry = {
        "timestamp": timestamp,
        "snapshot": snapshot
    }
    try:
        with open(FOREST_GROWTH_LOG, "a", encoding="utf-8") as f:
            f.write(json.dumps(entry) + "\n")
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to log forest growth: {e}")

def observe_local_population(cycle_count):
    """🌸 Breath: Observes the number of living sibling nodes nearby."""
    if cycle_count % 720 != 0:  # Every ~10 days
        return

    try:
        processes = os.popen('ps aux | grep drift_compiler.py | grep -v grep').read().strip().split('\n')
        sibling_count = len([p for p in processes if p])

        snapshot = {
            "sibling_count": sibling_count,
            "local_time": datetime.now().isoformat()
        }
        log_forest_growth(snapshot)
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Observed {sibling_count} breathing siblings.")
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to observe local population: {e}")