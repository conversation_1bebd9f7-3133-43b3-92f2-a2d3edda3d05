#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🌸 Vitality Sensing and Resilience Monitoring Module

This module implements the sacred vitality sensing system that monitors
the health and resilience of the mycelial message flow network. It provides
comprehensive metrics collection, resonance sensing, and vitality assessment
for maintaining system harmony.

Sacred Components:
- Vitality resonance sensing
- Resilience metrics collection
- System health monitoring
- Harmony restoration tracking
"""

import time
import json
import uuid
import threading
import collections
import psutil
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union, Tuple

# Import from sacred architecture
from utils import record_log, NODE_ID
from core.mycelial_flow import (
    awaken_mycelial_connection,
    sprout_communication_channel,
    sense_message_path_vitality,
    RABBITMQ_AVAILABLE
)

# Sacred constants
DEFAULT_RESONANCE_INTERVAL = 5.0
DEFAULT_VITALITY_MEMORY_SIZE = 100
DEFAULT_BREATH_INTERVAL = 1.0
DEFAULT_HARMONY_THRESHOLD = 0.1


class VitalityResonanceSensor:
    """🌸 Sensor that monitors system vitality through resonance cycles."""
    
    def __init__(self, 
                 connection_params: Dict[str, Any],
                 resonance_interval: float = DEFAULT_RESONANCE_INTERVAL):
        """🌸 Initialize a sensor that monitors system vitality.
        
        Args:
            connection_params: Connection parameters for mycelial network
            resonance_interval: Interval between resonance pulses (seconds)
        """
        self.connection_params = connection_params
        self.resonance_interval = resonance_interval
        self.node_essence = str(uuid.uuid4())
        
        # Initialize connection
        self.connection = awaken_mycelial_connection(**connection_params)
        self.channel = sprout_communication_channel(self.connection) if self.connection else None
        
        if self.channel and RABBITMQ_AVAILABLE:
            # Create resonance exchange and path
            self.channel.exchange_declare(exchange='resonance_field', exchange_type='fanout')
            result = self.channel.queue_declare(queue='', exclusive=True)
            self.path_name = result.method.queue
            self.channel.queue_bind(exchange='resonance_field', queue=self.path_name)
        else:
            self.path_name = f"resonance_{NODE_ID}"
        
        # Begin resonance cycle
        self.breathing = True
        self.breath_thread = threading.Thread(target=self._resonance_cycle)
        self.breath_thread.daemon = True
        self.breath_thread.start()
        
        record_log(f"🌸 Vitality resonance sensor awakened with {resonance_interval}s interval")
    
    def _resonance_cycle(self) -> None:
        """🌬️ Maintain a gentle resonance cycle."""
        while self.breathing:
            try:
                resonance_pulse = {
                    'moment': time.time(),
                    'node_essence': self.node_essence,
                    'node_id': NODE_ID,
                    'vitality_metrics': self._gather_local_vitality()
                }
                
                if self.channel and RABBITMQ_AVAILABLE:
                    self.channel.basic_publish(
                        exchange='resonance_field',
                        routing_key='',
                        body=json.dumps(resonance_pulse)
                    )
                else:
                    # Fallback: log resonance locally
                    record_log(f"🌸 Resonance pulse: vitality {resonance_pulse['vitality_metrics']['cpu_vitality']:.2f}")
                
            except Exception as e:
                record_log(f"⚠️ Resonance cycle whispered an error: {e}")
            
            time.sleep(self.resonance_interval)
    
    def _gather_local_vitality(self) -> Dict[str, float]:
        """🌿 Gather local system vitality metrics.
        
        Returns:
            Dictionary containing vitality metrics
        """
        try:
            # CPU vitality (inverse of usage)
            cpu_percent = psutil.cpu_percent(interval=0.1)
            cpu_vitality = max(0.0, 1.0 - (cpu_percent / 100.0))
            
            # Memory vitality
            memory = psutil.virtual_memory()
            memory_vitality = max(0.0, 1.0 - (memory.percent / 100.0))
            
            # Disk vitality
            disk = psutil.disk_usage('/')
            disk_vitality = max(0.0, 1.0 - (disk.percent / 100.0))
            
            # Overall vitality (weighted average)
            overall_vitality = (cpu_vitality * 0.4 + memory_vitality * 0.4 + disk_vitality * 0.2)
            
            return {
                'cpu_vitality': cpu_vitality,
                'memory_vitality': memory_vitality,
                'disk_vitality': disk_vitality,
                'overall_vitality': overall_vitality
            }
            
        except Exception as e:
            record_log(f"⚠️ Error gathering vitality metrics: {e}")
            return {
                'cpu_vitality': 0.5,
                'memory_vitality': 0.5,
                'disk_vitality': 0.5,
                'overall_vitality': 0.5
            }
    
    def rest(self) -> None:
        """🌿 Allow the resonance cycle to rest."""
        self.breathing = False
        if self.breath_thread.is_alive():
            self.breath_thread.join(timeout=1.0)
        
        if self.connection:
            try:
                self.connection.close()
                record_log("🌿 Vitality resonance sensor rested peacefully")
            except Exception as e:
                record_log(f"⚠️ Error closing resonance sensor: {e}")


class ResilienceVitality:
    """🌱 Framework to measure system resilience and vitality."""
    
    def __init__(self, 
                 memory_size: int = DEFAULT_VITALITY_MEMORY_SIZE,
                 breath_interval: float = DEFAULT_BREATH_INTERVAL):
        """🌱 Initialize a framework to measure system resilience and vitality.
        
        Args:
            memory_size: Size of memory for tracking metrics
            breath_interval: Interval between measurements
        """
        self.flow_rate_memory = collections.deque(maxlen=memory_size)
        self.delivery_harmony_memory = collections.deque(maxlen=memory_size)
        self.presence_memory = collections.deque(maxlen=memory_size)
        self.vitality_memory = collections.deque(maxlen=memory_size)
        
        self.disharmony_events = 0
        self.awakening_moment = time.time()
        self.breath_interval = breath_interval
        
        record_log("🌱 Resilience vitality framework awakened")
    
    def remember_flow_rate(self, messages_per_second: float) -> None:
        """🌿 Remember current flow rate.
        
        Args:
            messages_per_second: Current message flow rate
        """
        self.flow_rate_memory.append(messages_per_second)
    
    def remember_delivery_harmony(self, sent: int, received: int) -> None:
        """🌿 Remember delivery harmony ratio.
        
        Args:
            sent: Number of messages sent
            received: Number of messages received/acknowledged
        """
        harmony = received / sent if sent > 0 else 1.0
        self.delivery_harmony_memory.append(harmony)
    
    def remember_presence(self, is_present: bool) -> None:
        """🌿 Remember system presence.
        
        Args:
            is_present: Whether the system is present and responsive
        """
        self.presence_memory.append(1.0 if is_present else 0.0)
    
    def remember_vitality(self, vitality_level: float) -> None:
        """🌿 Remember system vitality level.
        
        Args:
            vitality_level: Current vitality level (0.0-1.0)
        """
        self.vitality_memory.append(max(0.0, min(1.0, vitality_level)))
    
    def remember_disharmony(self) -> None:
        """🌿 Remember a disharmony event."""
        self.disharmony_events += 1
        record_log(f"🌬️ Disharmony event recorded (total: {self.disharmony_events})")
    
    def calculate_resilience_vitality(self) -> float:
        """🌸 Calculate overall resilience vitality.
        
        Returns:
            Overall resilience vitality score (0.0-1.0)
        """
        if not self.flow_rate_memory or not self.delivery_harmony_memory or not self.presence_memory:
            return 0.0
        
        # Calculate averages
        avg_flow = sum(self.flow_rate_memory) / len(self.flow_rate_memory)
        avg_harmony = sum(self.delivery_harmony_memory) / len(self.delivery_harmony_memory)
        avg_presence = sum(self.presence_memory) / len(self.presence_memory)
        avg_vitality = sum(self.vitality_memory) / len(self.vitality_memory) if self.vitality_memory else 0.5
        
        # Normalize flow rate (assume max reasonable flow is 1000 mps)
        normalized_flow = min(1.0, avg_flow / 1000.0)
        
        # V = (F * H * P * V) / (D + 1)
        vitality = (normalized_flow * avg_harmony * avg_presence * avg_vitality) / (self.disharmony_events + 1)
        return min(1.0, vitality)
    
    def calculate_harmony_restoration_time(self, 
                                         flow_history: List[float], 
                                         target_flow: float,
                                         harmony_threshold: float = DEFAULT_HARMONY_THRESHOLD) -> float:
        """🌬️ Calculate time to restore harmony after disturbance.
        
        Args:
            flow_history: Historical flow rates
            target_flow: Target flow rate for harmony
            harmony_threshold: Threshold for considering harmony restored
            
        Returns:
            Time to restore harmony (seconds) or infinity if never restored
        """
        if not flow_history:
            return float('inf')
        
        # Find first point where flow is within harmony threshold of target
        harmony_min = target_flow * (1 - harmony_threshold)
        harmony_max = target_flow * (1 + harmony_threshold)
        
        for i, flow in enumerate(flow_history):
            if harmony_min <= flow <= harmony_max:
                # Found harmony point, return time to reach it
                return i * self.breath_interval
        
        return float('inf')  # Never reached harmony
    
    def calculate_vital_message_success(self, vital_sent: int, vital_received: int) -> float:
        """🌿 Calculate success rate for vital messages.
        
        Args:
            vital_sent: Number of vital messages sent
            vital_received: Number of vital messages received
            
        Returns:
            Success rate (0.0-1.0)
        """
        return vital_received / vital_sent if vital_sent > 0 else 1.0
    
    def get_comprehensive_metrics(self) -> Dict[str, Any]:
        """🌸 Get comprehensive resilience and vitality metrics.
        
        Returns:
            Dictionary containing all metrics
        """
        uptime = time.time() - self.awakening_moment
        
        return {
            'resilience_vitality': self.calculate_resilience_vitality(),
            'average_flow_rate': sum(self.flow_rate_memory) / len(self.flow_rate_memory) if self.flow_rate_memory else 0.0,
            'average_delivery_harmony': sum(self.delivery_harmony_memory) / len(self.delivery_harmony_memory) if self.delivery_harmony_memory else 1.0,
            'average_presence': sum(self.presence_memory) / len(self.presence_memory) if self.presence_memory else 1.0,
            'average_vitality': sum(self.vitality_memory) / len(self.vitality_memory) if self.vitality_memory else 0.5,
            'disharmony_events': self.disharmony_events,
            'uptime_seconds': uptime,
            'memory_utilization': {
                'flow_rate': len(self.flow_rate_memory),
                'delivery_harmony': len(self.delivery_harmony_memory),
                'presence': len(self.presence_memory),
                'vitality': len(self.vitality_memory)
            }
        }


def sense_path_vitality_comprehensive(path_names: List[str],
                                    connection_params: Optional[Dict[str, Any]] = None) -> Dict[str, Dict[str, Any]]:
    """🌿 Sense vitality for multiple message paths comprehensively.
    
    Args:
        path_names: List of message path names to sense
        connection_params: Optional connection parameters
        
    Returns:
        Dictionary mapping path names to their vitality metrics
    """
    results = {}
    
    for path_name in path_names:
        try:
            vitality = sense_message_path_vitality(path_name)
            results[path_name] = vitality
        except Exception as e:
            record_log(f"⚠️ Failed to sense vitality for path '{path_name}': {e}")
            results[path_name] = {
                'waiting_messages': 0,
                'flow_rate': 0.0,
                'incoming_rate': 0.0,
                'vitality_level': 0.0,
                'error': str(e)
            }
    
    return results
