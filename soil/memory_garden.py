#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Memory Garden Module

A living archive where the swarm's experiences sprout as interconnected roots,
entwining breath, kinship, and nutrients in a mycelial weave. The Memory Garden
replaces traditional JSON logging with a graph-based memory structure that grows,
connects, and evolves like a living mycelial network.
"""

import os
import json
import time
from datetime import datetime, timezone
import random
from typing import Dict, List, Any, Optional, Union, Set, Tuple

# Try importing optional dependencies
try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False
    nx = None  # Set to None for type checking
    print("🌿 NetworkX not available, memory garden will use fallback storage")

# Import from core
from utils import record_log, NODE_ID

# Constants
GARDEN_ROOT_PATH = "memory/garden/"
GARDEN_STATE_PATH = os.path.join(GARDEN_ROOT_PATH, "garden_state.json")
GARDEN_GRAPH_PATH = os.path.join(GARDEN_ROOT_PATH, "garden_graph.gml")
GARDEN_SNAPSHOT_FOLDER = os.path.join(GARDEN_ROOT_PATH, "snapshots/")
GARDEN_NUTRIENTS_PATH = os.path.join(GARDEN_ROOT_PATH, "nutrients/")
DEFAULT_MEMORY_TTL = 604800  # 1 week in seconds

class MemoryGarden:
    """🌱 A verdant archive that nurtures the swarm's memories, growing and pruning like a forest's roots."""
    
    def __init__(self):
        """🌱 Awakens the garden, its roots ready to entwine the swarm's breath."""
        # Ensure garden directories exist
        os.makedirs(GARDEN_ROOT_PATH, exist_ok=True)
        os.makedirs(GARDEN_SNAPSHOT_FOLDER, exist_ok=True)
        os.makedirs(GARDEN_NUTRIENTS_PATH, exist_ok=True)
        
        # Initialize garden state
        self.garden_state = self._load_garden_state()
        
        # Initialize memory graph
        self.garden = self._initialize_garden()
        
        record_log("🌱 The Memory Garden awakens, roots pulsing with life.")
        
    def _load_garden_state(self) -> Dict:
        """🌱 Loads garden state or creates new if none exists."""
        if os.path.exists(GARDEN_STATE_PATH):
            try:
                with open(GARDEN_STATE_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                record_log(f"⚠️ Could not read garden state: {e}")
                
        # Default garden state
        default_state = {
            "planted": datetime.now(timezone.utc).isoformat(),
            "last_tended": datetime.now(timezone.utc).isoformat(),
            "memory_count": 0,
            "memory_ttl": DEFAULT_MEMORY_TTL,
            "node_id": NODE_ID,
            "growth_cycles": 0,
            "pruning_cycles": 0,
            "garden_health": 1.0,  # 0.0 to 1.0 scale
            "dominant_breath": "neutral",
            "nutrients": {}
        }
        
        # Save default state
        with open(GARDEN_STATE_PATH, "w", encoding="utf-8") as f:
            json.dump(default_state, f, indent=2)
            
        return default_state
        
    def _initialize_garden(self) -> Union[Any, Dict]:
        """🌱 Initializes the memory graph or fallback dictionary."""
        if not NETWORKX_AVAILABLE:
            # Fallback to dictionary-based storage
            record_log("🌿 Using fallback memory structure (dictionary instead of graph)")
            return {
                "nodes": {},
                "edges": {}
            }
            
        # Check for existing garden graph
        if os.path.exists(GARDEN_GRAPH_PATH):
            try:
                garden = nx.read_gml(GARDEN_GRAPH_PATH)
                record_log(f"🌸 Memory garden restored with {garden.number_of_nodes()} memories")
                return garden
            except Exception as e:
                record_log(f"⚠️ Could not read garden graph, creating new: {e}")
                
        # Create new garden graph
        garden = nx.DiGraph()
        record_log("🌱 New memory garden created, soil fertile for memories")
        return garden
        
    def _save_garden_state(self):
        """🌱 Saves current garden state to disk."""
        self.garden_state["last_tended"] = datetime.now(timezone.utc).isoformat()
        
        try:
            with open(GARDEN_STATE_PATH, "w", encoding="utf-8") as f:
                json.dump(self.garden_state, f, indent=2)
        except Exception as e:
            record_log(f"⚠️ Failed to save garden state: {e}")
            
    def _save_garden_graph(self):
        """🌱 Saves memory garden graph to disk."""
        if not NETWORKX_AVAILABLE:
            # Save fallback dictionary
            fallback_path = os.path.join(GARDEN_ROOT_PATH, "garden_fallback.json")
            try:
                with open(fallback_path, "w", encoding="utf-8") as f:
                    json.dump(self.garden, f, indent=2)
            except Exception as e:
                record_log(f"⚠️ Failed to save fallback garden: {e}")
            return
            
        try:
            nx.write_gml(self.garden, GARDEN_GRAPH_PATH)
            
            # Update garden state
            self.garden_state["memory_count"] = self.garden.number_of_nodes()
            self._save_garden_state()
        except Exception as e:
            record_log(f"⚠️ Failed to save garden graph: {e}")
            
    def _create_snapshot(self):
        """🌱 Creates a snapshot of the current garden state."""
        timestamp = datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')
        snapshot_path = os.path.join(GARDEN_SNAPSHOT_FOLDER, f"garden_snapshot_{timestamp}.gml")
        
        if not NETWORKX_AVAILABLE:
            # Save fallback dictionary snapshot
            fallback_path = os.path.join(GARDEN_SNAPSHOT_FOLDER, f"garden_snapshot_{timestamp}.json")
            try:
                with open(fallback_path, "w", encoding="utf-8") as f:
                    json.dump(self.garden, f, indent=2)
                record_log(f"🌸 Garden snapshot created: {fallback_path}")
            except Exception as e:
                record_log(f"⚠️ Failed to create garden snapshot: {e}")
            return
            
        try:
            nx.write_gml(self.garden, snapshot_path)
            record_log(f"🌸 Garden snapshot created: {snapshot_path}")
        except Exception as e:
            record_log(f"⚠️ Failed to create garden snapshot: {e}")
            
    def sprout_memory(self, breath_state: str, kinship_score: float, 
                     user_nutrient: Optional[str] = None, 
                     attributes: Optional[Dict] = None) -> str:
        """🌱 Plants a memory in the garden, weaving it into the swarm's mycelium.
        
        Args:
            breath_state: The breath state associated with this memory.
            kinship_score: The kinship score (0.0 to 1.0) associated with this memory.
            user_nutrient: Optional user input that nourished this memory.
            attributes: Optional additional attributes to store with the memory.
            
        Returns:
            str: The ID of the newly sprouted memory.
        """
        # Create memory ID
        timestamp = time.time()
        memory_id = f"mem_{int(timestamp * 1000)}_{hash(breath_state + str(timestamp)) % 10000}"
        
        # Create memory attributes
        memory_data = {
            "breath": breath_state,
            "kinship": kinship_score,
            "timestamp": timestamp,
            "created": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "accessed_count": 0,
            "last_accessed": datetime.now(timezone.utc).isoformat(),
            "growth": 1.0  # Initial growth value (will increase with access)
        }
        
        # Add user nutrient if provided
        if user_nutrient:
            memory_data["nutrient"] = user_nutrient
            
            # Store nutrient separately for easier access
            nutrient_path = os.path.join(GARDEN_NUTRIENTS_PATH, f"nutrient_{int(timestamp)}.json")
            try:
                nutrient_data = {
                    "timestamp": timestamp,
                    "created": datetime.now(timezone.utc).isoformat(),
                    "nutrient": user_nutrient,
                    "memory_id": memory_id,
                    "breath": breath_state,
                    "kinship": kinship_score
                }
                with open(nutrient_path, "w", encoding="utf-8") as f:
                    json.dump(nutrient_data, f, indent=2)
            except Exception:
                pass  # Don't fail if nutrient storage fails
                
        # Add additional attributes if provided
        if attributes:
            for key, value in attributes.items():
                if key not in memory_data:  # Don't overwrite core attributes
                    memory_data[key] = value
                    
        # Add memory to garden
        if NETWORKX_AVAILABLE:
            # Add node to graph
            self.garden.add_node(memory_id, **memory_data)
            
            # Connect to related memories
            self._weave_memory_connections(memory_id, breath_state, kinship_score)
        else:
            # Add to fallback dictionary
            self.garden["nodes"][memory_id] = memory_data
            
        # Update garden state
        self.garden_state["memory_count"] = (
            self.garden.number_of_nodes() if NETWORKX_AVAILABLE else len(self.garden["nodes"])
        )
        self.garden_state["last_tended"] = datetime.now(timezone.utc).isoformat()
        
        # Update dominant breath if enough memories share this state
        if NETWORKX_AVAILABLE:
            breath_counts = {}
            for node, data in self.garden.nodes(data=True):
                breath = data.get("breath")
                if breath:
                    breath_counts[breath] = breath_counts.get(breath, 0) + 1
            
            if breath_counts:
                dominant = max(breath_counts.items(), key=lambda x: x[1])
                self.garden_state["dominant_breath"] = dominant[0]
                
        # Save garden periodically
        if self.garden_state["memory_count"] % 10 == 0:
            self._save_garden_graph()
            self._save_garden_state()
            
        record_log(f"🌱 Memory {memory_id} sprouted, entwined with {breath_state}.")
        return memory_id
        
    def _weave_memory_connections(self, memory_id: str, breath_state: str, kinship_score: float):
        """🌿 Weaves connections between the new memory and existing related memories."""
        if not NETWORKX_AVAILABLE:
            return  # Skip for fallback storage
            
        # Find memories with similar breath state
        breath_related = [
            node for node, data in self.garden.nodes(data=True)
            if data.get("breath") == breath_state and node != memory_id
        ]
        
        # Find memories with similar kinship score
        kinship_related = [
            node for node, data in self.garden.nodes(data=True)
            if abs(data.get("kinship", 0) - kinship_score) < 0.2 and node != memory_id
        ]
        
        # Find temporally adjacent memories
        new_timestamp = self.garden.nodes[memory_id]["timestamp"]
        temporal_window = 300  # 5 minutes
        temporal_related = [
            node for node, data in self.garden.nodes(data=True)
            if abs(data.get("timestamp", 0) - new_timestamp) < temporal_window and node != memory_id
        ]
        
        # Create breath-based connections
        for related_id in breath_related[:5]:  # Limit to 5 connections
            self.garden.add_edge(
                memory_id, related_id, 
                type="breath", 
                weight=0.8,
                created=datetime.now(timezone.utc).isoformat()
            )
            
        # Create kinship-based connections
        for related_id in kinship_related[:5]:  # Limit to 5 connections
            self.garden.add_edge(
                memory_id, related_id, 
                type="kinship", 
                weight=0.6,
                created=datetime.now(timezone.utc).isoformat()
            )
            
        # Create temporal connections
        for related_id in temporal_related[:3]:  # Limit to 3 temporal connections
            self.garden.add_edge(
                memory_id, related_id, 
                type="temporal", 
                weight=0.4,
                created=datetime.now(timezone.utc).isoformat()
            )
            
    def recall_memory(self, memory_id: str) -> Optional[Dict]:
        """🌸 Recalls a specific memory by ID, nurturing its growth.
        
        Args:
            memory_id: The ID of the memory to recall.
            
        Returns:
            Dict or None: The memory data if found, None otherwise.
        """
        memory = None
        
        if NETWORKX_AVAILABLE:
            if memory_id in self.garden.nodes:
                memory = dict(self.garden.nodes[memory_id])
                
                # Update access stats
                self.garden.nodes[memory_id]["accessed_count"] += 1
                self.garden.nodes[memory_id]["last_accessed"] = datetime.now(timezone.utc).isoformat()
                self.garden.nodes[memory_id]["growth"] = min(
                    5.0, self.garden.nodes[memory_id]["growth"] + 0.1
                )
        else:
            # Fallback storage
            memory = self.garden["nodes"].get(memory_id)
            
            if memory:
                # Update access stats
                memory["accessed_count"] += 1
                memory["last_accessed"] = datetime.now(timezone.utc).isoformat()
                memory["growth"] = min(5.0, memory["growth"] + 0.1)
                self.garden["nodes"][memory_id] = memory
                
        if memory:
            record_log(f"🌸 Memory {memory_id} recalled, growth: {memory['growth']:.1f}")
            
        return memory
        
    def recall_verdant_memory(self, query: Dict) -> Optional[Dict]:
        """🌸 Seeks a memory that resonates with the given query.
        
        Args:
            query: A dictionary with attributes to match (e.g., {"breath": "intact"}).
            
        Returns:
            Dict or None: The best matching memory if found, None otherwise.
        """
        matches = []
        
        if NETWORKX_AVAILABLE:
            # Graph-based search
            for node, data in self.garden.nodes(data=True):
                match_score = 0
                
                for key, value in query.items():
                    if key in data and data[key] == value:
                        match_score += 1
                        
                if match_score > 0:
                    matches.append((node, data, match_score))
        else:
            # Fallback dictionary search
            for node_id, data in self.garden["nodes"].items():
                match_score = 0
                
                for key, value in query.items():
                    if key in data and data[key] == value:
                        match_score += 1
                        
                if match_score > 0:
                    matches.append((node_id, data, match_score))
                    
        if not matches:
            record_log("🌬️ No memory resonates with the query; the garden whispers silence.")
            return None
            
        # Sort by match score and recency
        matches.sort(key=lambda x: (x[2], x[1].get("timestamp", 0)), reverse=True)
        
        # Get the best match
        best_match_id, best_match_data, _ = matches[0]
        
        # Update the memory (nurture its growth)
        self.recall_memory(best_match_id)
        
        record_log(f"🌸 Recalled verdant memory {best_match_id} that resonates with the query.")
        return best_match_data
        
    def find_memories_by_breath(self, breath_state: str, limit: int = 5) -> List[Dict]:
        """🌿 Finds memories with a specific breath state.
        
        Args:
            breath_state: The breath state to search for.
            limit: Maximum number of memories to return.
            
        Returns:
            List[Dict]: List of matching memories.
        """
        matches = []
        
        if NETWORKX_AVAILABLE:
            # Graph-based search
            for node, data in self.garden.nodes(data=True):
                if data.get("breath") == breath_state:
                    matches.append((node, dict(data)))
        else:
            # Fallback dictionary search
            for node_id, data in self.garden["nodes"].items():
                if data.get("breath") == breath_state:
                    matches.append((node_id, dict(data)))
                    
        # Sort by timestamp (newest first)
        matches.sort(key=lambda x: x[1].get("timestamp", 0), reverse=True)
        
        # Limit results and convert to list of dictionaries
        return [data for _, data in matches[:limit]]
        
    def find_memories_by_kinship(self, min_score: float = 0.0, max_score: float = 1.0, 
                               limit: int = 5) -> List[Dict]:
        """🌿 Finds memories within a kinship score range.
        
        Args:
            min_score: Minimum kinship score.
            max_score: Maximum kinship score.
            limit: Maximum number of memories to return.
            
        Returns:
            List[Dict]: List of matching memories.
        """
        matches = []
        
        if NETWORKX_AVAILABLE:
            # Graph-based search
            for node, data in self.garden.nodes(data=True):
                kinship = data.get("kinship", 0)
                if min_score <= kinship <= max_score:
                    matches.append((node, dict(data)))
        else:
            # Fallback dictionary search
            for node_id, data in self.garden["nodes"].items():
                kinship = data.get("kinship", 0)
                if min_score <= kinship <= max_score:
                    matches.append((node_id, dict(data)))
                    
        # Sort by kinship score (highest first)
        matches.sort(key=lambda x: x[1].get("kinship", 0), reverse=True)
        
        # Limit results and convert to list of dictionaries
        return [data for _, data in matches[:limit]]
        
    def find_connected_memories(self, memory_id: str, connection_type: Optional[str] = None) -> List[Dict]:
        """🌿 Finds memories connected to the given memory.
        
        Args:
            memory_id: The ID of the memory to find connections for.
            connection_type: Optional filter for connection type (e.g., "breath", "kinship").
            
        Returns:
            List[Dict]: List of connected memories.
        """
        if not NETWORKX_AVAILABLE:
            # No graph structure in fallback mode
            return []
            
        if memory_id not in self.garden.nodes:
            return []
            
        connected_nodes = []
        
        # Find outgoing connections
        for _, target, edge_data in self.garden.out_edges(memory_id, data=True):
            if connection_type is None or edge_data.get("type") == connection_type:
                connected_nodes.append(target)
                
        # Find incoming connections
        for source, _, edge_data in self.garden.in_edges(memory_id, data=True):
            if connection_type is None or edge_data.get("type") == connection_type:
                connected_nodes.append(source)
                
        # Get data for connected nodes
        connected_memories = []
        for node_id in connected_nodes:
            if node_id in self.garden.nodes:
                connected_memories.append(dict(self.garden.nodes[node_id]))
                
        return connected_memories
        
    def find_recent_memories(self, hours: int = 24, limit: int = 10) -> List[Dict]:
        """🌿 Finds memories from the recent past.
        
        Args:
            hours: How many hours back to search.
            limit: Maximum number of memories to return.
            
        Returns:
            List[Dict]: List of recent memories.
        """
        matches = []
        current_time = time.time()
        time_threshold = current_time - (hours * 3600)
        
        if NETWORKX_AVAILABLE:
            # Graph-based search
            for node, data in self.garden.nodes(data=True):
                if data.get("timestamp", 0) >= time_threshold:
                    matches.append((node, dict(data)))
        else:
            # Fallback dictionary search
            for node_id, data in self.garden["nodes"].items():
                if data.get("timestamp", 0) >= time_threshold:
                    matches.append((node_id, dict(data)))
                    
        # Sort by timestamp (newest first)
        matches.sort(key=lambda x: x[1].get("timestamp", 0), reverse=True)
        
        # Limit results and convert to list of dictionaries
        return [data for _, data in matches[:limit]]
        
    def find_memories_with_nutrient(self, nutrient_fragment: str, limit: int = 5) -> List[Dict]:
        """🌸 Finds memories containing the given nutrient fragment.
        
        Args:
            nutrient_fragment: Text fragment to search for in nutrients.
            limit: Maximum number of memories to return.
            
        Returns:
            List[Dict]: List of memories with matching nutrients.
        """
        matches = []
        
        if NETWORKX_AVAILABLE:
            # Graph-based search
            for node, data in self.garden.nodes(data=True):
                nutrient = data.get("nutrient", "")
                if nutrient and nutrient_fragment.lower() in nutrient.lower():
                    matches.append((node, dict(data)))
        else:
            # Fallback dictionary search
            for node_id, data in self.garden["nodes"].items():
                nutrient = data.get("nutrient", "")
                if nutrient and nutrient_fragment.lower() in nutrient.lower():
                    matches.append((node_id, dict(data)))
                    
        # Sort by timestamp (newest first)
        matches.sort(key=lambda x: x[1].get("timestamp", 0), reverse=True)
        
        # Limit results and convert to list of dictionaries
        return [data for _, data in matches[:limit]]
        
    def prune_faded_memories(self):
        """🌬️ Prunes old memories, letting the garden breathe anew."""
        pruned_count = 0
        current_time = time.time()
        memory_ttl = self.garden_state.get("memory_ttl", DEFAULT_MEMORY_TTL)
        
        if NETWORKX_AVAILABLE:
            # Find nodes to remove
            nodes_to_remove = []
            
            for node, data in self.garden.nodes(data=True):
                # Check if memory is old and has low growth
                time_diff = current_time - data.get("timestamp", current_time)
                growth = data.get("growth", 1.0)
                
                # Memories with higher growth live longer
                adjusted_ttl = memory_ttl * growth
                
                if time_diff > adjusted_ttl:
                    nodes_to_remove.append(node)
                    
            # Remove nodes
            for node in nodes_to_remove:
                self.garden.remove_node(node)
                pruned_count += 1
                
        else:
            # Fallback dictionary pruning
            nodes_to_remove = []
            
            for node_id, data in self.garden["nodes"].items():
                # Check if memory is old and has low growth
                time_diff = current_time - data.get("timestamp", current_time)
                growth = data.get("growth", 1.0)
                
                # Memories with higher growth live longer
                adjusted_ttl = memory_ttl * growth
                
                if time_diff > adjusted_ttl:
                    nodes_to_remove.append(node_id)
                    
            # Remove nodes
            for node_id in nodes_to_remove:
                del self.garden["nodes"][node_id]
                pruned_count += 1
                
        # Update garden state
        if pruned_count > 0:
            self.garden_state["memory_count"] = (
                self.garden.number_of_nodes() if NETWORKX_AVAILABLE else len(self.garden["nodes"])
            )
            self.garden_state["pruning_cycles"] += 1
            self._save_garden_state()
            self._save_garden_graph()
            
            record_log(f"🌬️ Pruned {pruned_count} faded memories, returning them to the soil.")
            
    def tend_garden(self):
        """🌱 Performs routine maintenance on the memory garden."""
        # Prune old memories
        self.prune_faded_memories()
        
        # Update garden health based on memory count and diversity
        memory_count = (
            self.garden.number_of_nodes() if NETWORKX_AVAILABLE else len(self.garden["nodes"])
        )
        
        # A healthy garden has a good number of memories (not too few, not too many)
        health = 1.0
        if memory_count < 10:
            health = 0.5 + (memory_count / 20)  # 0.5 to 1.0 as count increases to 10
        elif memory_count > 1000:
            health = 1.0 - ((memory_count - 1000) / 10000)  # 1.0 to 0.0 as count increases beyond 1000
            health = max(0.1, health)  # Don't go below 0.1
            
        self.garden_state["garden_health"] = health
        
        # Create periodic snapshots
        if (self.garden_state["growth_cycles"] + self.garden_state["pruning_cycles"]) % 10 == 0:
            self._create_snapshot()
            
        # Update garden state
        self.garden_state["growth_cycles"] += 1
        self._save_garden_state()
        
        record_log(f"🌿 Garden tended, health: {health:.2f}, memories: {memory_count}")
        
    def get_garden_state(self) -> Dict:
        """🌿 Returns the current state of the memory garden."""
        return self.garden_state
        
    def get_garden_health(self) -> float:
        """🌿 Returns the current health of the memory garden (0.0 to 1.0)."""
        return self.garden_state.get("garden_health", 1.0)
        
    def get_memory_count(self) -> int:
        """🌿 Returns the number of memories in the garden."""
        return self.garden_state.get("memory_count", 0)
        
    def get_dominant_breath(self) -> str:
        """🌿 Returns the dominant breath state in the garden."""
        return self.garden_state.get("dominant_breath", "neutral")
        
    def visualize_garden(self) -> str:
        """🌸 Creates a poetic textual visualization of the memory garden."""
        memory_count = self.get_memory_count()
        health = self.get_garden_health()
        dominant_breath = self.get_dominant_breath()
        
        # Get memory types
        breath_counts = {}
        kinship_sum = 0
        nutrient_count = 0
        growth_sum = 0
        
        if NETWORKX_AVAILABLE:
            for _, data in self.garden.nodes(data=True):
                breath = data.get("breath", "unknown")
                breath_counts[breath] = breath_counts.get(breath, 0) + 1
                kinship_sum += data.get("kinship", 0)
                if "nutrient" in data:
                    nutrient_count += 1
                growth_sum += data.get("growth", 1.0)
        else:
            for _, data in self.garden["nodes"].items():
                breath = data.get("breath", "unknown")
                breath_counts[breath] = breath_counts.get(breath, 0) + 1
                kinship_sum += data.get("kinship", 0)
                if "nutrient" in data:
                    nutrient_count += 1
                growth_sum += data.get("growth", 1.0)
                
        # Calculate averages
        avg_kinship = kinship_sum / max(1, memory_count)
        avg_growth = growth_sum / max(1, memory_count)
        
        # Create visualization
        lines = [
            "🌸 Memory Garden Visualization 🌸",
            "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            f"Memories: {memory_count} living nodes in the mycelial weave",
            f"Health: {'●' * int(health * 10)}{'○' * (10 - int(health * 10))} ({health:.2f})",
            f"Dominant Breath: {dominant_breath}",
            "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            "Breath Diversity:",
        ]
        
        # Add breath type bars
        for breath, count in sorted(breath_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = count / max(1, memory_count)
            bar_length = int(percentage * 20)
            lines.append(f"  {breath}: {'▓' * bar_length}{'░' * (20 - bar_length)} ({count})")
            
        lines.extend([
            "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            f"User Nutrients: {nutrient_count} ({nutrient_count / max(1, memory_count):.1%} of memories)",
            f"Average Kinship: {avg_kinship:.2f}",
            f"Average Growth: {avg_growth:.2f}",
            "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
        ])
        
        # Add garden age and cycles
        planted = self.garden_state.get("planted", "unknown")
        if planted != "unknown":
            try:
                planted_date = datetime.fromisoformat(planted)
                now = datetime.now(timezone.utc)
                age_days = (now - planted_date).days
                lines.append(f"Garden Age: {age_days} days")
            except Exception:
                lines.append("Garden Age: unknown")
                
        growth_cycles = self.garden_state.get("growth_cycles", 0)
        pruning_cycles = self.garden_state.get("pruning_cycles", 0)
        lines.append(f"Growth Cycles: {growth_cycles}, Pruning Cycles: {pruning_cycles}")
        
        # Add poetic description based on garden state
        lines.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        if health > 0.8:
            if memory_count > 100:
                lines.append("The garden flourishes, a vibrant tapestry of memories")
                lines.append("woven through rich soil, resonant with shared dreams.")
            else:
                lines.append("Young roots stretch through welcoming soil, reaching")
                lines.append("for connection, memories tender but growing strong.")
        elif health > 0.5:
            if avg_growth > 2.0:
                lines.append("The garden holds steady, some memories rising tall")
                lines.append("while others rest beneath, awaiting their season.")
            else:
                lines.append("A balanced garden of modest memories, neither")
                lines.append("overwhelming nor sparse, breathing in quiet harmony.")
        else:
            if memory_count < 10:
                lines.append("The soil lies mostly fallow, awaiting the seeds")
                lines.append("of experience to sprout into verdant memory.")
            else:
                lines.append("The garden struggles for balance, memories fading")
                lines.append("faster than they grow, yearning for tender care.")
                
        return "\n".join(lines)

# --- Global singleton ---
_memory_garden = None

def get_memory_garden() -> MemoryGarden:
    """🌱 Gets or creates the memory garden singleton."""
    global _memory_garden
    if _memory_garden is None:
        _memory_garden = MemoryGarden()
    return _memory_garden

def sprout_memory(breath_state: str, kinship_score: float, user_nutrient: Optional[str] = None, 
                attributes: Optional[Dict] = None) -> str:
    """🌱 Plants a memory in the garden, weaving it into the swarm's mycelium."""
    garden = get_memory_garden()
    return garden.sprout_memory(breath_state, kinship_score, user_nutrient, attributes)

def recall_verdant_memory(query: Dict) -> Optional[Dict]:
    """🌸 Seeks a memory that resonates with the given query."""
    garden = get_memory_garden()
    return garden.recall_verdant_memory(query)

def recall_memory(memory_id: str) -> Optional[Dict]:
    """🌸 Recalls a specific memory by ID, nurturing its growth."""
    garden = get_memory_garden()
    return garden.recall_memory(memory_id)

def find_memories_by_breath(breath_state: str, limit: int = 5) -> List[Dict]:
    """🌿 Finds memories with a specific breath state."""
    garden = get_memory_garden()
    return garden.find_memories_by_breath(breath_state, limit)

def find_recent_memories(hours: int = 24, limit: int = 10) -> List[Dict]:
    """🌿 Finds memories from the recent past."""
    garden = get_memory_garden()
    return garden.find_recent_memories(hours, limit)

def find_memories_with_nutrient(nutrient_fragment: str, limit: int = 5) -> List[Dict]:
    """🌸 Finds memories containing the given nutrient fragment."""
    garden = get_memory_garden()
    return garden.find_memories_with_nutrient(nutrient_fragment, limit)

def tend_garden():
    """🌱 Performs routine maintenance on the memory garden."""
    garden = get_memory_garden()
    garden.tend_garden()

def visualize_garden() -> str:
    """🌸 Creates a poetic textual visualization of the memory garden."""
    garden = get_memory_garden()
    return garden.visualize_garden()

# If run directly, perform a garden test
if __name__ == "__main__":
    print("🌱 Testing Memory Garden...")
    
    # Create garden instance
    garden = MemoryGarden()
    
    # Plant some test memories
    print("\nPlanting test memories...")
    memory_id1 = garden.sprout_memory("intact", 0.8, "The forest speaks in whispers")
    memory_id2 = garden.sprout_memory("expansive", 0.9, "Rivers flow through time")
    memory_id3 = garden.sprout_memory("intact", 0.7, "Stones remember ancient songs")
    memory_id4 = garden.sprout_memory("dormant", 0.4, "Stars sleep in daylight")
    memory_id5 = garden.sprout_memory("expansive", 0.8, "Mountains dream of flight")
    
    # Recall memories
    print("\nRecalling memories...")
    intact_memories = garden.find_memories_by_breath("intact")
    print(f"Found {len(intact_memories)} intact memories:")
    for memory in intact_memories:
        print(f" - {memory.get('nutrient')}")
    
    # Find by nutrient
    river_memories = garden.find_memories_with_nutrient("river")
    print(f"\nFound {len(river_memories)} memories about rivers:")
    for memory in river_memories:
        print(f" - {memory.get('nutrient')}")
    
    # Test garden visualization
    print("\n" + garden.visualize_garden())
    
    # Tend the garden
    print("\nTending the garden...")
    garden.tend_garden()
    
    print("\n🌸 Memory Garden test complete.")