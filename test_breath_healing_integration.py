#!/usr/bin/env python3
"""🌬️ Test Breath Cycle Healing Integration"""

import os
import json
from datetime import datetime, timezone
from breath import update_heartbeat, check_drift_health

def test_breath_healing_integration():
    """🌸 Tests if healing is triggered during breath cycles."""
    print("🌬️ Testing Breath Cycle Healing Integration...")
    
    # Ensure heartbeats directory exists
    os.makedirs("memory/heartbeats", exist_ok=True)
    
    # Test health check
    print("🌱 Testing health check...")
    health_status = check_drift_health()
    print(f"🌿 Health status: {health_status}")
    
    # Test heartbeat update (this should trigger healing if needed)
    print("🌱 Testing heartbeat update with healing integration...")
    update_heartbeat()
    
    # Check if heartbeat file was created
    from utils import NODE_ID
    from breath import get_heartbeat_file_path
    
    heartbeat_path = get_heartbeat_file_path()
    
    if os.path.exists(heartbeat_path):
        with open(heartbeat_path, 'r') as f:
            heartbeat = json.load(f)
            
        print(f"🌸 Heartbeat created successfully:")
        print(f"  Node ID: {heartbeat.get('node_id')}")
        print(f"  Overall Health: {heartbeat.get('health', {}).get('overall', 'unknown')}")
        print(f"  Healing Performed: {heartbeat.get('health', {}).get('healing_performed', False)}")
        print(f"  Recent Healing: {heartbeat.get('health', {}).get('recent_healing', [])}")
        print(f"  Memory Count: {heartbeat.get('memory_count', 0)}")
        print(f"  Cycle Count: {heartbeat.get('cycle_count', 0)}")
        
        if heartbeat.get('health', {}).get('healing_performed', False):
            print("🩺 Healing was triggered during breath cycle!")
        else:
            print("🌱 No healing needed - node is healthy")
            
    else:
        print("⚠️ Heartbeat file not created")
    
    print("🌸 Breath cycle healing integration test completed!")

if __name__ == "__main__":
    test_breath_healing_integration()