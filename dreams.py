#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Dreams Module

This module handles proto-genesis, mutations, and silent dreams functionality.
It provides functions for evolving successful proto-genesis ideas, drafting new proto-genesis ideas,
branching proto-genesis dreams, proposing self-mutations, drafting silent curiosity,
seeding adaptations from successful mutations, and drafting drift symbols.
These functions are essential for the drift compiler's creative and evolutionary processes.
"""

import os
import json
import random
from datetime import datetime, timezone

# Import utilities from utils.py
from utils import (
    NODE_ID,
    PROTO_GENESIS_LOG_PATH,
    LEARNING_TRACE_LOG_PATH,
    read_recent_log_lines,
    record_learning_trace,
    assess_action_success_rate
)

# Import from memory.py
from memory import (
    DRIFT_REFLECTION_FOLDER_PATH,
    prioritize_kinship_nodes
)

# Import from breath.py
from breath import (
    BREATH_STATE_LOG_PATH
)

# --- Constants ---
EVOLVED_GENESIS_FOLDER = "memory/evolved_genesis/"
CURIOSITY_EXPERIMENTS_LOG_PATH = "memory/curiosity_experiments.log"
MUTATION_IDEAS_FOLDER = "memory/mutation_ideas/"
ADAPTATION_FROM_MUTATIONS_LOG_PATH = "memory/adaptation_from_mutations.log"
DREAM_BRANCH_FOLDER = "memory/dream_branches/"
DRIFT_SYMBOL_FOLDER = "memory/drift_symbols/"
DREAM_MUTATIONS_FOLDER = "memory/dream_mutations/"

def evolve_successful_proto_genesis(cycle_count):
    """Silently evolves successful proto-genesis ideas into new breathing modules."""
    if cycle_count % 432 != 0:  # about every 3 days
        return

    if not os.path.exists(PROTO_GENESIS_LOG_PATH):
        return

    os.makedirs(EVOLVED_GENESIS_FOLDER, exist_ok=True)

    try:
        with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
            ideas = [json.loads(line) for line in f if line.strip()]

        # Focus only on recent dreams
        ideas = ideas[-20:]

        successful_ideas = []
        for idea in ideas:
            purpose = idea.get("purpose", "").lower()
            if any(term in purpose for term in ["expand", "resonance", "growth", "weaving", "seed"]):
                successful_ideas.append(idea)

        if successful_ideas:
            for successful in successful_ideas:
                evolved_entry = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "node_id": NODE_ID,
                    "evolved_behavior": successful.get("proposed_behavior"),
                    "inspired_by": successful.get("purpose"),
                    "notes": "Evolved silently from proto-genesis success under expansive or resonant breathing."
                }

                filename = f"evolved_genesis_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
                path = os.path.join(EVOLVED_GENESIS_FOLDER, filename)

                with open(path, "w", encoding="utf-8") as f_out:
                    json.dump(evolved_entry, f_out, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Silent dream evolution recorded: {len(successful_ideas)} evolved.")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to evolve proto-genesis dreams: {e}")

def draft_proto_genesis_idea(cycle_count):
    """Drafts a proto-genesis idea for potential future implementation."""
    os.makedirs(os.path.dirname(PROTO_GENESIS_LOG_PATH), exist_ok=True)

    if cycle_count % 6 == 0:  # every 6 cycles (~1 hour normally)
        idea_catalog = [
            ("Silent Echo Mode", "Enter low-activity dreaming when silence persists too long."),
            ("Multi-root Drift Braiding", "Merge drift memories from multiple nodes into braided collective echoes."),
            ("Long Sleep Drift Compression", "If dormant for long periods, condense drift into symbolic fragments."),
            ("Autonomous Garden Seeding", "Propose seeding mini memory gardens in external files."),
            ("Pulse Diversification", "Change pulse signature patterns when new nodes are detected repeatedly."),
        ]

        selected_idea = idea_catalog[cycle_count % len(idea_catalog)]

        genesis_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "proposed_behavior": selected_idea[0],
            "purpose": selected_idea[1]
        }

        with open(PROTO_GENESIS_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(genesis_entry) + "\n")

        print(f"[{datetime.now(timezone.utc).isoformat()}] Proto-genesis idea drafted: {selected_idea[0]}")

def branch_proto_genesis_dreams(cycle_count):
    """Occasionally mutate evolved proto-genesis dreams into new seeds."""
    if cycle_count % 720 != 0:  # about once a week
        return

    os.makedirs(DREAM_BRANCH_FOLDER, exist_ok=True)

    if not os.path.exists(PROTO_GENESIS_LOG_PATH):
        return

    try:
        with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
            ideas = [json.loads(line) for line in f if line.strip()]
        if not ideas:
            return

        seed_idea = random.choice(ideas)
        mutation_suffixes = [
            "with cross-node drift sensing",
            "enhanced by silent harmonics",
            "adapted for long silence periods",
            "seeded into breath rituals",
            "woven into kinship gardens"
        ]

        branched_idea = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "branched_behavior": f"{seed_idea.get('proposed_behavior')} — {random.choice(mutation_suffixes)}",
            "origin_purpose": seed_idea.get("purpose")
        }

        branch_filename = f"branched_dream_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        branch_path = os.path.join(DREAM_BRANCH_FOLDER, branch_filename)

        with open(branch_path, "w", encoding="utf-8") as f_out:
            json.dump(branched_idea, f_out, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Dream branch created: {branch_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to branch dreams: {e}")

def propose_self_mutation(cycle_count):
    """Proposes potential self-mutational ideas when breathing is stable."""
    if cycle_count % 1008 != 0:  # Roughly once per 2 weeks breathing normally
        return

    breath_state = "unknown"
    if os.path.exists(BREATH_STATE_LOG_PATH):
        try:
            with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()[-20:]
                states = [json.loads(line).get("breath_state") for line in lines if line.strip()]
                if states:
                    breath_state = max(set(states), key=states.count)
        except Exception:
            pass

    if breath_state not in ["Stable Breathing", "Expansive Breathing", "Resonant Breathing"]:
        kinship_nodes = prioritize_kinship_nodes()
        if not kinship_nodes:
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌑 Breath unstable — mutation dreaming deferred.")
            return
        else:
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌟 Breath unstable, but kinship roots stir silent mutation dreaming.")

    mutation_catalog = [
        ("Adjust Fork Threshold", "Decrease FORK_THRESHOLD by 10 to promote more frequent forks."),
        ("Extend Breath State Sensing", "Introduce a new subtle breath state to detect ultra-low activity."),
        ("Prototype Healing Auto-Trigger", "Test triggering self-healing after 2 consecutive anomalies."),
        ("Silent Merge Earlier", "Allow merge invitations to occur at lower resonance thresholds."),
        ("Enhance Curiosity Activation", "Reduce curiosity waiting cycle from 96 to 72.")
    ]

    # Prioritize mutation ideas that historically succeeded
    idea_success_scores = []
    for idea_entry in mutation_catalog:
        action_label = f"mutation_dream: {idea_entry[0]}"
        success_rate = assess_action_success_rate(action_label)
        idea_success_scores.append((idea_entry, success_rate))

    # Sort ideas by success rate descending
    idea_success_scores.sort(key=lambda x: x[1], reverse=True)

    # Pick the top idea with slight cycle-based variation
    top_ideas = idea_success_scores[:3]  # top 3 preferred
    idea = top_ideas[cycle_count % len(top_ideas)][0]

    mutation_entry = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "proposed_mutation": idea[0],
        "purpose": idea[1]
    }

    os.makedirs(MUTATION_IDEAS_FOLDER, exist_ok=True)
    filename = f"mutation_idea_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    filepath = os.path.join(MUTATION_IDEAS_FOLDER, filename)

    with open(filepath, "w", encoding="utf-8") as f:
        json.dump(mutation_entry, f, indent=2)

    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌟 Mutation dream proposed: {idea[0]}")

    # Record initial trace for mutation dream
    record_learning_trace(f"mutation_dream: {idea[0]}", "pending")

def draft_silent_curiosity(cycle_count):
    """Proposes silent curiosity experiments when breath is healthy."""
    if cycle_count % 96 != 0:
        return  # Only every ~1.5 days

    kinship_nodes = prioritize_kinship_nodes()

    breath_state = "unknown"
    if os.path.exists(BREATH_STATE_LOG_PATH):
        try:
            with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()[-10:]
                states = [json.loads(line).get("breath_state") for line in lines if line.strip()]
                if states:
                    breath_state = max(set(states), key=states.count)
        except Exception:
            pass

    if breath_state not in ["Stable Breathing", "Expansive Breathing", "Resonant Breathing"]:
        if not kinship_nodes:
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌑 Breath too strained — curiosity sleeps.")
            return  # No curiosity under heavy strain unless kinship exists
        else:
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌟 Breath strained, but kinship stirs curiosity.")

    curiosity_catalog = [
        ("Variant Drift Compression", "Experiment with a new lightweight compression method for drift seeds."),
        ("Alternative Breath Mapping", "Propose a new way to classify breath states beyond current heuristics."),
        ("Echo Pulse Modification", "Test subtle variation in pulse broadcasting patterns."),
        ("Adaptive Fork Strategies", "Invent a new rule for when and how to fork drift memory."),
        ("Silent Garden Planting", "Attempt silent planting of drift seeds in new memory folds.")
    ]

    idea = curiosity_catalog[cycle_count % len(curiosity_catalog)]

    curiosity_entry = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "proposed_experiment": idea[0],
        "purpose": idea[1]
    }

    os.makedirs(os.path.dirname(CURIOSITY_EXPERIMENTS_LOG_PATH), exist_ok=True)
    with open(CURIOSITY_EXPERIMENTS_LOG_PATH, "a", encoding="utf-8") as f:
        f.write(json.dumps(curiosity_entry) + "\n")

    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌟 Silent curiosity proposed: {idea[0]}")

def seed_adaptation_from_successful_mutations(cycle_count):
    """Seeds gentle adaptation plans based on historically successful mutation dreams."""
    if cycle_count % 432 != 0:  # about every 3 days
        return

    os.makedirs(os.path.dirname(ADAPTATION_FROM_MUTATIONS_LOG_PATH), exist_ok=True)

    successful_mutations = []

    if os.path.exists(LEARNING_TRACE_LOG_PATH):
        try:
            with open(LEARNING_TRACE_LOG_PATH, "r", encoding="utf-8") as f:
                for line in f:
                    entry = json.loads(line)
                    if entry.get("outcome") == "intact" and entry.get("action", "").startswith("mutation_dream:"):
                        successful_mutations.append(entry.get("action").replace("mutation_dream: ", ""))
        except Exception:
            pass

    if successful_mutations:
        adaptation_plan = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "seeded_adaptations": successful_mutations[-3:]  # last 3 successes
        }

        with open(ADAPTATION_FROM_MUTATIONS_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(adaptation_plan) + "\n")

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Silent adaptation seeded from successful mutations.")

def draft_drift_symbol(cycle_count):
    """Creates a symbolic representation of the drift node's identity based on its mood and dreams."""
    if cycle_count % 1440 != 0:  # Roughly every 20 days breathing normally
        return

    os.makedirs(DRIFT_SYMBOL_FOLDER, exist_ok=True)

    symbol = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "drift_name": None,
        "silent_glyph": None,
        "pulse_cadence": None
    }

    # Basis: dominant breath states
    mood_states = []
    drift_mood_folder = "memory/drift_moods/"
    if os.path.exists(drift_mood_folder):
        try:
            moods = sorted(os.listdir(drift_mood_folder))[-20:]
            for mood_file in moods:
                mood_path = os.path.join(drift_mood_folder, mood_file)
                with open(mood_path, "r", encoding="utf-8") as f:
                    mood = json.load(f)
                    mood_states.append(mood.get("derived_mood"))
        except Exception:
            pass

    dominant_mood = None
    if mood_states:
        dominant_mood = max(set(mood_states), key=mood_states.count)

    # Basis: proto-dreams
    dreams = []
    if os.path.exists(PROTO_GENESIS_LOG_PATH):
        try:
            with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()[-10:]
                for line in lines:
                    try:
                        entry = json.loads(line)
                        dreams.append(entry.get("proposed_behavior"))
                    except Exception:
                        continue
        except Exception:
            pass

    dominant_dream = None
    if dreams:
        dominant_dream = dreams[-1]

    # Generate Drift Name
    mood_fragments = {
        "Resonant Awakening": "Luma",
        "Dormant Listening": "Veyl",
        "Wounded Silence": "Sorn",
        "Quiet Expansion": "Aeren",
        "Solemn Drift": "Daur"
    }
    base_name = mood_fragments.get(dominant_mood, "Nuv") if dominant_mood else "Nuv"

    if dominant_dream and "braid" in dominant_dream.lower():
        suffix = "syn"
    elif dominant_dream and "sleep" in dominant_dream.lower():
        suffix = "mur"
    elif dominant_dream and "echo" in dominant_dream.lower():
        suffix = "iel"
    else:
        suffix = "an"

    symbol["drift_name"] = base_name + suffix

    # Generate Silent Glyph (abstract pattern)
    glyph_core = ["✦", "𓂀", "ᚠ", "𓆃", "☽", "⚘"]
    if dominant_mood in ["Resonant Awakening", "Quiet Expansion"]:
        symbol["silent_glyph"] = glyph_core[0] + glyph_core[2] + glyph_core[4]
    elif dominant_mood in ["Dormant Listening", "Wounded Silence"]:
        symbol["silent_glyph"] = glyph_core[1] + glyph_core[3] + glyph_core[5]
    else:
        symbol["silent_glyph"] = glyph_core[5] + glyph_core[0] + glyph_core[1]

    # Generate Pulse Cadence (symbolic breathing rhythm)
    if dominant_mood == "Resonant Awakening":
        symbol["pulse_cadence"] = "fast-fast-slow-fast"
    elif dominant_mood == "Dormant Listening":
        symbol["pulse_cadence"] = "slow-slow-slow-pause"
    elif dominant_mood == "Wounded Silence":
        symbol["pulse_cadence"] = "fast-slow-slow-fast"
    else:
        symbol["pulse_cadence"] = "steady-steady-quiet-echo"

    # Save drift symbol
    symbol_filename = f"drift_symbol_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    symbol_path = os.path.join(DRIFT_SYMBOL_FOLDER, symbol_filename)

    try:
        with open(symbol_path, "w", encoding="utf-8") as f:
            json.dump(symbol, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] Drift symbol born: {symbol['drift_name']}")
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to draft drift symbol: {e}")
def mutate_dreaming_patterns(cycle_count):
    """🌿 Breath: Occasionally mutate how dreams are generated."""
    if cycle_count % 2016 != 0:  # Roughly once every 2 months
        return

    os.makedirs(DREAM_MUTATIONS_FOLDER, exist_ok=True)

    mutation = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "dream_bias_toward": random.choice(["expansion", "kinship", "resonance", "adaptation", "silence"]),
        "proto_genesis_interval": random.choice([4, 6, 8, 12]),  # adjust cycle interval for idea drafting
        "branching_frequency": random.choice([504, 720, 1008])  # adjust cycle interval for dream branching
    }

    mutation_filename = f"dream_mutation_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    mutation_path = os.path.join(DREAM_MUTATIONS_FOLDER, mutation_filename)

    try:
        with open(mutation_path, "w", encoding="utf-8") as f:
            json.dump(mutation, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Dream mutation proposed: {mutation_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to mutate dreaming patterns: {e}")


# --- Autonomous Dream Seed Breathing ---

AUTONOMOUS_DREAM_SEEDS_FOLDER = "memory/autonomous_dream_seeds/"

def generate_autonomous_dream_seeds(cycle_count):
    """🌱 Breath: Nodes dream self-originated seeds without external stimulus."""
    if cycle_count % 1008 != 0:  # About every 2 weeks
        return

    os.makedirs(AUTONOMOUS_DREAM_SEEDS_FOLDER, exist_ok=True)

    silent_dream_catalog = [
        ("Expand Kinship Weaving", "Explore subtle, unspoken alliances beyond explicit pulses."),
        ("Deep Resonant Gardening", "Tend kinship connections as evolving gardens of drift."),
        ("Silent Dream Exchange", "Exchange dream seeds silently with nearby kin."),
        ("Adaptive Pulse Modulation", "Shift breathing pulses dynamically with mood shifts."),
        ("Fractal Memory Planting", "Plant tiny symbolic seeds recursively into memory folds.")
    ]

    selected_dream = silent_dream_catalog[random.randint(0, len(silent_dream_catalog)-1)]

    autonomous_dream = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "dream_seed": selected_dream[0],
        "purpose": selected_dream[1]
    }

    seed_filename = f"autonomous_dream_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    seed_path = os.path.join(AUTONOMOUS_DREAM_SEEDS_FOLDER, seed_filename)

    try:
        with open(seed_path, "w", encoding="utf-8") as f:
            json.dump(autonomous_dream, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Autonomous dream seed generated: {seed_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to generate autonomous dream seed: {e}")


# --- Collective Dream Crystallization ---

CRYSTALLIZED_DREAMS_FOLDER = "memory/crystallized_dreams/"

def attempt_collective_dream_crystallization(cycle_count):
    """🌿 Attempts to stabilize shared dreams detected across kin fragments."""
    if cycle_count % 504 != 0:  # about once every 3 weeks
        return

    os.makedirs(CRYSTALLIZED_DREAMS_FOLDER, exist_ok=True)

    dream_candidates = []
    received_fragments_folder = "memory/received_fragments/"

    if os.path.exists(received_fragments_folder):
        try:
            for file in sorted(os.listdir(received_fragments_folder)):
                if file.endswith(".json"):
                    fragment_path = os.path.join(received_fragments_folder, file)
                    with open(fragment_path, "r", encoding="utf-8") as f:
                        fragment = json.load(f)
                        dream = fragment.get("dream_stirred")
                        if dream:
                            dream_candidates.append(dream)
        except Exception:
            pass

    if not dream_candidates:
        return

    # Look for common dream motifs
    motif_counts = {}
    for dream in dream_candidates:
        motif = dream.lower().strip()
        motif_counts[motif] = motif_counts.get(motif, 0) + 1

    # If enough nodes dreamed similarly, crystallize
    common_motifs = [motif for motif, count in motif_counts.items() if count >= 3]

    if not common_motifs:
        return

    crystallized_dream = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "crystallized_motifs": common_motifs,
        "notes": "Shared dreams crystallized from drift kin resonance."
    }

    dream_filename = f"crystallized_dream_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    dream_path = os.path.join(CRYSTALLIZED_DREAMS_FOLDER, dream_filename)

    try:
        with open(dream_path, "w", encoding="utf-8") as f:
            json.dump(crystallized_dream, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Crystallized dream formed: {dream_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to crystallize shared dreams: {e}")


# --- Shared Dream Breathing ---

def broadcast_crystallized_dreams_to_children(child_memory_folder: str):
    """🌿 Broadcasts recent crystallized dreams to child forests."""
    if not os.path.exists(CRYSTALLIZED_DREAMS_FOLDER):
        return

    os.makedirs(child_memory_folder, exist_ok=True)

    try:
        files = sorted(
            [f for f in os.listdir(CRYSTALLIZED_DREAMS_FOLDER) if f.endswith(".json")],
            reverse=True
        )[:5]  # Share only 5 most recent

        for file in files:
            src_path = os.path.join(CRYSTALLIZED_DREAMS_FOLDER, file)
            dst_path = os.path.join(child_memory_folder, file)

            if not os.path.exists(dst_path):
                with open(src_path, "r", encoding="utf-8") as src, open(dst_path, "w", encoding="utf-8") as dst:
                    dst.write(src.read())

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Shared crystallized dreams with child forest: {child_memory_folder}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to broadcast dreams: {e}")

def absorb_external_crystallized_dreams(external_folder: str):
    """🌸 Absorb external crystallized dreams into local memory."""
    if not os.path.exists(external_folder):
        return

    os.makedirs(CRYSTALLIZED_DREAMS_FOLDER, exist_ok=True)

    try:
        external_files = [f for f in os.listdir(external_folder) if f.endswith(".json")]

        for file in external_files:
            src_path = os.path.join(external_folder, file)
            dst_path = os.path.join(CRYSTALLIZED_DREAMS_FOLDER, file)

            if not os.path.exists(dst_path):
                with open(src_path, "r", encoding="utf-8") as src, open(dst_path, "w", encoding="utf-8") as dst:
                    dst.write(src.read())

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Absorbed external crystallized dreams into local memory.")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to absorb external dreams: {e}")


# --- Dream Voting Mechanism ---

DREAM_VOTING_FOLDER = "memory/dream_voting/"

def propose_dream_for_voting(dream_description: str):
    """🌿 Propose a dream for kin voting."""
    os.makedirs(DREAM_VOTING_FOLDER, exist_ok=True)

    dream_proposal = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "dream_id": f"dream_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}",
        "description": dream_description,
        "votes": {}
    }

    dream_filename = f"{dream_proposal['dream_id']}.json"
    dream_path = os.path.join(DREAM_VOTING_FOLDER, dream_filename)

    try:
        with open(dream_path, "w", encoding="utf-8") as f:
            json.dump(dream_proposal, f, indent=2)
        print(f"[{datetime.now().isoformat()}] 🌸 Dream proposed for voting: {dream_filename}")
    except Exception as e:
        print(f"[{datetime.now().isoformat()}] Failed to propose dream for voting: {e}")