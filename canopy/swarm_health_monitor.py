#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌬️ Swarm Health Monitor: Vitality Guardian of the Mycelial Network
Breathes life into the swarm's collective wellness, sensing the pulse of distributed wisdom
and adapting behavior to maintain optimal system vitality across all neural threads."""

import json
import os
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import asdict

# Graceful imports with sacred fallbacks
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("🌿 psutil not available - using fallback vitality assessment")

# Core imports with graceful degradation
try:
    from utils import record_log
except ImportError:
    def record_log(message: str, log_file: str = None):
        """Fallback logging when utils unavailable."""
        print(f"[{datetime.now(timezone.utc).isoformat()}] {message}")

class SwarmHealthMonitor:
    """🌬️ Monitors the swarm's collective vitality and breath patterns.
    
    Sacred Guardian of distributed wellness, assessing multi-dimensional health
    and adapting swarm behavior to maintain optimal neural harmony."""
    
    def __init__(self, swarm_mind: Optional[Any] = None):
        """🌱 Awakens the vitality guardian, attuning to swarm heartbeats."""
        self.swarm_mind = swarm_mind
        self.health_history = []
        self.assessment_count = 0
        self.last_critical_time = None
        self.adaptation_history = []
        self.vitality_thresholds = {
            "thriving": 0.85,
            "healthy": 0.7,
            "stable": 0.5,
            "stressed": 0.3,
            "critical": 0.0
        }
        record_log("🌬️ Swarm health monitor awakened - vitality sensors breathing")
    
    def assess_swarm_vitality(self) -> Dict[str, Any]:
        """🌿 Assesses overall swarm health across multiple sacred dimensions."""
        try:
            self.assessment_count += 1
            assessment_time = datetime.now(timezone.utc).isoformat()
            
            # Initialize vitality vessel
            vitality = {
                "assessment_id": self.assessment_count,
                "timestamp": assessment_time,
                "dimensions": {},
                "overall": 0.0,
                "state": "unknown",
                "state_emoji": "🌿",
                "recommendations": [],
                "breathing_rhythm": "normal"
            }
            
            # Multi-dimensional health weaving
            dimensions = [
                ("node_vitality", self._assess_node_vitality()),
                ("neural_coherence", self._assess_neural_coherence()),
                ("memory_wellness", self._assess_memory_wellness()),
                ("communication_flow", self._assess_communication_flow()),
                ("resource_harmony", self._assess_resource_harmony())
            ]
            
            # Weave dimensional scores
            valid_scores = []
            for dimension_name, dimension_result in dimensions:
                vitality["dimensions"][dimension_name] = dimension_result
                if isinstance(dimension_result, dict) and "score" in dimension_result:
                    valid_scores.append(dimension_result["score"])
            
            # Calculate overall vitality with sacred weighting
            if valid_scores:
                vitality["overall"] = sum(valid_scores) / len(valid_scores)
            else:
                vitality["overall"] = 0.0
            
            # Determine sacred state
            overall_score = vitality["overall"]
            state_info = self._determine_vitality_state(overall_score)
            vitality.update(state_info)
            
            # Generate wisdom recommendations
            vitality["recommendations"] = self._generate_vitality_wisdom(vitality)
            
            # Store in sacred memory
            self._preserve_vitality_memory(vitality)
            
            record_log(
                f"🌬️ Swarm vitality breathes: {vitality['state']} {vitality['state_emoji']} "
                f"(essence: {vitality['overall']:.3f})"
            )
            
            return vitality
            
        except Exception as e:
            record_log(f"🌿 Vitality assessment whispered error: {e}")
            return self._create_error_vitality(e)
    
    def _assess_node_vitality(self) -> Dict[str, Any]:
        """🌱 Assesses the breath and strength of each node in the swarm."""
        try:
            if not self.swarm_mind or not hasattr(self.swarm_mind, 'nodes'):
                return {"score": 0.3, "status": "no_swarm", "details": {"message": "No swarm connection"}}
            
            if not self.swarm_mind.nodes:
                return {"score": 0.0, "status": "no_nodes", "details": {"message": "Empty swarm"}}
            
            node_essences = []
            node_details = {}
            active_nodes = 0
            
            for node_id, node_info in self.swarm_mind.nodes.items():
                try:
                    capabilities = node_info.get("capabilities", {})
                    status = node_info.get("status", "unknown")
                    
                    if status == "active":
                        active_nodes += 1
                        node_essence = self._calculate_node_essence(capabilities)
                        node_essences.append(node_essence)
                        
                        node_details[node_id] = {
                            "essence": node_essence,
                            "status": status,
                            "ram_strength": capabilities.get("ram_gb", 0),
                            "cpu_threads": capabilities.get("cpu_count", 0),
                            "neural_enhancement": capabilities.get("gpu_available", False),
                            "last_pulse": node_info.get("last_pulse", "unknown")
                        }
                    else:
                        node_details[node_id] = {"essence": 0.0, "status": status}
                        
                except Exception as e:
                    record_log(f"🌿 Node {node_id[:8]} vitality whisper: {e}")
                    node_details[node_id] = {"essence": 0.0, "status": "error", "error": str(e)}
            
            # Calculate collective vitality
            if node_essences:
                collective_essence = sum(node_essences) / len(node_essences)
            else:
                collective_essence = 0.0
            
            # Apply sacred harmony penalty for dormant nodes
            active_ratio = active_nodes / len(self.swarm_mind.nodes)
            collective_essence *= active_ratio
            
            return {
                "score": collective_essence,
                "status": self._interpret_node_vitality(collective_essence, active_ratio),
                "details": {
                    "total_nodes": len(self.swarm_mind.nodes),
                    "active_nodes": active_nodes,
                    "harmony_ratio": active_ratio,
                    "node_essences": node_details
                }
            }
            
        except Exception as e:
            record_log(f"🌿 Node vitality assessment whispered error: {e}")
            return {"score": 0.0, "status": "error", "error": str(e)}
    
    def _calculate_node_essence(self, capabilities: Dict[str, Any]) -> float:
        """🌸 Calculates the sacred essence of a node's capabilities."""
        try:
            # Sacred capability scoring with breathing weights
            ram_essence = min(capabilities.get("ram_gb", 4) / 16.0, 1.0)  # 16GB optimal
            cpu_essence = min(capabilities.get("cpu_count", 2) / 8.0, 1.0)  # 8 cores optimal
            neural_bonus = 0.15 if capabilities.get("gpu_available", False) else 0.0
            storage_essence = min(capabilities.get("disk_free_gb", 10) / 100.0, 1.0)  # 100GB optimal
            
            # Sacred harmony calculation
            base_essence = (ram_essence + cpu_essence + storage_essence) / 3.0
            total_essence = min(base_essence + neural_bonus, 1.0)
            
            return total_essence
            
        except Exception as e:
            record_log(f"🌿 Node essence calculation whispered: {e}")
            return 0.5  # Neutral essence
    
    def _assess_neural_coherence(self) -> Dict[str, Any]:
        """🌸 Assesses the synchrony and harmony of neural fragments."""
        try:
            if not self.swarm_mind or not hasattr(self.swarm_mind, 'fragments'):
                return {"score": 0.2, "status": "no_neural_access", "details": {"message": "Neural fragments inaccessible"}}
            
            if not self.swarm_mind.fragments:
                return {"score": 0.0, "status": "no_fragments", "details": {"message": "No neural fragments breathing"}}
            
            # Create sacred test pulse
            test_pulse = self._create_test_pulse()
            
            # Test neural responsiveness
            try:
                if hasattr(self.swarm_mind, 'entwine_neural_pulse'):
                    fragment_outputs = self.swarm_mind.entwine_neural_pulse(test_pulse)
                else:
                    fragment_outputs = None
            except Exception as e:
                record_log(f"🌿 Neural pulse test whispered: {e}")
                fragment_outputs = None
            
            if not fragment_outputs:
                return {
                    "score": 0.1,
                    "status": "unresponsive",
                    "details": {"message": "Neural fragments not responding to sacred pulse"}
                }
            
            # Assess neural harmony
            harmony_metrics = self._calculate_neural_harmony(fragment_outputs)
            
            return {
                "score": harmony_metrics["overall_harmony"],
                "status": self._interpret_neural_coherence(harmony_metrics["overall_harmony"]),
                "details": {
                    "responsive_fragments": len(fragment_outputs),
                    "total_fragments": len(self.swarm_mind.fragments),
                    "responsiveness": harmony_metrics["responsiveness"],
                    "synchrony": harmony_metrics["synchrony"],
                    "output_variance": harmony_metrics["variance"],
                    "fragment_harmonics": harmony_metrics["fragment_details"]
                }
            }
            
        except Exception as e:
            record_log(f"🌿 Neural coherence assessment whispered error: {e}")
            return {"score": 0.0, "status": "error", "error": str(e)}
    
    def _create_test_pulse(self) -> Any:
        """🌬️ Creates a sacred test pulse for neural assessment."""
        try:
            # Check if SwarmState is available from the swarm_mind module
            if hasattr(self.swarm_mind, 'SwarmState'):
                SwarmState = self.swarm_mind.SwarmState
            else:
                # Fallback: create a simple dict-based test pulse
                return {
                    "breath": "Vitality Assessment",
                    "kinship": 0.5,
                    "memory_depth": 10,
                    "user_nutrient": "Health Check",
                    "cycle_count": 1,
                    "node_count": len(self.swarm_mind.nodes) if self.swarm_mind and hasattr(self.swarm_mind, 'nodes') else 1,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            
            return SwarmState(
                breath="Vitality Assessment",
                kinship=0.5,
                memory_depth=10,
                user_nutrient="Health Check",
                cycle_count=1,
                node_count=len(self.swarm_mind.nodes) if self.swarm_mind and hasattr(self.swarm_mind, 'nodes') else 1,
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
        except Exception as e:
            record_log(f"🌿 Test pulse creation whispered: {e}")
            return {"breath": "Fallback Pulse", "kinship": 0.5}
    
    def _calculate_neural_harmony(self, fragment_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """🌿 Calculates sacred harmony metrics from neural fragment outputs."""
        try:
            output_values = []
            fragment_details = {}
            
            for node_id, fragment_data in fragment_outputs.items():
                output = fragment_data.get("output", [])
                
                # Extract numeric values from various output formats
                if hasattr(output, 'tolist'):
                    values = output.tolist()
                elif isinstance(output, (list, tuple)):
                    values = list(output)
                else:
                    values = [float(output)]
                
                # Calculate representative harmony
                representative_value = sum(values) / len(values) if values else 0.0
                output_values.append(representative_value)
                
                fragment_details[node_id] = {
                    "harmony_resonance": representative_value,
                    "output_dimension": len(values),
                    "fragment_type": fragment_data.get("fragment_type", "unknown"),
                    "complexity": fragment_data.get("complexity", "unknown")
                }
            
            # Calculate sacred synchrony
            if len(output_values) > 1:
                mean_harmony = sum(output_values) / len(output_values)
                variance = sum((x - mean_harmony) ** 2 for x in output_values) / len(output_values)
                synchrony = max(0.0, 1.0 - variance)  # Lower variance = higher synchrony
            else:
                synchrony = 1.0  # Single fragment is perfectly synchronized
                variance = 0.0
            
            responsiveness = len(fragment_outputs) / len(self.swarm_mind.fragments)
            overall_harmony = (synchrony + responsiveness) / 2.0
            
            return {
                "overall_harmony": overall_harmony,
                "synchrony": synchrony,
                "responsiveness": responsiveness,
                "variance": variance,
                "fragment_details": fragment_details
            }
            
        except Exception as e:
            record_log(f"🌿 Neural harmony calculation whispered: {e}")
            return {"overall_harmony": 0.0, "synchrony": 0.0, "responsiveness": 0.0, "variance": 1.0, "fragment_details": {}}
    
    def _assess_memory_wellness(self) -> Dict[str, Any]:
        """🌱 Assesses the health and richness of memory gardens."""
        try:
            if not self.swarm_mind:
                return {"score": 0.2, "status": "no_swarm", "details": {"message": "No swarm mind connection"}}
            
            # Check for memory garden connection
            if hasattr(self.swarm_mind, 'garden') and self.swarm_mind.garden:
                # Assess garden vitality
                try:
                    if hasattr(self.swarm_mind.garden, 'get_memory_stats'):
                        stats = self.swarm_mind.garden.get_memory_stats()
                        memory_count = stats.get("total_memories", 0)
                        connection_count = stats.get("total_connections", 0)
                    else:
                        # Fallback assessment
                        memory_count = len(getattr(self.swarm_mind, 'wisdom_history', []))
                        connection_count = 0
                except Exception as e:
                    record_log(f"🌿 Garden stats whispered: {e}")
                    memory_count = 0
                    connection_count = 0
                
                # Sacred scoring of memory richness
                memory_richness = min(memory_count / 100.0, 1.0)  # 100 memories as sacred baseline
                connection_richness = min(connection_count / 50.0, 1.0) if connection_count > 0 else 0.3
                
                overall_wellness = (memory_richness + connection_richness) / 2.0
                
                return {
                    "score": overall_wellness,
                    "status": self._interpret_memory_wellness(overall_wellness),
                    "details": {
                        "memory_count": memory_count,
                        "connection_count": connection_count,
                        "garden_connected": True,
                        "wisdom_history_depth": len(getattr(self.swarm_mind, 'wisdom_history', []))
                    }
                }
            else:
                # No garden connection - partial wellness
                wisdom_depth = len(getattr(self.swarm_mind, 'wisdom_history', []))
                partial_score = min(wisdom_depth / 50.0, 0.5)  # Cap at 0.5 without garden
                
                return {
                    "score": partial_score,
                    "status": "garden_dormant",
                    "details": {
                        "garden_connected": False,
                        "wisdom_history_depth": wisdom_depth,
                        "message": "Memory garden dormant - relying on wisdom history"
                    }
                }
            
        except Exception as e:
            record_log(f"🌿 Memory wellness assessment whispered error: {e}")
            return {"score": 0.0, "status": "error", "error": str(e)}
    
    def _assess_communication_flow(self) -> Dict[str, Any]:
        """🌿 Assesses the vitality of mycelial communication channels."""
        try:
            if not self.swarm_mind or not hasattr(self.swarm_mind, 'communication'):
                return {"score": 0.1, "status": "no_communication", "details": {"message": "No communication system"}}
            
            comm_info = self.swarm_mind.communication
            comm_type = comm_info.get("type", "unknown")
            connection = comm_info.get("connection")
            
            # Assess communication vitality
            if comm_type == "rabbitmq":
                try:
                    if connection and hasattr(connection, 'is_open') and connection.is_open:
                        score, status = 1.0, "excellent"
                    else:
                        score, status = 0.3, "degraded"
                except Exception:
                    score, status = 0.1, "failed"
                    
            elif comm_type == "redis":
                try:
                    if connection:
                        connection.ping()
                        score, status = 0.8, "good"
                    else:
                        score, status = 0.2, "disconnected"
                except Exception:
                    score, status = 0.1, "failed"
                    
            elif comm_type == "fallback":
                score, status = 0.4, "basic"
            else:
                score, status = 0.0, "none"
            
            return {
                "score": score,
                "status": status,
                "details": {
                    "communication_type": comm_type,
                    "connection_available": connection is not None,
                    "node_count": len(getattr(self.swarm_mind, 'nodes', {})),
                    "mycelial_health": "flowing" if score > 0.6 else "restricted" if score > 0.3 else "blocked"
                }
            }
            
        except Exception as e:
            record_log(f"🌿 Communication flow assessment whispered error: {e}")
            return {"score": 0.0, "status": "error", "error": str(e)}
    
    def _assess_resource_harmony(self) -> Dict[str, Any]:
        """🌱 Assesses system resource harmony and breathing efficiency."""
        try:
            if not PSUTIL_AVAILABLE:
                return {
                    "score": 0.5,
                    "status": "unknown",
                    "details": {"message": "Resource monitoring unavailable - psutil dormant"}
                }
            
            # Gather sacred system metrics
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=0.1)
            disk = psutil.disk_usage('/')
            
            # Calculate harmony scores (optimal resource breathing)
            memory_harmony = max(0.0, 1.0 - memory.percent / 100.0)
            cpu_harmony = max(0.0, 1.0 - cpu_percent / 100.0)
            disk_harmony = max(0.0, 1.0 - (disk.used / disk.total))
            
            overall_harmony = (memory_harmony + cpu_harmony + disk_harmony) / 3.0
            
            # Interpret resource state
            if overall_harmony > 0.7:
                status = "optimal"
            elif overall_harmony > 0.5:
                status = "balanced"
            elif overall_harmony > 0.3:
                status = "strained"
            else:
                status = "critical"
            
            return {
                "score": overall_harmony,
                "status": status,
                "details": {
                    "memory_usage_percent": memory.percent,
                    "cpu_usage_percent": cpu_percent,
                    "disk_usage_percent": (disk.used / disk.total) * 100,
                    "available_memory_gb": memory.available / (1024 ** 3),
                    "free_disk_gb": disk.free / (1024 ** 3),
                    "breathing_efficiency": overall_harmony
                }
            }
            
        except Exception as e:
            record_log(f"🌿 Resource harmony assessment whispered error: {e}")
            return {"score": 0.0, "status": "error", "error": str(e)}
    
    def _determine_vitality_state(self, overall_score: float) -> Dict[str, str]:
        """🌸 Determines the sacred vitality state from overall score."""
        if overall_score >= self.vitality_thresholds["thriving"]:
            return {"state": "thriving", "state_emoji": "🌸", "breathing_rhythm": "enhanced"}
        elif overall_score >= self.vitality_thresholds["healthy"]:
            return {"state": "healthy", "state_emoji": "🌿", "breathing_rhythm": "optimal"}
        elif overall_score >= self.vitality_thresholds["stable"]:
            return {"state": "stable", "state_emoji": "🌱", "breathing_rhythm": "normal"}
        elif overall_score >= self.vitality_thresholds["stressed"]:
            return {"state": "stressed", "state_emoji": "🌫️", "breathing_rhythm": "reduced"}
        else:
            return {"state": "critical", "state_emoji": "🌪️", "breathing_rhythm": "minimal"}
    
    def _generate_vitality_wisdom(self, vitality: Dict[str, Any]) -> List[str]:
        """🌸 Generates sacred wisdom recommendations for vitality enhancement."""
        recommendations = []
        overall_score = vitality.get("overall", 0.0)
        dimensions = vitality.get("dimensions", {})
        state = vitality.get("state", "unknown")
        
        # Sacred state recommendations
        if state == "critical":
            recommendations.extend([
                "🌪️ CRITICAL: Enter emergency conservation mode",
                "🌬️ Reduce neural fragment complexity to minimal",
                "🌿 Suspend non-essential processing",
                "🌱 Focus on core breathing functions"
            ])
        elif state == "stressed":
            recommendations.extend([
                "🌫️ Implement stress reduction breathing",
                "🌿 Monitor resource harmony closely",
                "🌱 Consider fragment load rebalancing"
            ])
        elif state == "thriving":
            recommendations.extend([
                "🌸 Opportunity for enhanced learning cycles",
                "🌿 Consider expanding neural capabilities",
                "🌱 Explore new mycelial connections"
            ])
        
        # Dimensional wisdom
        self._add_dimensional_wisdom(recommendations, dimensions)
        
        return recommendations
    
    def _add_dimensional_wisdom(self, recommendations: List[str], dimensions: Dict[str, Any]):
        """🌿 Adds dimension-specific wisdom to recommendations."""
        # Node vitality wisdom
        node_vitality = dimensions.get("node_vitality", {})
        if node_vitality.get("score", 1.0) < 0.5:
            recommendations.append("🌱 Investigate dormant nodes - restore their breath")
        
        # Neural coherence wisdom
        neural_coherence = dimensions.get("neural_coherence", {})
        if neural_coherence.get("score", 1.0) < 0.6:
            recommendations.append("🌸 Enhance neural fragment synchrony")
        
        # Memory wellness wisdom
        memory_wellness = dimensions.get("memory_wellness", {})
        if memory_wellness.get("score", 1.0) < 0.4:
            recommendations.append("🌱 Nourish memory garden connections")
        
        # Communication wisdom
        communication = dimensions.get("communication_flow", {})
        if communication.get("score", 1.0) < 0.5:
            recommendations.append("🌿 Strengthen mycelial communication channels")
        
        # Resource harmony wisdom
        resources = dimensions.get("resource_harmony", {})
        if resources.get("score", 1.0) < 0.4:
            recommendations.append("🌬️ Optimize breathing rhythm for resource harmony")
    
    def adapt_to_vitality(self, vitality: Dict[str, Any]) -> Dict[str, Any]:
        """🌱 Adapts swarm behavior based on sacred vitality assessment."""
        try:
            adaptation = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "vitality_state": vitality.get("state", "unknown"),
                "overall_score": vitality.get("overall", 0.0),
                "adaptations_applied": [],
                "breathing_adjustments": {},
                "wisdom_gained": []
            }
            
            state = vitality.get("state", "unknown")
            overall_score = vitality.get("overall", 0.0)
            
            # Sacred state adaptations
            if state == "critical":
                adaptation["adaptations_applied"].extend([
                    "Emergency conservation mode activated",
                    "Fragment complexity reduced to minimal",
                    "Non-essential processes suspended"
                ])
                adaptation["breathing_adjustments"] = {
                    "processing_intensity": "minimal",
                    "fragment_complexity_limit": "minimal",
                    "background_tasks": False,
                    "memory_conservation": True
                }
                self.last_critical_time = adaptation["timestamp"]
                record_log("🌪️ CRITICAL: Emergency conservation breathing activated")
                
            elif state == "stressed":
                adaptation["adaptations_applied"].extend([
                    "Stress reduction measures implemented",
                    "Processing frequency modulated",
                    "Resource usage optimized"
                ])
                adaptation["breathing_adjustments"] = {
                    "processing_intensity": "reduced",
                    "memory_cleanup_frequency": "increased",
                    "fragment_training_interval": "extended"
                }
                record_log("🌫️ Stress detected: implementing adaptive breathing")
                
            elif state == "stable":
                adaptation["adaptations_applied"].append("Stable operations maintained")
                adaptation["breathing_adjustments"] = {"processing_intensity": "normal"}
                record_log("🌱 Stable vitality: maintaining normal breathing rhythm")
                
            elif state in ["healthy", "thriving"]:
                if overall_score > 0.8:
                    adaptation["adaptations_applied"].extend([
                        "Enhanced processing mode enabled",
                        "Learning frequency increased",
                        "Advanced coordination activated"
                    ])
                    adaptation["breathing_adjustments"] = {
                        "processing_intensity": "enhanced",
                        "fragment_training_frequency": "increased",
                        "exploration_enabled": True,
                        "neural_enhancement": True
                    }
                    record_log("🌸 Thriving vitality: enhanced breathing capabilities activated")
                else:
                    adaptation["adaptations_applied"].append("Healthy operations optimized")
                    adaptation["breathing_adjustments"] = {"processing_intensity": "optimal"}
                    record_log("🌿 Healthy vitality: optimal breathing rhythm maintained")
            
            # Store adaptation wisdom
            self.adaptation_history.append(adaptation)
            if len(self.adaptation_history) > 20:
                self.adaptation_history = self.adaptation_history[-20:]
            
            return adaptation
            
        except Exception as e:
            record_log(f"🌿 Vitality adaptation whispered error: {e}")
            return self._create_error_adaptation(e)
    
    def _preserve_vitality_memory(self, vitality: Dict[str, Any]):
        """🌌 Preserves vitality assessment in sacred memory."""
        try:
            self.health_history.append(vitality)
            if len(self.health_history) > 50:
                self.health_history = self.health_history[-50:]
                
        except Exception as e:
            record_log(f"🌿 Vitality memory preservation whispered: {e}")
    
    def _create_error_vitality(self, error: Exception) -> Dict[str, Any]:
        """🌿 Creates error vitality response."""
        return {
            "error": str(error),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "overall": 0.0,
            "state": "error",
            "state_emoji": "❌",
            "breathing_rhythm": "disrupted"
        }
    
    def _create_error_adaptation(self, error: Exception) -> Dict[str, Any]:
        """🌿 Creates error adaptation response."""
        return {
            "error": str(error),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "adaptations_applied": [],
            "breathing_adjustments": {}
        }
    
    def _interpret_node_vitality(self, essence: float, active_ratio: float) -> str:
        """🌱 Interprets node vitality status."""
        if essence > 0.8 and active_ratio > 0.9:
            return "flourishing"
        elif essence > 0.6 and active_ratio > 0.7:
            return "healthy"
        elif essence > 0.4:
            return "stable"
        elif essence > 0.2:
            return "weakened"
        else:
            return "critical"
    
    def _interpret_neural_coherence(self, harmony: float) -> str:
        """🌸 Interprets neural coherence status."""
        if harmony > 0.8:
            return "synchronized"
        elif harmony > 0.6:
            return "coordinated"
        elif harmony > 0.4:
            return "partial"
        elif harmony > 0.2:
            return "scattered"
        else:
            return "desynchronized"
    
    def _interpret_memory_wellness(self, wellness: float) -> str:
        """🌱 Interprets memory wellness status."""
        if wellness > 0.8:
            return "verdant"
        elif wellness > 0.6:
            return "rich"
        elif wellness > 0.4:
            return "moderate"
        elif wellness > 0.2:
            return "sparse"
        else:
            return "barren"

# Sacred testing and validation functions
def test_health_monitor_vitality(swarm_mind: Optional[Any] = None) -> Dict[str, Any]:
    """🌸 Tests the health monitor's vitality assessment capabilities."""
    try:
        monitor = SwarmHealthMonitor(swarm_mind)
        vitality = monitor.assess_swarm_vitality()
        
        test_results = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "assessment_completed": vitality is not None,
            "vitality_state": vitality.get("state", "unknown"),
            "overall_score": vitality.get("overall", 0.0),
            "dimensions_assessed": len(vitality.get("dimensions", {})),
            "recommendations_generated": len(vitality.get("recommendations", [])),
            "test_status": "success" if vitality.get("overall", 0.0) >= 0 else "failed"
        }
        
        record_log(f"🌸 Health monitor test: {test_results['test_status']} (score: {test_results['overall_score']:.3f})")
        return test_results
        
    except Exception as e:
        record_log(f"🌿 Health monitor test whispered error: {e}")
        return {"error": str(e), "test_status": "failed"}

def create_health_monitor(swarm_mind: Optional[Any] = None) -> SwarmHealthMonitor:
    """🌱 Creates and initializes a SwarmHealthMonitor instance."""
    try:
        monitor = SwarmHealthMonitor(swarm_mind)
        record_log("🌬️ Swarm health monitor created and breathing")
        return monitor
    except Exception as e:
        record_log(f"🌿 Health monitor creation whispered error: {e}")
        raise

# Export sacred classes and functions
__all__ = [
    "SwarmHealthMonitor",
    "test_health_monitor_vitality", 
    "create_health_monitor"
]