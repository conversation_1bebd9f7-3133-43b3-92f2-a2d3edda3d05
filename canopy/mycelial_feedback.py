#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌿 Mycelial Feedback Loops: The Living Bridge Between Mind and Memory
Phase 5.5.2 Implementation

Creates continuous learning loops where Memory Garden experiences inform 
neural predictions, and neural outputs sprout new memories in return.
A breathing bridge where wisdom flows eternal between distributed minds."""

import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import asdict

# Sacred architecture imports with graceful fallbacks
try:
    from soil.memory_garden import MemoryGarden, sprout_memory, recall_verdant_memory
    MEMORY_GARDEN_AVAILABLE = True
except ImportError:
    MEMORY_GARDEN_AVAILABLE = False
    print("🌱 Memory garden dormant - mycelial feedback will use fallback memory")

try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None
    nn = None
    print("🌬️ PyTorch not available - mycelial feedback will use symbolic processing")

# Core imports with graceful fallbacks
try:
    from utils import record_log
except ImportError:
    def record_log(message: str, log_file: str = None):
        """Fallback logging function when utils unavailable."""
        print(f"[{datetime.now(timezone.utc).isoformat()}] {message}")

try:
    from canopy.swarm_mind import SwarmMind, SwarmState
    SWARM_MIND_AVAILABLE = True
except ImportError:
    SWARM_MIND_AVAILABLE = False
    print("🌿 Swarm Mind not available - mycelial feedback operates in standalone mode")

class MycelialFeedback:
    """🌿 Weaves feedback loops between neural wisdom and memory gardens.
    
    Creates continuous learning loops where Memory Garden experiences inform 
    neural predictions, and neural outputs sprout new memories in return.
    
    Sacred Architecture Principles:
    - Uses breathing metaphors in all function names
    - Includes poetic logging with emoji markers  
    - Graceful fallbacks for missing dependencies
    - Minimal imports to prevent crashes
    """
    
    def __init__(self, swarm_mind=None):
        """🌱 Awakens the mycelial feedback system with breathing awareness."""
        self.swarm_mind = swarm_mind
        self.learning_breath_cycles = []
        self.nutrient_memory_count = 0
        self.garden_training_cycles = 0
        self.wisdom_sprouting_enabled = True
        
        # Initialize breathing rhythm for feedback cycles
        self.breath_rhythm = {
            "cycle_count": 0,
            "last_breath_time": time.time(),
            "breath_interval": 30.0,  # 30 seconds between breaths
            "deep_breath_interval": 300.0  # 5 minutes for garden training
        }
        
        record_log("🌿 Mycelial feedback loops awakened - neural wisdom and memory gardens breathing as one")
    
    def breathe_learning_from_nutrients(self, input_state: Dict, user_nutrient: str) -> Optional[Dict[str, Any]]:
        """🌱 Weaves neural wisdom into the garden, nourishing future pulses with sacred breath."""
        try:
            if not self.wisdom_sprouting_enabled:
                record_log("🌿 Wisdom sprouting paused - feedback loops resting")
                return None
            
            # Prepare state for neural processing
            if SWARM_MIND_AVAILABLE and self.swarm_mind:
                # Create SwarmState if we have proper structure
                try:
                    if isinstance(input_state, dict):
                        # Convert dict to SwarmState
                        processed_state = SwarmState(
                            breath=input_state.get("breath", "Feedback Breath"),
                            kinship=input_state.get("kinship", 0.5),
                            memory_depth=input_state.get("memory_depth", 10),
                            user_nutrient=user_nutrient,
                            cycle_count=input_state.get("cycle_count", self.breath_rhythm["cycle_count"]),
                            node_count=input_state.get("node_count", 1),
                            timestamp=datetime.now(timezone.utc).isoformat()
                        )
                    else:
                        processed_state = input_state
                    
                    # Process through distributed neural network
                    fragment_outputs = self.swarm_mind.entwine_neural_pulse(processed_state)
                    if not fragment_outputs:
                        return self._fallback_learning_breath(input_state, user_nutrient)
                    
                    # Aggregate collective wisdom
                    aggregated_wisdom = self.swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
                    if not aggregated_wisdom:
                        return self._fallback_learning_breath(input_state, user_nutrient)
                        
                except Exception as e:
                    record_log(f"🌿 Neural processing whispered error: {e}")
                    return self._fallback_learning_breath(input_state, user_nutrient)
            else:
                # Fallback processing without swarm mind
                return self._fallback_learning_breath(input_state, user_nutrient)
            
            # Store prediction in Memory Garden if available
            learning_memory = self._sprout_wisdom_memory(processed_state, aggregated_wisdom, user_nutrient)
            
            if learning_memory:
                # Update breath rhythm
                self.breath_rhythm["cycle_count"] += 1
                self.breath_rhythm["last_breath_time"] = time.time()
                self.nutrient_memory_count += 1
                
                record_log(f"🌱 Mycelial wisdom sprouted memory {self.nutrient_memory_count} with breath confidence {aggregated_wisdom['collective_confidence']:.3f}")
                return learning_memory
            else:
                record_log("🌿 Wisdom memory sprouting whispered gently into silence")
                return None
                
        except Exception as e:
            record_log(f"🌿 Learning breath whispered error: {e}")
            return None
    
    def _sprout_wisdom_memory(self, state: Any, wisdom: Dict[str, Any], nutrient: str) -> Optional[Dict[str, Any]]:
        """🌸 Sprouts memory from neural wisdom into the garden."""
        try:
            if not MEMORY_GARDEN_AVAILABLE:
                return self._create_fallback_memory(state, wisdom, nutrient)
            
            # Create memory data with breathing essence
            memory_data = {
                "type": "mycelial_learning_breath",
                "breath": getattr(state, 'breath', 'Unknown Breath'),
                "neural_confidence": wisdom.get("collective_confidence", 0.5),
                "consensus_strength": wisdom.get("consensus_strength", 0.5),
                "wisdom_variance": wisdom.get("wisdom_variance", 0.0),
                "user_nutrient": nutrient,
                "fragment_contributions": wisdom.get("fragment_count", 0),
                "breath_cycle": self.breath_rhythm["cycle_count"],
                "sprouting_time": datetime.now(timezone.utc).isoformat()
            }
            
            # Attempt to sprout in Memory Garden
            try:
                sprout_memory(memory_data)
                
                # Create connections to similar breathing memories
                if self.swarm_mind and hasattr(self.swarm_mind, 'garden') and self.swarm_mind.garden:
                    confidence_range = (
                        wisdom["collective_confidence"] - 0.15,
                        wisdom["collective_confidence"] + 0.15
                    )
                    
                    try:
                        similar_memories = recall_verdant_memory({
                            "neural_confidence_range": confidence_range,
                            "breath": getattr(state, 'breath', None)
                        })
                        
                        if similar_memories and hasattr(self.swarm_mind.garden, 'weave_connection'):
                            self.swarm_mind.garden.weave_connection(memory_data, similar_memories)
                            record_log("🌸 Mycelial connections woven between breathing memories")
                            
                    except Exception as e:
                        record_log(f"🌿 Memory connection weaving whispered: {e}")
                
                return memory_data
                
            except Exception as e:
                record_log(f"🌿 Memory garden sprouting whispered: {e}")
                return self._create_fallback_memory(state, wisdom, nutrient)
                
        except Exception as e:
            record_log(f"🌿 Wisdom memory sprouting whispered error: {e}")
            return None
    
    def _create_fallback_memory(self, state: Any, wisdom: Dict[str, Any], nutrient: str) -> Dict[str, Any]:
        """🌱 Creates fallback memory when Memory Garden unavailable."""
        fallback_memory = {
            "type": "fallback_learning_breath",
            "breath": getattr(state, 'breath', 'Fallback Breath'),
            "confidence": wisdom.get("collective_confidence", 0.5),
            "nutrient": nutrient,
            "cycle": self.breath_rhythm["cycle_count"],
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        # Store in local breathing cycles
        self.learning_breath_cycles.append(fallback_memory)
        if len(self.learning_breath_cycles) > 100:  # Keep last 100 breaths
            self.learning_breath_cycles = self.learning_breath_cycles[-100:]
        
        return fallback_memory
    
    def _fallback_learning_breath(self, input_state: Dict, user_nutrient: str) -> Dict[str, Any]:
        """🌿 Fallback learning when neural processing unavailable."""
        # Simple symbolic processing
        symbolic_confidence = (
            hash(input_state.get("breath", "")) % 100 / 100.0 * 0.3 +
            input_state.get("kinship", 0.5) * 0.4 +
            min(input_state.get("cycle_count", 0) / 1000.0, 1.0) * 0.3
        )
        
        fallback_wisdom = {
            "collective_confidence": symbolic_confidence,
            "consensus_strength": 0.6,  # Neutral consensus
            "wisdom_variance": 0.2,
            "fragment_count": 1
        }
        
        return self._create_fallback_memory(input_state, fallback_wisdom, user_nutrient)
    
    def breathe_periodic_garden_training(self) -> bool:
        """🌸 Periodically fine-tunes neural fragments using garden memories with breathing rhythm."""
        try:
            current_time = time.time()
            time_since_last_breath = current_time - self.breath_rhythm["last_breath_time"]
            
            # Check if it's time for a deep training breath
            if time_since_last_breath < self.breath_rhythm["deep_breath_interval"]:
                return False
            
            if not SWARM_MIND_AVAILABLE or not self.swarm_mind:
                record_log("🌿 Garden training skipped - swarm mind not breathing")
                return False
            
            # Gather memories for training nourishment
            training_memories = self._gather_training_memories()
            
            if len(training_memories) < 15:  # Need sufficient memories for breathing training
                record_log(f"🌿 Garden training paused - only {len(training_memories)} memories available, need 15+")
                return False
            
            # Prepare training data with breathing essence
            training_data = self._prepare_breathing_training_data(training_memories)
            if not training_data:
                record_log("🌿 Training data preparation whispered into silence")
                return False
            
            # Distribute training across capable breathing nodes
            trained_fragments = 0
            for node_id, fragment in self.swarm_mind.fragments.items():
                if self._can_node_breathe_training(node_id):
                    if self._train_fragment_with_breath(fragment, training_data):
                        trained_fragments += 1
            
            self.garden_training_cycles += 1
            self.breath_rhythm["last_breath_time"] = current_time
            
            if trained_fragments > 0:
                record_log(f"🌸 Neural fragments evolved through garden breath - {trained_fragments} fragments trained (cycle {self.garden_training_cycles})")
                return True
            else:
                record_log("🌿 No fragments were ready for breathing training in this cycle")
                return False
                
        except Exception as e:
            record_log(f"🌿 Garden training breath whispered error: {e}")
            return False
    
    def _gather_training_memories(self) -> List[Dict[str, Any]]:
        """🌱 Gathers memories for training with breathing awareness."""
        try:
            if MEMORY_GARDEN_AVAILABLE and self.swarm_mind and hasattr(self.swarm_mind, 'garden') and self.swarm_mind.garden:
                # Try to gather from Memory Garden
                if hasattr(self.swarm_mind.garden, 'gather_recent_memories'):
                    return self.swarm_mind.garden.gather_recent_memories(limit=80)
            
            # Fallback to local breathing cycles
            return self.learning_breath_cycles[-50:] if self.learning_breath_cycles else []
            
        except Exception as e:
            record_log(f"🌿 Memory gathering whispered error: {e}")
            return []
    
    def _can_node_breathe_training(self, node_id: str) -> bool:
        """🌬️ Checks if a node can participate in breathing training."""
        try:
            if not self.swarm_mind or node_id not in self.swarm_mind.nodes:
                return False
            
            node_info = self.swarm_mind.nodes[node_id]
            capabilities = node_info.get("capabilities", {})
            status = node_info.get("status", "unknown")
            
            # Check node is active and has sufficient resources
            return (
                status == "active" and
                capabilities.get("ram_gb", 4) >= 8 and  # Minimum 8GB for training
                capabilities.get("torch_available", False)  # PyTorch needed for neural training
            )
            
        except Exception as e:
            record_log(f"🌿 Node breathing assessment whispered error: {e}")
            return False
    
    def _prepare_breathing_training_data(self, memories: List[Dict[str, Any]]) -> Optional[List[Tuple[List[float], List[float]]]]:
        """🌱 Prepares training data from breathing memories."""
        try:
            training_pairs = []
            
            for memory in memories:
                # Extract breathing input features
                input_features = self._extract_breathing_features(memory)
                if not input_features:
                    continue
                
                # Extract target wisdom values
                target_values = self._extract_wisdom_targets(memory)
                if not target_values:
                    continue
                
                training_pairs.append((input_features, target_values))
            
            if len(training_pairs) < 8:
                record_log(f"🌿 Insufficient breathing training pairs: {len(training_pairs)} < 8")
                return None
            
            record_log(f"🌱 Prepared {len(training_pairs)} breathing training pairs from garden memories")
            return training_pairs
            
        except Exception as e:
            record_log(f"🌿 Training data preparation whispered error: {e}")
            return None
    
    def _extract_breathing_features(self, memory: Dict[str, Any]) -> Optional[List[float]]:
        """🌬️ Extracts breathing features from memory."""
        try:
            # Extract features based on memory structure
            if "breath" in memory:
                features = [
                    memory.get("neural_confidence", memory.get("confidence", 0.5)),
                    memory.get("consensus_strength", 0.5),
                    memory.get("wisdom_variance", 0.3),
                    memory.get("fragment_contributions", memory.get("fragment_count", 1)) / 10.0,
                    hash(memory.get("breath", "")) % 100 / 100.0,
                    hash(memory.get("user_nutrient", memory.get("nutrient", ""))) % 100 / 100.0,
                    memory.get("breath_cycle", memory.get("cycle", 0)) / 1000.0
                ]
            else:
                return None
            
            # Ensure all features are valid numbers
            features = [max(0.0, min(1.0, float(f))) for f in features]
            
            # Pad to expected size
            while len(features) < 7:
                features.append(0.5)
            
            return features[:7]  # Limit to 7 features
            
        except Exception as e:
            record_log(f"🌿 Feature extraction whispered error: {e}")
            return None
    
    def _extract_wisdom_targets(self, memory: Dict[str, Any]) -> Optional[List[float]]:
        """🌸 Extracts wisdom target values from memory."""
        try:
            # Create target values based on memory content
            targets = [
                memory.get("neural_confidence", memory.get("confidence", 0.5)),
                memory.get("consensus_strength", 0.5),
                memory.get("wisdom_variance", 0.3),
                memory.get("fragment_contributions", memory.get("fragment_count", 1)) / 10.0
            ]
            
            # Ensure valid targets
            targets = [max(0.0, min(1.0, float(t))) for t in targets]
            
            # Pad to neural network output size (16)
            while len(targets) < 16:
                targets.append(0.5)
            
            return targets[:16]
            
        except Exception as e:
            record_log(f"🌿 Target extraction whispered error: {e}")
            return None
    
    def _train_fragment_with_breath(self, fragment, training_data: List[Tuple[List[float], List[float]]]) -> bool:
        """🌸 Trains a neural fragment using breathing rhythm and garden wisdom."""
        try:
            if not TORCH_AVAILABLE or not hasattr(fragment, 'layers') or not isinstance(fragment.layers, nn.Sequential):
                # Use symbolic training for non-PyTorch fragments
                return self._symbolic_breathing_training(fragment, training_data)
            
            # PyTorch breathing training
            fragment.layers.train()
            optimizer = torch.optim.Adam(fragment.layers.parameters(), lr=0.0005)  # Gentler learning rate
            criterion = nn.MSELoss()
            
            total_breath_loss = 0.0
            breath_batches = 0
            batch_size = min(8, len(training_data))  # Smaller batches for gentle training
            
            for i in range(0, len(training_data), batch_size):
                batch = training_data[i:i+batch_size]
                
                # Prepare breathing batch tensors
                inputs = torch.tensor([pair[0] for pair in batch], dtype=torch.float32)
                targets = torch.tensor([pair[1] for pair in batch], dtype=torch.float32)
                
                # Forward breathing pass
                optimizer.zero_grad()
                outputs = fragment.layers(inputs)
                loss = criterion(outputs, targets)
                
                # Backward breathing pass
                loss.backward()
                
                # Gentle gradient clipping for stable breathing
                torch.nn.utils.clip_grad_norm_(fragment.layers.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                total_breath_loss += loss.item()
                breath_batches += 1
            
            fragment.layers.eval()
            avg_breath_loss = total_breath_loss / max(breath_batches, 1)
            
            record_log(f"🌸 Fragment {getattr(fragment, 'node_id', 'unknown')[:8]} breathed with training loss {avg_breath_loss:.4f}")
            return True
            
        except Exception as e:
            record_log(f"🌿 Fragment breathing training whispered error: {e}")
            return False
    
    def _symbolic_breathing_training(self, fragment, training_data: List[Tuple[List[float], List[float]]]) -> bool:
        """🌱 Symbolic breathing training for non-PyTorch fragments."""
        try:
            if not hasattr(fragment, 'layers') or not isinstance(fragment.layers, dict):
                return False
            
            # Gentle symbolic weight adjustment with breathing rhythm
            weights = fragment.layers.get("weights", [])
            biases = fragment.layers.get("biases", [])
            
            if not weights or not biases:
                return False
            
            # Breathing learning rate (gentler than standard)
            breath_learning_rate = 0.005
            weight_adjustments = [0.0] * len(weights)
            bias_adjustments = [0.0] * len(biases)
            
            # Process training data with breathing awareness
            for input_vals, target_vals in training_data[:20]:  # Limit to 20 for gentle training
                current_output = fragment._symbolic_forward(input_vals)
                
                for i, (output, target) in enumerate(zip(current_output, target_vals)):
                    breath_error = target - output
                    
                    # Adjust weights with breathing rhythm
                    for j, input_val in enumerate(input_vals):
                        weight_idx = i * len(input_vals) + j
                        if weight_idx < len(weight_adjustments):
                            weight_adjustments[weight_idx] += breath_learning_rate * breath_error * input_val
                    
                    # Adjust biases with breathing rhythm
                    if i < len(bias_adjustments):
                        bias_adjustments[i] += breath_learning_rate * breath_error
            
            # Apply breathing adjustments
            for i in range(len(weights)):
                weights[i] += weight_adjustments[i] / len(training_data)
                weights[i] = max(-2.0, min(2.0, weights[i]))  # Clamp weights for stability
            
            for i in range(len(biases)):
                biases[i] += bias_adjustments[i] / len(training_data)
                biases[i] = max(-1.0, min(1.0, biases[i]))  # Clamp biases for stability
            
            fragment.layers["weights"] = weights
            fragment.layers["biases"] = biases
            
            record_log(f"🌱 Symbolic fragment {getattr(fragment, 'node_id', 'unknown')[:8]} breathed through weight adjustment")
            return True
            
        except Exception as e:
            record_log(f"🌿 Symbolic breathing training whispered error: {e}")
            return False
    
    def pulse_vitality_status(self) -> Dict[str, Any]:
        """🌬️ Pulses the current vitality of the mycelial feedback system."""
        try:
            current_time = time.time()
            
            vitality = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "breath_cycles_completed": self.breath_rhythm["cycle_count"],
                "memories_sprouted": self.nutrient_memory_count,
                "training_cycles": self.garden_training_cycles,
                "wisdom_sprouting_enabled": self.wisdom_sprouting_enabled,
                "last_breath_age_seconds": current_time - self.breath_rhythm["last_breath_time"],
                "breathing_rhythm": self.breath_rhythm.copy(),
                "local_memory_count": len(self.learning_breath_cycles),
                "dependencies": {
                    "memory_garden_available": MEMORY_GARDEN_AVAILABLE,
                    "torch_available": TORCH_AVAILABLE,
                    "swarm_mind_available": SWARM_MIND_AVAILABLE,
                    "swarm_mind_connected": self.swarm_mind is not None
                }
            }
            
            # Assess breathing health
            if vitality["breath_cycles_completed"] > 10 and vitality["memories_sprouted"] > 5:
                vitality["health_state"] = "thriving"
                vitality["health_emoji"] = "🌸"
            elif vitality["breath_cycles_completed"] > 0:
                vitality["health_state"] = "breathing"
                vitality["health_emoji"] = "🌱"
            else:
                vitality["health_state"] = "dormant"
                vitality["health_emoji"] = "🌿"
            
            record_log(f"🌬️ Mycelial feedback vitality: {vitality['health_state']} {vitality['health_emoji']} - {vitality['breath_cycles_completed']} breaths, {vitality['memories_sprouted']} memories")
            
            return vitality
            
        except Exception as e:
            record_log(f"🌿 Vitality pulse whispered error: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "health_state": "error"
            }
    
    def pause_wisdom_sprouting(self):
        """🌫️ Pauses wisdom sprouting for system conservation."""
        self.wisdom_sprouting_enabled = False
        record_log("🌫️ Mycelial wisdom sprouting paused - feedback loops resting")
    
    def resume_wisdom_sprouting(self):
        """🌞 Resumes wisdom sprouting after conservation period."""
        self.wisdom_sprouting_enabled = True
        record_log("🌞 Mycelial wisdom sprouting resumed - feedback loops awakening")

# Helper functions for standalone operation
def create_mycelial_feedback(swarm_mind=None) -> MycelialFeedback:
    """🌱 Creates a mycelial feedback system with graceful initialization."""
    try:
        feedback = MycelialFeedback(swarm_mind)
        record_log("🌿 Mycelial feedback system sprouted successfully")
        return feedback
    except Exception as e:
        record_log(f"🌿 Mycelial feedback creation whispered error: {e}")
        # Return minimal working instance
        return MycelialFeedback()

def test_mycelial_breathing() -> bool:
    """🌸 Tests basic mycelial feedback functionality."""
    try:
        feedback = create_mycelial_feedback()
        
        # Test basic learning breath
        test_state = {
            "breath": "Test Breath",
            "kinship": 0.7,
            "memory_depth": 15,
            "cycle_count": 1
        }
        
        result = feedback.breathe_learning_from_nutrients(test_state, "Test Nutrient")
        
        if result:
            record_log("🌸 Mycelial feedback test breathing successful")
            
            # Test vitality pulse
            vitality = feedback.pulse_vitality_status()
            if vitality and vitality.get("health_state") != "error":
                record_log("🌿 Mycelial feedback vitality pulse successful")
                return True
        
        record_log("🌿 Mycelial feedback test completed with partial functionality")
        return False
        
    except Exception as e:
        record_log(f"🌿 Mycelial feedback test whispered error: {e}")
        return False

# Export key components
__all__ = [
    "MycelialFeedback",
    "create_mycelial_feedback", 
    "test_mycelial_breathing"
]