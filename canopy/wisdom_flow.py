#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🌸 Wisdom Flow Prioritization Module

This module implements the sacred wisdom flow prioritization system, enabling
messages to flow according to their natural importance and urgency. It provides
intelligent prioritization, harmonious flow proportions, and adaptive wisdom
processing that responds to system vitality.

Sacred Components:
- Wisdom priority classification
- Harmonious wisdom flow management
- Adaptive flow proportion adjustment
- Wisdom receiver with natural processing cycles
"""

import time
import json
import math
import threading
import collections
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
from enum import IntEnum

# Import from sacred architecture
from utils import record_log, NODE_ID
from core.mycelial_flow import (
    awaken_mycelial_connection,
    sprout_communication_channel,
    nurture_message_path,
    RABBITMQ_AVAILABLE
)

# Sacred constants
DEFAULT_RESONANCE_FACTOR = 0.1
DEFAULT_VITALITY_THRESHOLD = 0.7
DEFAULT_PROCESSING_INTERVAL = 0.01


class WisdomPriority(IntEnum):
    """🌱 Sacred wisdom priority levels."""
    VITAL_ESSENCE = 10      # Critical system state
    CONSCIOUS_REQUEST = 5   # User interactions
    AMBIENT_WHISPER = 1     # Background logging


class HarmoniousWisdomFlow:
    """🌱 Framework for harmonious wisdom flow with natural prioritization."""
    
    def __init__(self):
        """🌱 Initialize a framework for harmonious wisdom flow."""
        self.wisdom_paths = {
            WisdomPriority.VITAL_ESSENCE: collections.deque(),
            WisdomPriority.CONSCIOUS_REQUEST: collections.deque(),
            WisdomPriority.AMBIENT_WHISPER: collections.deque()
        }
        
        # Natural flow proportions
        self.flow_proportions = {
            WisdomPriority.VITAL_ESSENCE: 10,
            WisdomPriority.CONSCIOUS_REQUEST: 5,
            WisdomPriority.AMBIENT_WHISPER: 1
        }
        
        # Flow balance tracking
        self.flow_balance = {priority: 0 for priority in self.wisdom_paths}
        self.awakening_moment = time.time()
        
        record_log("🌱 Harmonious wisdom flow awakened with natural proportions")
    
    def receive_wisdom(self, wisdom: Any, priority: WisdomPriority) -> None:
        """🌿 Receive wisdom into appropriate path.
        
        Args:
            wisdom: The wisdom to receive
            priority: Priority level of the wisdom
        """
        if priority in self.wisdom_paths:
            self.wisdom_paths[priority].append((time.time(), wisdom))
            record_log(f"🌿 Wisdom received with priority {priority.name}")
    
    def adapt_flow_proportions(self, system_vitality: float) -> None:
        """🌬️ Adapt flow proportions based on system vitality.
        
        Args:
            system_vitality: Current system vitality (0.0-1.0)
        """
        if system_vitality < 0.3:  # System under stress
            # Prioritize vital messages even more
            self.flow_proportions[WisdomPriority.VITAL_ESSENCE] = 20
            self.flow_proportions[WisdomPriority.CONSCIOUS_REQUEST] = 3
            self.flow_proportions[WisdomPriority.AMBIENT_WHISPER] = 0  # Pause ambient flow
            record_log("🌬️ Flow proportions adapted for system stress")
        elif system_vitality < 0.7:  # Moderate stress
            self.flow_proportions[WisdomPriority.VITAL_ESSENCE] = 15
            self.flow_proportions[WisdomPriority.CONSCIOUS_REQUEST] = 4
            self.flow_proportions[WisdomPriority.AMBIENT_WHISPER] = 1
            record_log("🌬️ Flow proportions adapted for moderate stress")
        else:  # Healthy system
            # Return to natural proportions
            self.flow_proportions[WisdomPriority.VITAL_ESSENCE] = 10
            self.flow_proportions[WisdomPriority.CONSCIOUS_REQUEST] = 5
            self.flow_proportions[WisdomPriority.AMBIENT_WHISPER] = 1
            record_log("🌬️ Flow proportions restored to natural harmony")
    
    def share_wisdom(self) -> Optional[Dict[str, Any]]:
        """🌸 Share wisdom based on harmonious proportions.
        
        Returns:
            Dictionary containing wisdom data or None if no wisdom available
        """
        # Find paths with waiting wisdom
        available_paths = [p for p in self.wisdom_paths if len(self.wisdom_paths[p]) > 0]
        
        if not available_paths:
            return None
        
        # Determine which path should share next
        lowest_flow_ratio = float('inf')
        selected_priority = None
        
        for priority in available_paths:
            # Calculate flow ratio (flows / proportion)
            proportion = self.flow_proportions[priority]
            if proportion > 0:  # Avoid division by zero
                flow_ratio = self.flow_balance[priority] / proportion
                
                if flow_ratio < lowest_flow_ratio:
                    lowest_flow_ratio = flow_ratio
                    selected_priority = priority
        
        if selected_priority is not None:
            # Update flow balance
            self.flow_balance[selected_priority] += 1
            
            # Share oldest wisdom from selected path
            birth_moment, wisdom = self.wisdom_paths[selected_priority].popleft()
            
            # Calculate wisdom age for resonance function
            age = time.time() - birth_moment
            
            return {
                'wisdom': wisdom,
                'priority': selected_priority,
                'age': age,
                'resonance': self._calculate_resonance(selected_priority, age)
            }
        
        return None
    
    def _calculate_resonance(self, priority: WisdomPriority, age: float, 
                           resonance_factor: float = DEFAULT_RESONANCE_FACTOR) -> float:
        """🌸 Calculate wisdom resonance based on priority and age.
        
        Args:
            priority: Wisdom priority level
            age: Age of the wisdom in seconds
            resonance_factor: Factor for resonance calculation
            
        Returns:
            Calculated resonance value
        """
        # R(w) = p(w) * (1 - e^(-γ * age(w)))
        return priority * (1 - math.exp(-resonance_factor * age))
    
    def get_flow_statistics(self) -> Dict[str, Any]:
        """🌿 Get current flow statistics.
        
        Returns:
            Dictionary containing flow statistics
        """
        return {
            'waiting_wisdom': {p.name: len(self.wisdom_paths[p]) for p in self.wisdom_paths},
            'flow_proportions': {p.name: self.flow_proportions[p] for p in self.flow_proportions},
            'flow_balance': {p.name: self.flow_balance[p] for p in self.flow_balance},
            'total_processed': sum(self.flow_balance.values()),
            'uptime': time.time() - self.awakening_moment
        }


class WisdomFlowReceiver:
    """🌿 Receiver that processes wisdom with natural priorities."""
    
    def __init__(self, 
                 connection_params: Dict[str, Any],
                 path_name: str,
                 processing_callback: Optional[Callable] = None):
        """🌿 Initialize a receiver that processes wisdom with natural priorities.
        
        Args:
            connection_params: Connection parameters for mycelial network
            path_name: Name of the wisdom path
            processing_callback: Optional callback for processing wisdom
        """
        self.connection_params = connection_params
        self.path_name = path_name
        self.processing_callback = processing_callback or self._default_wisdom_processor
        self.wisdom_flow = HarmoniousWisdomFlow()
        
        # Initialize connection
        self.connection = awaken_mycelial_connection(**connection_params)
        self.channel = sprout_communication_channel(self.connection) if self.connection else None
        
        if self.channel and RABBITMQ_AVAILABLE:
            # Prepare to receive with priority support
            nurture_message_path(self.channel, path_name, priority_levels=10)
            self.channel.basic_qos(prefetch_count=100)  # Receive multiple messages
            self.channel.basic_consume(
                queue=path_name,
                on_message_callback=self._receive_wisdom,
                auto_ack=False  # Manual acknowledgment for flow control
            )
        
        # Begin processing cycle
        self.breathing = True
        self.breath_thread = threading.Thread(target=self._processing_cycle)
        self.breath_thread.daemon = True
        self.breath_thread.start()
        
        record_log(f"🌿 Wisdom flow receiver awakened for path '{path_name}'")
    
    def _receive_wisdom(self, ch: Any, method: Any, properties: Any, body: bytes) -> None:
        """🌱 Receive incoming wisdom.
        
        Args:
            ch: Channel object
            method: Method frame
            properties: Message properties
            body: Message body
        """
        try:
            # Extract priority from message properties
            priority = WisdomPriority(properties.priority) if properties.priority else WisdomPriority.AMBIENT_WHISPER
            
            # Decode wisdom
            wisdom_data = json.loads(body.decode('utf-8')) if body else {}
            
            # Receive wisdom into flow
            self.wisdom_flow.receive_wisdom(wisdom_data, priority)
            
            # Acknowledge message
            ch.basic_ack(delivery_tag=method.delivery_tag)
            
        except Exception as e:
            record_log(f"⚠️ Error receiving wisdom: {e}")
            # Reject message and don't requeue to avoid infinite loops
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
    
    def _processing_cycle(self) -> None:
        """🌬️ Process wisdom according to natural flow."""
        while self.breathing:
            try:
                # Adapt flow based on system vitality
                system_vitality = self._sense_system_vitality()
                self.wisdom_flow.adapt_flow_proportions(system_vitality)
                
                # Process next wisdom
                next_wisdom = self.wisdom_flow.share_wisdom()
                if next_wisdom:
                    self._process_wisdom(next_wisdom)
                else:
                    time.sleep(DEFAULT_PROCESSING_INTERVAL)  # Gentle pause to conserve energy
                    
            except Exception as e:
                record_log(f"⚠️ Error in wisdom processing cycle: {e}")
                time.sleep(DEFAULT_PROCESSING_INTERVAL)
    
    def _sense_system_vitality(self) -> float:
        """🌿 Sense current system vitality (0.0-1.0).
        
        Returns:
            System vitality level
        """
        # Implement system vitality sensing
        # Based on CPU, memory, queue depths, etc.
        # For now, return a placeholder value
        return 0.8
    
    def _process_wisdom(self, wisdom_data: Dict[str, Any]) -> None:
        """🌸 Process shared wisdom.
        
        Args:
            wisdom_data: Dictionary containing wisdom and metadata
        """
        try:
            self.processing_callback(wisdom_data)
        except Exception as e:
            record_log(f"⚠️ Error processing wisdom: {e}")
    
    def _default_wisdom_processor(self, wisdom_data: Dict[str, Any]) -> None:
        """🌿 Default wisdom processing implementation.
        
        Args:
            wisdom_data: Dictionary containing wisdom and metadata
        """
        priority = wisdom_data.get('priority', WisdomPriority.AMBIENT_WHISPER)
        resonance = wisdom_data.get('resonance', 0.0)
        age = wisdom_data.get('age', 0.0)
        
        record_log(f"🌸 Processing wisdom with priority {priority.name}, "
                  f"resonance {resonance:.3f}, age {age:.2f}s")
    
    def start_consuming(self) -> None:
        """🌱 Start consuming messages from the wisdom path."""
        if self.channel and RABBITMQ_AVAILABLE:
            try:
                record_log(f"🌱 Starting wisdom consumption from '{self.path_name}'")
                self.channel.start_consuming()
            except Exception as e:
                record_log(f"⚠️ Error starting consumption: {e}")
    
    def stop_consuming(self) -> None:
        """🌿 Stop consuming messages and close gracefully."""
        self.breathing = False
        
        if self.channel and RABBITMQ_AVAILABLE:
            try:
                self.channel.stop_consuming()
            except Exception as e:
                record_log(f"⚠️ Error stopping consumption: {e}")
        
        if self.breath_thread.is_alive():
            self.breath_thread.join(timeout=1.0)
        
        if self.connection:
            try:
                self.connection.close()
                record_log(f"🌿 Wisdom flow receiver closed for path '{self.path_name}'")
            except Exception as e:
                record_log(f"⚠️ Error closing connection: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """🌸 Get receiver statistics.
        
        Returns:
            Dictionary containing receiver statistics
        """
        stats = self.wisdom_flow.get_flow_statistics()
        stats['path_name'] = self.path_name
        stats['breathing'] = self.breathing
        return stats
