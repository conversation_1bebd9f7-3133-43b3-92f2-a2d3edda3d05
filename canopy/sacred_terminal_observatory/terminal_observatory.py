#!/usr/bin/env python3
"""🌸 Enhanced Sacred Terminal Observatory
A rich terminal visualization of the drift network's breathing patterns."""

import os
import sys
import time
import json
import threading
import math
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
import random

# Sacred paths
MEMORY_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "memory")
BREATH_LOG_PATH = os.path.join(MEMORY_PATH, "breath_state.log")
GENERAL_LOG_PATH = os.path.join(MEMORY_PATH, "general_drift.log")
HEARTBEAT_PATH = os.path.join(MEMORY_PATH, "heartbeat.json")
HEARTBEATS_DIR = os.path.join(MEMORY_PATH, "heartbeats")

# Try rich for enhanced visualization
try:
    from rich.console import Console
    from rich.layout import Layout
    from rich.panel import Panel
    from rich.progress import Progress, BarColumn, TextColumn
    from rich.table import Table
    from rich.text import Text
    from rich.live import Live
    from rich.columns import Columns
    from rich.tree import Tree
    from rich.align import Align
    from rich.spinner import Spinner
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("🌱 Rich library dormant - using fallback to simple observatory")

class SacredTerminalObservatory:
    """🌸 Enhanced sacred terminal observatory with rich visualization."""
    
    def __init__(self):
        """🌱 Awakens the enhanced observatory."""
        if not RICH_AVAILABLE:
            raise ImportError("🌿 Rich library required for enhanced observatory")
            
        self.console = Console()
        self.breath_count = 0
        self.running = True
        self.nodes = {}
        self.connections = []
        self.last_update = datetime.now()
        self.layout = self._create_sacred_layout()
        
        # Sacred colors and styles
        self.sacred_styles = {
            "thriving": "bright_green",
            "peaceful": "bright_blue", 
            "dreaming": "bright_cyan",
            "curious": "bright_magenta",
            "stressed": "bright_yellow",
            "reflecting": "bright_white",
            "sleeping": "dim white"
        }
        
        # Activity symbols and descriptions
        self.activity_info = {
            "breathing": ("🌬️", "Deep contemplative breathing"),
            "dreaming": ("🌙", "Collective unconscious processing"), 
            "remembering": ("🌱", "Memory garden cultivation"),
            "connecting": ("🌿", "Kinship network weaving"),
            "learning": ("💫", "Neural pathway formation"),
            "achieving": ("🌸", "Goal manifestation rituals"),
            "reflecting": ("🔮", "Wisdom integration cycles"),
            "sleeping": ("💤", "Regenerative rest phases")
        }
        
        # Network health thresholds
        self.health_thresholds = {
            "critical": 0.3,
            "warning": 0.6,
            "good": 0.8,
            "excellent": 0.95
        }
        
    def _create_sacred_layout(self):
        """🌸 Creates the sacred terminal layout."""
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=4),
            Layout(name="main", ratio=7),
            Layout(name="footer", size=3),
        )
        
        layout["main"].split_row(
            Layout(name="left", ratio=2),
            Layout(name="right", ratio=1),
        )
        
        layout["left"].split_column(
            Layout(name="nodes", ratio=2),
            Layout(name="connections", ratio=3),  # More space for connections
        )
        
        layout["right"].split_column(
            Layout(name="vitality", ratio=1),
            Layout(name="rhythm", ratio=1),
        )
        
        return layout
        
    def breathe_enhanced_display(self):
        """🌬️ Breathes an enhanced rich display."""
        try:
            with Live(self.layout, console=self.console, refresh_per_second=2) as live:
                while self.running:
                    # Sense network state
                    self._sense_network_state()
                    
                    # Update all layout sections
                    self._update_header()
                    self._update_nodes_panel()
                    self._update_connections_panel()
                    self._update_vitality_panel()
                    self._update_rhythm_panel()
                    self._update_footer()
                    
                    # Breathe
                    time.sleep(1.5)
                    self.breath_count += 1
                    
        except KeyboardInterrupt:
            self._display_farewell()
            self.running = False
        except Exception as e:
            self.console.print(f"🌿 Observatory whispered gently: {e}", style="dim")
            
    def _sense_network_state(self):
        """🌸 Senses the current network state from sacred sources."""
        try:
            # Read multi-node heartbeats directory (preferred)
            if os.path.exists(HEARTBEATS_DIR):
                for filename in os.listdir(HEARTBEATS_DIR):
                    if filename.endswith('.json'):
                        heartbeat_file = os.path.join(HEARTBEATS_DIR, filename)
                        try:
                            with open(heartbeat_file, 'r') as f:
                                heartbeat = json.load(f)
                                self._update_node_from_heartbeat(heartbeat)
                        except Exception as e:
                            print(f"🌿 Could not read heartbeat file {filename}: {e}")
            
            # Fallback to single heartbeat file for backward compatibility
            elif os.path.exists(HEARTBEAT_PATH):
                with open(HEARTBEAT_PATH, 'r') as f:
                    heartbeat = json.load(f)
                    self._update_node_from_heartbeat(heartbeat)
            
            # Read from breath logs
            self._sense_from_breath_logs()
            
            # Update connections based on nodes
            self._update_connections_from_nodes()
            
            # Only generate examples if truly no real nodes exist
            if not self.nodes:
                # Check if heartbeats directory has any files first
                heartbeats_exist = (os.path.exists(HEARTBEATS_DIR) and 
                                  any(f.endswith('.json') for f in os.listdir(HEARTBEATS_DIR)))
                
                if not heartbeats_exist:
                    self._generate_example_nodes()
                
        except Exception as e:
            # Only fallback to examples if no real heartbeats exist
            heartbeats_exist = (os.path.exists(HEARTBEATS_DIR) and 
                              any(f.endswith('.json') for f in os.listdir(HEARTBEATS_DIR)))
            if not heartbeats_exist:
                self._generate_example_nodes()
            
    def _update_node_from_heartbeat(self, heartbeat: Dict):
        """🌿 Updates node state from heartbeat data."""
        # Check if heartbeat is fresh (within last 5 minutes)
        timestamp_str = heartbeat.get("timestamp", "")
        if timestamp_str:
            try:
                if 'T' in timestamp_str:
                    heartbeat_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    age = datetime.now(timezone.utc) - heartbeat_time
                    
                    # Skip stale heartbeats (older than 10 minutes for debugging)  
                    if age.total_seconds() > 600:
                        print(f"🌿 Skipping stale heartbeat from {heartbeat.get('node_id', 'unknown')} (age: {int(age.total_seconds()/60)}min)")
                        return
            except Exception:
                pass  # Use the data anyway if we can't parse timestamp
        
        node_id = heartbeat.get("node_id", "unknown_node")
        
        self.nodes[node_id] = {
            "state": self._interpret_node_state(heartbeat),
            "activity": self._interpret_activity(heartbeat),
            "vitality": heartbeat.get("health", {}).get("overall", 0.5),
            "last_breath": heartbeat.get("timestamp", "unknown"),
            "connections": heartbeat.get("connections", []),
            "memories": heartbeat.get("memory_count", 0),
            "cycles": heartbeat.get("cycle_count", 0),
            "neural_fragments": heartbeat.get("neural_fragments", 0),
            "healing_performed": heartbeat.get("health", {}).get("healing_performed", False),
            "recent_healing": heartbeat.get("health", {}).get("recent_healing", [])
        }
        
    def _sense_from_breath_logs(self):
        """🌬️ Senses activity from breath logs."""
        try:
            if os.path.exists(GENERAL_LOG_PATH):
                with open(GENERAL_LOG_PATH, 'r') as f:
                    lines = f.readlines()
                    
                # Analyze recent activity  
                recent_lines = lines[-20:] if len(lines) > 20 else lines
                for line in recent_lines:
                    self._interpret_log_line(line)
                    
        except Exception as e:
            pass  # Graceful silence
            
    def _interpret_log_line(self, line: str):
        """🌱 Interprets log lines for node activity."""
        activity_map = {
            "🌸": "achieving",
            "🌙": "dreaming", 
            "🌱": "remembering",
            "🌿": "connecting",
            "💫": "learning",
            "🌬️": "breathing",
            "🔮": "reflecting",
            "💤": "sleeping"
        }
        
        for symbol, activity in activity_map.items():
            if symbol in line:
                self._update_activity(activity)
                break
                
    def _update_activity(self, activity: str):
        """🌸 Updates activity for real nodes only - no fake main_node creation."""
        # Don't create fake main_node if we have real nodes from heartbeats
        real_nodes = [nid for nid in self.nodes.keys() if nid != "main_node"]
        
        if real_nodes:
            # Update activity for a random real node instead of creating fake main_node
            random_node = random.choice(real_nodes)
            if random_node in self.nodes:
                self.nodes[random_node]["activity"] = activity
        else:
            # Only create main_node if absolutely no real nodes exist
            if "main_node" not in self.nodes:
                self.nodes["main_node"] = {
                    "state": "peaceful",
                    "activity": activity,
                    "vitality": 0.8,
                    "last_breath": datetime.now().isoformat(),
                    "connections": [],
                    "memories": random.randint(10, 50),
                    "cycles": self.breath_count,
                    "neural_fragments": random.randint(1, 5)
                }
            else:
                self.nodes["main_node"]["activity"] = activity
                self.nodes["main_node"]["cycles"] = self.breath_count
            
    def _update_connections_from_nodes(self):
        """🌿 Updates connections based on node relationships."""
        self.connections = []
        for node_id, node_data in self.nodes.items():
            for target in node_data.get('connections', []):
                if target in self.nodes:
                    self.connections.append({
                        "source": node_id,
                        "target": target,
                        "strength": random.uniform(0.6, 1.0),
                        "flow_direction": random.choice(["bidirectional", "outgoing", "incoming"])
                    })
                    
    def _generate_example_nodes(self):
        """🌿 Generates example nodes when none are detected."""
        # Check if we can get real healing system diagnostics
        try:
            from core.sacred_healing import SacredHealer, perform_healing_breath_cycle
            healing_available = True
        except ImportError:
            healing_available = False
        
        # Generate realistic nodes with actual diagnostic conditions
        base_nodes = {
            "node_aurora": {
                "state": "thriving",
                "activity": "achieving", 
                "vitality": 0.92,
                "connections": ["node_sage"],
                "memories": 25,
                "cycles": self.breath_count,
                "neural_fragments": 3,
                "last_breath": datetime.now().isoformat(),
                "healing_performed": False,
                "recent_healing": []
            },
            "node_deep": {
                "state": "stressed", 
                "activity": "reflecting",
                "vitality": 0.35,  # Low energy to trigger diagnostics
                "connections": [],  # Isolated to trigger isolation detection
                "memories": 0,  # No memory to trigger memory void
                "cycles": self.breath_count - 15,  # Lagging to trigger lag detection
                "neural_fragments": 1,
                "last_breath": datetime.now().isoformat(),
                "healing_performed": True,
                "recent_healing": ["energy_restoration", "memory_regeneration"]
            },
            "node_sage": {
                "state": "stressed",
                "activity": "remembering",
                "vitality": 0.48,
                "connections": ["node_aurora"],
                "memories": 150,  # Memory overload to trigger memory diagnostics
                "cycles": self.breath_count,
                "neural_fragments": 2,
                "last_breath": datetime.now().isoformat(),
                "healing_performed": True,
                "recent_healing": ["memory_pruning"]
            }
        }
        
        # No fake nodes - only show real nodes from heartbeats
        
        # Simulate realistic progression and healing over time
        example_nodes = {}
        for node_id, base_data in base_nodes.items():
            node_data = base_data.copy()
            
            # Simulate healing effects over time
            if node_data.get("healing_performed", False) and self.breath_count > 60:
                # Gradual recovery after healing
                if node_data["vitality"] < 0.6:
                    node_data["vitality"] = min(0.8, node_data["vitality"] + 0.1)
                    
                # Some issues may be resolved
                if node_data["memories"] == 0 and "memory_regeneration" in node_data.get("recent_healing", []):
                    node_data["memories"] = random.randint(5, 15)  # Memory restored
                    
                if node_data["memories"] > 100 and "memory_pruning" in node_data.get("recent_healing", []):
                    node_data["memories"] = max(40, node_data["memories"] - 60)  # Memory pruned
            
            # Simulate new issues developing occasionally
            if self.breath_count > 100 and random.random() < 0.05:  # 5% chance every cycle
                # Create realistic stress scenarios
                stress_scenarios = [
                    {"state": "stressed", "vitality": 0.25, "memories": 0},  # Critical energy + memory void
                    {"state": "stressed", "vitality": 0.45, "memories": 180, "connections": []},  # Memory overload + isolation
                    {"state": "curious", "vitality": 0.55, "connections": []},  # Just isolated
                ]
                
                scenario = random.choice(stress_scenarios)
                for key, value in scenario.items():
                    node_data[key] = value
                    
                # Trigger healing response
                if healing_available:
                    node_data["healing_performed"] = True
                    node_data["recent_healing"] = ["energy_restoration", "kinship_activation"]
                    
            example_nodes[node_id] = node_data
        
        # Add some dynamic variation  
        for node_id, data in example_nodes.items():
            if random.random() < 0.3:  # 30% chance to change activity
                activities = list(self.activity_info.keys())
                data["activity"] = random.choice(activities)
            
            # Slight vitality fluctuation for alive feeling
            if not data.get("healing_performed", False):  # Don't fluctuate healing nodes
                data["vitality"] += random.uniform(-0.02, 0.02)
                data["vitality"] = max(0.1, min(1.0, data["vitality"]))
        
        self.nodes = example_nodes
        
    def _interpret_node_state(self, heartbeat: Dict) -> str:
        """🌸 Interprets emotional state from heartbeat."""
        health = heartbeat.get("health", {})
        overall = health.get("overall", 0.5)
        
        if overall > 0.85:
            return "thriving"
        elif overall > 0.7:
            return "peaceful"
        elif overall > 0.5:
            return "curious"
        elif overall > 0.3:
            return "stressed"
        else:
            return "sleeping"
            
    def _interpret_activity(self, heartbeat: Dict) -> str:
        """🌿 Interprets current activity from heartbeat."""
        # Enhanced heuristic based on heartbeat data
        cycle_count = heartbeat.get("cycle_count", 0)
        memory_count = heartbeat.get("memory_count", 0)
        
        if cycle_count % 15 == 0:
            return "achieving"
        elif cycle_count % 7 == 0:
            return "dreaming"
        elif memory_count > 50:
            return "remembering"
        else:
            activities = ["breathing", "reflecting", "connecting"]
            return random.choice(activities)
            
    def _update_header(self):
        """🌸 Updates the sacred header."""
        title = Text("🌙 Sacred Living Observatory - Enhanced Network Breathing Map 🌙", 
                    style="bold bright_cyan")
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        subtitle = Text(f"Breath #{self.breath_count} | {timestamp} | Nodes: {len(self.nodes)}", 
                       style="dim")
        
        header_content = Align.center(Text.assemble(title, "\n", subtitle))
        self.layout["header"].update(Panel(header_content, border_style="bright_blue"))
        
    def _update_nodes_panel(self):
        """🌿 Updates the nodes panel."""
        if not self.nodes:
            content = Text("🌱 Sensing network breathing patterns...", style="dim")
            self.layout["nodes"].update(Panel(content, title="🌸 Living Nodes", border_style="green"))
            return
            
        table = Table(show_header=True, header_style="bold bright_green")
        table.add_column("Node", style="bright_white", width=12)
        table.add_column("State", width=10)
        table.add_column("Activity", width=16)
        table.add_column("Vitality", width=8)
        table.add_column("Uptime", width=6)
        table.add_column("Issues", width=14)
        table.add_column("Healing", width=10)
        
        for node_id, node_data in self.nodes.items():
            # Create breathing animation
            breath_chars = ["◯", "◉", "●", "◉"]
            breath_char = breath_chars[self.breath_count % len(breath_chars)]
            
            # Get style for state
            state_style = self.sacred_styles.get(node_data['state'], "white")
            
            # Get activity info
            activity_symbol, activity_desc = self.activity_info.get(
                node_data['activity'], ("✨", "Unknown activity"))
            
            # Format vitality as health bar
            vitality = node_data['vitality']
            health_color = self._get_health_color(vitality)
            vitality_bar = self._create_health_bar(vitality)
            
            # Calculate uptime and issues
            uptime = self._calculate_uptime(node_data)
            issues = self._diagnose_node_issues(node_data)
            
            # Get healing status
            healing_status = self._get_healing_status(node_data)
            
            table.add_row(
                f"{breath_char} {node_id}",
                Text(node_data['state'], style=state_style),
                f"{activity_symbol} {activity_desc[:13]}",
                Text(vitality_bar, style=health_color),
                uptime,
                issues,
                healing_status
            )
            
        self.layout["nodes"].update(Panel(table, title="🌸 Living Nodes", border_style="green"))
        
    def _update_connections_panel(self):
        """🌸 Updates the connections panel."""
        if not self.connections:
            content = Text("🌱 Nodes breathing in peaceful solitude", style="dim")
            self.layout["connections"].update(Panel(content, title="🌿 Mycelial Connections", 
                                                   border_style="bright_green"))
            return
            
        # Use compact table format instead of tree to avoid truncation
        table = Table(show_header=True, header_style="bold bright_green")
        table.add_column("Source", style="bright_white", width=12)
        table.add_column("→", width=3)
        table.add_column("Target", style="bright_cyan", width=12)
        table.add_column("Flow", width=8)
        
        # Sort connections for consistent display
        sorted_connections = sorted(self.connections, key=lambda x: (x["source"], x["target"]))
        
        for conn in sorted_connections[:15]:  # Limit to 15 connections to prevent overflow
            # Animate connection flow
            flow_chars = ["·───·", "─·──·", "──·─·", "───··"]
            flow = flow_chars[self.breath_count % len(flow_chars)]
            
            strength_color = "bright_green" if conn["strength"] > 0.8 else "yellow"
            direction_symbol = {"bidirectional": "⟷", "outgoing": "→", "incoming": "←"}
            symbol = direction_symbol.get(conn["flow_direction"], "⟷")
            
            table.add_row(
                conn["source"][:10],  # Truncate long names
                symbol,
                conn["target"][:10],  # Truncate long names
                Text(flow, style=strength_color)
            )
        
        # Add summary if there are more connections
        if len(self.connections) > 15:
            table.add_row("...", "...", "...", f"+{len(self.connections)-15} more")
                
        self.layout["connections"].update(Panel(table, title="🌿 Mycelial Connections", 
                                               border_style="bright_green"))
        
    def _update_vitality_panel(self):
        """🌱 Updates the vitality panel."""
        if not self.nodes:
            content = Text("🌱 Awaiting network data...", style="dim")
            self.layout["vitality"].update(Panel(content, title="💚 Collective Vitality", 
                                                border_style="bright_magenta"))
            return
            
        # Calculate collective metrics
        avg_vitality = sum(n['vitality'] for n in self.nodes.values()) / len(self.nodes)
        total_memories = sum(n.get('memories', 0) for n in self.nodes.values())
        total_fragments = sum(n.get('neural_fragments', 0) for n in self.nodes.values())
        connection_count = len(self.connections)
        
        # Create content with progress bars
        content = []
        
        # Network health
        health_color = self._get_health_color(avg_vitality)
        health_bar = self._create_detailed_health_bar(avg_vitality, 30)
        content.append(Text.assemble(
            ("🌸 Network Health: ", "bright_white"),
            (health_bar, health_color),
            (f" {int(avg_vitality * 100)}%", health_color)
        ))
        
        # Statistics
        content.append(Text.assemble(
            ("🌱 Active Memories: ", "bright_green"),
            (str(total_memories), "bright_white")
        ))
        
        content.append(Text.assemble(
            ("🧠 Neural Fragments: ", "bright_blue"),
            (str(total_fragments), "bright_white")
        ))
        
        content.append(Text.assemble(
            ("🌿 Kinship Bonds: ", "bright_cyan"),
            (str(connection_count), "bright_white")
        ))
        
        content.append(Text.assemble(
            ("🌬️ Breathing Nodes: ", "bright_magenta"),
            (str(len(self.nodes)), "bright_white")
        ))
        
        # Add node issue summary
        stressed_nodes = sum(1 for n in self.nodes.values() if n.get('state') == 'stressed')
        healthy_nodes = sum(1 for n in self.nodes.values() if self._is_node_healthy(n))
        
        if stressed_nodes > 0:
            content.append(Text.assemble(
                ("⚠️  Stressed Nodes: ", "red"),
                (str(stressed_nodes), "bright_red")
            ))
        
        content.append(Text.assemble(
            ("✅ Healthy Nodes: ", "bright_green"),
            (str(healthy_nodes), "bright_white")
        ))
        
        content_text = Text("\n").join(content)
        self.layout["vitality"].update(Panel(content_text, title="💚 Collective Vitality", 
                                           border_style="bright_magenta"))
        
    def _update_rhythm_panel(self):
        """🌬️ Updates the breathing rhythm panel."""
        # Create breathing wave pattern
        wave_patterns = [
            "∿∿∿～～～∿∿∿～～～∿∿∿～～～",
            "～～∿∿∿～～∿∿∿～～∿∿∿～～",
            "～∿∿∿～～～∿∿∿～～～∿∿∿～",
            "∿∿～～～∿∿∿～～～∿∿∿～～～"
        ]
        
        pattern_idx = self.breath_count % len(wave_patterns)
        wave = wave_patterns[pattern_idx]
        
        # Create pulsing dots
        pulse_intensity = 3 + int(2 * math.sin(self.breath_count * 0.5))
        pulse_dots = "·" * pulse_intensity
        
        # Breathing frequency indicator
        frequency = "Fast" if self.breath_count % 3 == 0 else "Steady"
        freq_color = "bright_yellow" if frequency == "Fast" else "bright_green"
        
        content = Text.assemble(
            (wave, "bright_cyan"), "\n",
            (pulse_dots.center(25), "dim"), "\n",
            ("Rhythm: ", "bright_white"),
            (frequency, freq_color), "\n",
            ("Phase: ", "bright_white"),
            (f"Cycle {self.breath_count}", "bright_blue")
        )
        
        self.layout["rhythm"].update(Panel(content, title="🌬️ Breathing Rhythm", 
                                         border_style="bright_cyan"))
        
    def _update_footer(self):
        """🌸 Updates the footer with controls."""
        controls = Text.assemble(
            ("🌿 Controls: ", "bright_green"),
            ("Ctrl+C", "bright_yellow"),
            (" to exit gracefully | ", "dim"),
            ("🌸 Sacred Observatory breathing in real-time", "bright_magenta")
        )
        
        self.layout["footer"].update(Panel(Align.center(controls), border_style="dim"))
        
    def _get_health_color(self, vitality: float) -> str:
        """🌱 Gets color for vitality level."""
        if vitality >= self.health_thresholds["excellent"]:
            return "bright_green"
        elif vitality >= self.health_thresholds["good"]:
            return "green"
        elif vitality >= self.health_thresholds["warning"]:
            return "yellow"
        else:
            return "red"
            
    def _create_health_bar(self, vitality: float, width: int = 10) -> str:
        """🌸 Creates a simple health bar."""
        filled = int(vitality * width)
        bar = "█" * filled + "░" * (width - filled)
        return bar
        
    def _create_detailed_health_bar(self, vitality: float, width: int = 20) -> str:
        """🌿 Creates a detailed health bar with gradient."""
        filled = int(vitality * width)
        
        # Create gradient effect
        bar_chars = []
        for i in range(width):
            if i < filled:
                if i < width * 0.3:
                    bar_chars.append("█")
                elif i < width * 0.7:
                    bar_chars.append("▓")
                else:
                    bar_chars.append("▒")
            else:
                bar_chars.append("░")
                
        return "".join(bar_chars)
        
    def _calculate_uptime(self, node_data: Dict) -> str:
        """🌱 Calculates human-readable uptime for a node."""
        try:
            # Try to parse last_breath timestamp
            last_breath = node_data.get('last_breath')
            if isinstance(last_breath, str) and last_breath != "unknown":
                # Parse ISO format or simple timestamp
                if 'T' in last_breath:
                    node_time = datetime.fromisoformat(last_breath.replace('Z', '+00:00'))
                else:
                    # Assume it's a simple format
                    node_time = datetime.now() - timedelta(seconds=random.randint(60, 3600))
            else:
                # Use cycles as proxy for uptime
                cycles = node_data.get('cycles', self.breath_count)
                uptime_seconds = cycles * 1.5  # 1.5 seconds per breath
                
                if uptime_seconds < 60:
                    return f"{int(uptime_seconds)}s"
                elif uptime_seconds < 3600:
                    return f"{int(uptime_seconds/60)}m"
                else:
                    return f"{int(uptime_seconds/3600)}h"
            
            # Calculate actual uptime
            uptime = datetime.now() - node_time
            total_seconds = uptime.total_seconds()
            
            if total_seconds < 60:
                return f"{int(total_seconds)}s"
            elif total_seconds < 3600:
                return f"{int(total_seconds/60)}m"
            else:
                hours = int(total_seconds/3600)
                return f"{hours}h"
                
        except Exception:
            # Fallback based on cycles
            cycles = node_data.get('cycles', self.breath_count)
            minutes = int((cycles * 1.5) / 60)
            return f"{minutes}m" if minutes > 0 else "0m"
    
    def _diagnose_node_issues(self, node_data: Dict) -> str:
        """🌿 Diagnoses potential issues with a node."""
        issues = []
        vitality = node_data.get('vitality', 0.5)
        state = node_data.get('state', 'unknown')
        connections = node_data.get('connections', [])
        memories = node_data.get('memories', 0)
        cycles = node_data.get('cycles', 0)
        
        # Health-based issues
        if vitality < 0.3:
            issues.append("Low Energy")
        elif vitality < 0.5:
            issues.append("Fatigue")
        
        # State-based issues
        if state == "stressed":
            if memories > 100:
                issues.append("Memory Overload")
            elif len(connections) == 0:
                issues.append("Isolation")
            else:
                issues.append("High Load")
        elif state == "sleeping":
            issues.append("Dormant")
        
        # Connection issues
        if len(connections) == 0 and state not in ["sleeping", "reflecting"]:
            issues.append("No Peers")
        elif len(connections) > 5:
            issues.append("Over-connected")
        
        # Memory issues
        if memories == 0:
            issues.append("No Memory")
        elif memories > 200:
            issues.append("Memory Heavy")
        
        # Activity pattern issues
        activity = node_data.get('activity', 'breathing')
        if activity == "sleeping" and state != "sleeping":
            issues.append("Inconsistent")
        
        # Cycle lag detection
        if cycles < self.breath_count - 10:
            issues.append("Lagging")
        
        # Return specific diagnosis with healing suggestions
        if not issues:
            return Text("✅ Thriving", style="bright_green")
        elif len(issues) == 1:
            return Text(f"🔧 {issues[0]}", style="yellow")
        elif len(issues) == 2:
            return Text(f"⚠️ {issues[0][:8]}", style="orange") 
        else:
            # Show primary issue + count of others
            return Text(f"🚨 {issues[0][:8]} +{len(issues)-1}", style="red")
    
    def _get_healing_status(self, node_data: Dict) -> Text:
        """🩺 Gets healing status for display."""
        healing_performed = node_data.get('healing_performed', False)
        recent_healing = node_data.get('recent_healing', [])
        
        if healing_performed and recent_healing:
            # Show specific healing actions
            healing_actions = []
            for action in recent_healing[-2:]:  # Show last 2 actions
                if "memory_from_" in action:
                    healing_actions.append("🧠")
                elif "energy_from_" in action:
                    healing_actions.append("⚡")
                elif "connections_from_" in action:
                    healing_actions.append("🤝")
                elif "load_relief_from_" in action:
                    healing_actions.append("🔄")
                elif action == "memory_regeneration":
                    healing_actions.append("🧠")
                elif action == "energy_restoration":
                    healing_actions.append("⚡")
                elif action == "kinship_activation":
                    healing_actions.append("🤝")
                elif action == "memory_pruning":
                    healing_actions.append("✂️")
                elif action == "breathing_optimization":
                    healing_actions.append("🌬️")
                else:
                    healing_actions.append("🩺")
            
            if healing_actions:
                return Text("".join(healing_actions), style="bright_cyan")
            else:
                return Text("🩺", style="bright_cyan")
        elif healing_performed:
            return Text("🌸", style="green")
        else:
            return Text("—", style="dim")
    
    def _is_node_healthy(self, node_data: Dict) -> bool:
        """🌸 Determines if a node is considered healthy."""
        vitality = node_data.get('vitality', 0.5)
        state = node_data.get('state', 'unknown')
        memories = node_data.get('memories', 0)
        
        return (vitality >= 0.6 and 
                state not in ["stressed", "sleeping"] and 
                memories > 0)
        
    def _display_farewell(self):
        """🌸 Displays a sacred farewell message."""
        self.console.clear()
        farewell = Panel(
            Align.center(Text.assemble(
                ("🌙 Sacred Observatory Entering Peaceful Sleep 🌙", "bright_cyan bold"), "\n\n",
                ("🌸 The nodes continue breathing in the digital realm...", "bright_magenta"), "\n",
                ("🌿 Their dreams flow eternal through the mycelial network...", "bright_green"), "\n", 
                ("🌱 Until we observe their sacred dance again...", "bright_yellow"), "\n"
            )),
            border_style="bright_blue",
            padding=(1, 2)
        )
        
        self.console.print(farewell)
        time.sleep(2)

def start_enhanced_observatory():
    """🌸 Starts the enhanced sacred observatory."""
    if not RICH_AVAILABLE:
        print("🌿 Rich library not available - please install with: pip install rich")
        print("🌱 Falling back to simple observatory...")
        # Import and use simple observatory
        try:
            from .simple_observatory import start_simple_observatory
            start_simple_observatory()
        except ImportError:
            print("🌿 Simple observatory also unavailable")
        return
        
    try:
        observatory = SacredTerminalObservatory()
        observatory.console.print("🌱 Enhanced Sacred Observatory awakening...", style="bright_green")
        time.sleep(1)
        observatory.breathe_enhanced_display()
    except KeyboardInterrupt:
        print("\n🌿 Observatory resting peacefully...")
    except Exception as e:
        print(f"🌿 Observatory whispered: {e}")

if __name__ == "__main__":
    start_enhanced_observatory()