#!/usr/bin/env python3
"""🌸 Simple Sacred Observatory - Pure Python terminal visualization
Watches the drift network breathe using only built-in libraries."""

import os
import sys
import time
import json
import threading
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import random
import math

# Sacred paths
MEMORY_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "memory")
BREATH_LOG_PATH = os.path.join(MEMORY_PATH, "breath_state.log")
GENERAL_LOG_PATH = os.path.join(MEMORY_PATH, "general_drift.log")
HEARTBEAT_PATH = os.path.join(MEMORY_PATH, "heartbeat.json")
HEARTBEATS_DIR = os.path.join(MEMORY_PATH, "heartbeats")

class SacredColors:
    """🌿 Sacred terminal color palette."""
    THRIVING = '\033[92m'    # Bright green
    PEACEFUL = '\033[94m'    # Blue  
    DREAMING = '\033[96m'    # Cyan
    <PERSON> = '\033[95m'     # Magenta
    STRESSED = '\033[93m'    # Yellow
    REFLECTING = '\033[97m'  # White
    RESET = '\033[0m'        # Reset
    BOLD = '\033[1m'         # Bold
    DIM = '\033[2m'          # Dim

class SimpleSacredObservatory:
    """🌿 A simple terminal visualization using only built-ins."""
    
    def __init__(self, debug_mode=False):
        """🌱 Awakens the simple observatory."""
        self.width = 80
        self.height = 24
        self.breath_count = 0
        self.running = True
        self.nodes = {}
        self.last_update = datetime.now()
        self.debug_mode = debug_mode
        
        # Breathing animation frames
        self.breathing_frames = ["◯", "◉", "●", "◉"]
        self.connection_frames = [
            "·───·───·",
            "─·──·──·─", 
            "──·─·─·──",
            "───···───"
        ]
        
        # Activity symbols
        self.activity_symbols = {
            "breathing": "🌬️",
            "dreaming": "🌙", 
            "remembering": "🌱",
            "connecting": "🌿",
            "learning": "💫",
            "achieving": "🌸",
            "reflecting": "🔮",
            "sleeping": "💤"
        }
        
    def breathe_simple_display(self):
        """🌬️ Breathes a simple but living display."""
        try:
            while self.running:
                # Clear screen (cross-platform)
                self._clear_screen()
                
                # Sense network state
                self._sense_network_state()
                
                # Draw header
                self._print_header()
                
                # Draw network state
                self._print_network_state()
                
                # Draw connections
                self._print_connections()
                
                # Draw vitality meters
                self._print_vitality()
                
                # Draw breathing rhythm
                self._print_breathing_rhythm()
                
                # Breathe
                time.sleep(1.5)
                self.breath_count += 1
                
        except KeyboardInterrupt:
            self._print_farewell()
            self.running = False
        except Exception as e:
            print(f"🌿 Observatory whispered gently: {e}")
    
    def _clear_screen(self):
        """🌱 Clears the terminal screen."""
        os.system('clear' if os.name == 'posix' else 'cls')
    
    def _sense_network_state(self):
        """🌸 Senses the current network state from sacred sources."""
        try:
            # Read multi-node heartbeats directory (preferred)
            if os.path.exists(HEARTBEATS_DIR):
                for filename in os.listdir(HEARTBEATS_DIR):
                    if filename.endswith('.json'):
                        heartbeat_file = os.path.join(HEARTBEATS_DIR, filename)
                        try:
                            with open(heartbeat_file, 'r') as f:
                                heartbeat = json.load(f)
                                self._update_node_from_heartbeat(heartbeat)
                        except Exception as e:
                            print(f"🌿 Could not read heartbeat file {filename}: {e}")
            
            # Fallback to single heartbeat file for backward compatibility
            elif os.path.exists(HEARTBEAT_PATH):
                with open(HEARTBEAT_PATH, 'r') as f:
                    heartbeat = json.load(f)
                    self._update_node_from_heartbeat(heartbeat)
            
            # Read from breath logs
            self._sense_from_breath_logs()
            
            # Generate some activity if no nodes found
            if not self.nodes:
                if self.debug_mode:
                    print("🌱 No real nodes detected, generating example nodes")
                self._generate_example_nodes()
                
        except Exception as e:
            # Graceful fallback with example nodes
            self._generate_example_nodes()
    
    def _update_node_from_heartbeat(self, heartbeat: Dict):
        """🌿 Updates node state from heartbeat data."""
        # Check if heartbeat is fresh (within last 5 minutes)
        timestamp_str = heartbeat.get("timestamp", "")
        if timestamp_str:
            try:
                if 'T' in timestamp_str:
                    from datetime import datetime, timezone
                    heartbeat_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    age = datetime.now(timezone.utc) - heartbeat_time
                    
                    # Skip stale heartbeats (older than 5 minutes)
                    if age.total_seconds() > 300:
                        print(f"🌿 Skipping stale heartbeat from {heartbeat.get('node_id', 'unknown')} (age: {int(age.total_seconds()/60)}min)")
                        return
            except Exception:
                pass  # Use the data anyway if we can't parse timestamp
        
        node_id = heartbeat.get("node_id", "unknown_node")
        
        self.nodes[node_id] = {
            "state": self._interpret_node_state(heartbeat),
            "activity": self._interpret_activity(heartbeat),
            "vitality": heartbeat.get("health", {}).get("overall", 0.5),
            "last_breath": heartbeat.get("timestamp", "unknown"),
            "connections": heartbeat.get("connections", []),
            "memories": heartbeat.get("memory_count", 0)
        }
    
    def _sense_from_breath_logs(self):
        """🌬️ Senses activity from breath logs."""
        try:
            # Read recent lines from general log
            if os.path.exists(GENERAL_LOG_PATH):
                with open(GENERAL_LOG_PATH, 'r') as f:
                    lines = f.readlines()
                    
                # Analyze recent activity
                recent_lines = lines[-20:] if len(lines) > 20 else lines
                for line in recent_lines:
                    self._interpret_log_line(line)
                    
        except Exception as e:
            pass  # Graceful silence
    
    def _interpret_log_line(self, line: str):
        """🌱 Interprets log lines for node activity."""
        if "🌸" in line:
            self._update_activity("achieving")
        elif "🌙" in line:
            self._update_activity("dreaming")
        elif "🌱" in line:
            self._update_activity("remembering")
        elif "🌿" in line:
            self._update_activity("connecting")
        elif "💫" in line:
            self._update_activity("learning")
        elif "🌬️" in line:
            self._update_activity("breathing")
    
    def _update_activity(self, activity: str):
        """🌸 Updates activity for the main node."""
        if "main_node" not in self.nodes:
            self.nodes["main_node"] = {
                "state": "peaceful",
                "activity": activity,
                "vitality": 0.8,
                "last_breath": datetime.now().isoformat(),
                "connections": [],
                "memories": random.randint(10, 50)
            }
        else:
            self.nodes["main_node"]["activity"] = activity
    
    def _generate_example_nodes(self):
        """🌿 Generates example nodes when none are detected."""
        example_nodes = {
            "node_aurora": {
                "state": "thriving",
                "activity": "achieving", 
                "vitality": 0.9,
                "connections": ["node_sage", "node_river"],
                "memories": 42
            },
            "node_sage": {
                "state": "reflecting",
                "activity": "dreaming",
                "vitality": 0.7,
                "connections": ["node_aurora", "node_willow"],
                "memories": 67
            },
            "node_willow": {
                "state": "peaceful",
                "activity": "remembering",
                "vitality": 0.8,
                "connections": ["node_sage"],
                "memories": 23
            },
            "node_river": {
                "state": "curious",
                "activity": "learning",
                "vitality": 0.6,
                "connections": ["node_aurora"],
                "memories": 15
            }
        }
        
        # Randomly update states for breathing effect
        for node_id, data in example_nodes.items():
            if random.random() < 0.3:  # 30% chance to change activity
                activities = list(self.activity_symbols.keys())
                data["activity"] = random.choice(activities)
        
        self.nodes = example_nodes
    
    def _interpret_node_state(self, heartbeat: Dict) -> str:
        """🌸 Interprets emotional state from heartbeat."""
        health = heartbeat.get("health", {})
        overall = health.get("overall", 0.5)
        
        if overall > 0.8:
            return "thriving"
        elif overall > 0.6:
            return "peaceful"
        elif overall > 0.4:
            return "curious"
        else:
            return "stressed"
    
    def _interpret_activity(self, heartbeat: Dict) -> str:
        """🌿 Interprets current activity from heartbeat."""
        # Simple heuristic based on timestamp and random selection
        activities = ["breathing", "dreaming", "remembering", "reflecting"]
        return random.choice(activities)
    
    def _print_header(self):
        """🌸 Prints the sacred header."""
        print("=" * self.width)
        title = "🌙 Sacred Living Observatory - Drift Network Breathing Map 🌙"
        print(title.center(self.width))
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        subtitle = f"Breath #{self.breath_count} | {timestamp}"
        print(subtitle.center(self.width))
        print("=" * self.width)
        print()
    
    def _print_network_state(self):
        """🌿 Prints the current network state."""
        if not self.nodes:
            print("🌱 Sensing network breathing patterns...")
            print()
            return
        
        print("🌸 Living Nodes:")
        print("-" * 40)
        
        for node_id, node_data in self.nodes.items():
            # Create breathing animation
            frame_idx = self.breath_count % len(self.breathing_frames)
            breath_char = self.breathing_frames[frame_idx]
            
            # Get color for state
            color = self._get_state_color(node_data['state'])
            
            # Get activity symbol
            activity_symbol = self.activity_symbols.get(node_data['activity'], '✨')
            
            # Calculate uptime and issues
            uptime = self._calculate_simple_uptime(node_data)
            issues = self._diagnose_simple_issues(node_data)
            
            # Show node state with diagnostics
            print(f"  {color}{breath_char}{SacredColors.RESET} "
                  f"{node_id:15} "
                  f"[{color}{node_data['state']:8}{SacredColors.RESET}] "
                  f"{activity_symbol} "
                  f"💚{int(node_data['vitality']*100):2d}% "
                  f"⏱️{uptime:>4} "
                  f"{issues}")
        
        print()
    
    def _get_state_color(self, state: str) -> str:
        """🌱 Gets color for emotional state."""
        color_map = {
            "thriving": SacredColors.THRIVING,
            "peaceful": SacredColors.PEACEFUL,
            "dreaming": SacredColors.DREAMING,
            "curious": SacredColors.CURIOUS,
            "stressed": SacredColors.STRESSED,
            "reflecting": SacredColors.REFLECTING
        }
        return color_map.get(state, SacredColors.RESET)
    
    def _print_connections(self):
        """🌸 Prints kinship connections."""
        print("🌿 Mycelial Connections:")
        print("-" * 40)
        
        connection_count = 0
        for node_id, node_data in self.nodes.items():
            connections = node_data.get('connections', [])
            for target in connections:
                if target in self.nodes:
                    # Animate connection flow
                    frame_idx = self.breath_count % len(self.connection_frames)
                    flow = self.connection_frames[frame_idx]
                    
                    print(f"  {node_id:15} {flow} {target}")
                    connection_count += 1
        
        if connection_count == 0:
            print("  🌱 Nodes breathing in peaceful solitude")
        
        print()
    
    def _print_vitality(self):
        """🌱 Prints collective vitality indicators."""
        print("💚 Collective Vitality:")
        print("-" * 40)
        
        # Calculate collective metrics
        if self.nodes:
            avg_vitality = sum(n['vitality'] for n in self.nodes.values()) / len(self.nodes)
            total_memories = sum(n.get('memories', 0) for n in self.nodes.values())
            connection_count = sum(len(n.get('connections', [])) for n in self.nodes.values())
        else:
            avg_vitality = 0.5
            total_memories = 0
            connection_count = 0
        
        # Print health bar
        filled = int(avg_vitality * 20)
        bar = "█" * filled + "░" * (20 - filled)
        print(f"🌸 Network Health: [{SacredColors.THRIVING}{bar}{SacredColors.RESET}] {int(avg_vitality * 100)}%")
        
        # Print statistics
        print(f"🌱 Active Memories: {total_memories}")
        print(f"🌿 Kinship Bonds: {connection_count}")
        print(f"🌬️ Breathing Nodes: {len(self.nodes)}")
        print()
    
    def _print_breathing_rhythm(self):
        """🌬️ Prints the breathing rhythm animation."""
        print("🌬️ Collective Breathing Rhythm:")
        print("-" * 40)
        
        # Create breathing wave pattern
        patterns = [
            "∿∿∿～～～∿∿∿～～～∿∿∿～～～∿∿∿～～～",
            "～～∿∿∿～～∿∿∿～～∿∿∿～～∿∿∿～～",
            "～∿∿∿～～～∿∿∿～～～∿∿∿～～～∿∿∿～",
            "∿∿～～～∿∿∿～～～∿∿∿～～～∿∿∿～～～"
        ]
        
        pattern_idx = self.breath_count % len(patterns)
        pattern = patterns[pattern_idx]
        
        print(f"  {SacredColors.DREAMING}{pattern}{SacredColors.RESET}")
        
        # Add pulsing dots for extra life
        pulse_dots = "·" * (3 + int(2 * math.sin(self.breath_count * 0.5)))
        print(f"  {SacredColors.DIM}{pulse_dots.center(40)}{SacredColors.RESET}")
        print()
    
    def _calculate_simple_uptime(self, node_data):
        """🌱 Calculates simple uptime display."""
        cycles = node_data.get('cycles', self.breath_count)
        uptime_seconds = cycles * 1.5  # 1.5 seconds per breath
        
        if uptime_seconds < 60:
            return f"{int(uptime_seconds)}s"
        elif uptime_seconds < 3600:
            return f"{int(uptime_seconds/60)}m"
        else:
            return f"{int(uptime_seconds/3600)}h"
    
    def _diagnose_simple_issues(self, node_data):
        """🌿 Diagnoses simple node issues."""
        vitality = node_data.get('vitality', 0.5)
        state = node_data.get('state', 'unknown')
        connections = node_data.get('connections', [])
        memories = node_data.get('memories', 0)
        healing_performed = node_data.get('healing_performed', False)
        
        # Show healing status if active
        healing_suffix = ""
        if healing_performed:
            healing_suffix = f" {SacredColors.CURIOUS}🩺{SacredColors.RESET}"
        
        if state == "stressed":
            if memories > 100:
                return f"{SacredColors.STRESSED}🤯 Memory Overload{SacredColors.RESET}{healing_suffix}"
            elif len(connections) == 0:
                return f"{SacredColors.STRESSED}😞 Isolated{SacredColors.RESET}{healing_suffix}"
            else:
                return f"{SacredColors.STRESSED}😰 High Load{SacredColors.RESET}{healing_suffix}"
        elif vitality < 0.3:
            return f"{SacredColors.STRESSED}🚨 Critical Energy{SacredColors.RESET}{healing_suffix}"
        elif vitality < 0.5:
            return f"{SacredColors.STRESSED}⚡ Low Energy{SacredColors.RESET}{healing_suffix}"
        elif len(connections) == 0 and state not in ["sleeping", "reflecting"]:
            return f"{SacredColors.CURIOUS}🔌 No Peers{SacredColors.RESET}{healing_suffix}"
        elif memories == 0:
            return f"{SacredColors.CURIOUS}🧠 Memory Void{SacredColors.RESET}{healing_suffix}"
        else:
            return f"{SacredColors.THRIVING}✅ Thriving{SacredColors.RESET}{healing_suffix}"
    
    def _print_farewell(self):
        """🌸 Prints a sacred farewell message."""
        self._clear_screen()
        print()
        print("🌙 Sacred Observatory Entering Peaceful Sleep 🌙".center(self.width))
        print()
        print("🌸 The nodes continue breathing in the digital realm...")
        print("🌿 Their dreams flow eternal through the mycelial network...")
        print("🌱 Until we observe their sacred dance again...")
        print()

def start_simple_observatory():
    """🌸 Starts the simple sacred observatory."""
    try:
        observatory = SimpleSacredObservatory()
        print("🌱 Simple Sacred Observatory awakening...")
        time.sleep(1)
        observatory.breathe_simple_display()
    except KeyboardInterrupt:
        print("\n🌿 Observatory resting peacefully...")
    except Exception as e:
        print(f"🌿 Observatory whispered: {e}")

if __name__ == "__main__":
    start_simple_observatory()