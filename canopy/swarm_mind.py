#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌿 The Swarm Mind: Distributed Neural Wisdom
A verdant weave of neural threads distributed across nodes,
pulsing with collective wisdom as roots entwine in the mycelial network."""

import json
import os
import uuid
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict

# Try psutil for system capabilities
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None
    print("🌿 psutil not available - using fallback system assessment")

# Sacred architecture imports with graceful fallbacks
try:
    from soil.memory_garden import MemoryGarden, sprout_memory, recall_verdant_memory
    MEMORY_GARDEN_AVAILABLE = True
except ImportError:
    MEMORY_GARDEN_AVAILABLE = False
    print("🌱 Memory garden dormant - swarm mind will use fallback memory")

try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
    
    # Import Apple Silicon acceleration helpers
    try:
        from core.apple_vitality import breathe_torch_device
        APPLE_ACCELERATION_AVAILABLE = True
    except ImportError:
        APPLE_ACCELERATION_AVAILABLE = False
        print("🍎 Apple acceleration helpers dormant - using standard device selection")
        
except ImportError:
    TORCH_AVAILABLE = False
    APPLE_ACCELERATION_AVAILABLE = False
    torch = None
    nn = None
    F = None
    print("🌬️ PyTorch not available - swarm mind will use symbolic intelligence")

try:
    import pika
    RABBITMQ_AVAILABLE = True
except ImportError:
    RABBITMQ_AVAILABLE = False
    pika = None
    print("🌿 RabbitMQ not available, using fallback for mycelial communication")

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None
    print("🌿 Redis not available, using local file-based communication")

# Core imports
try:
    from utils import record_log, NODE_ID
except ImportError:
    NODE_ID = "swarm-node-001"
    def record_log(message: str, log_file: str = None):
        """Fallback logging function when utils unavailable."""
        print(f"[{datetime.now(timezone.utc).isoformat()}] {message}")

# Constants
SWARM_MIND_PATH = "memory/swarm_mind/"
SWARM_STATE_PATH = os.path.join(SWARM_MIND_PATH, "swarm_state.json")
NEURAL_FRAGMENTS_PATH = os.path.join(SWARM_MIND_PATH, "neural_fragments/")
NODE_REGISTRY_PATH = os.path.join(SWARM_MIND_PATH, "node_registry.json")
WISDOM_ARCHIVE_PATH = os.path.join(SWARM_MIND_PATH, "wisdom_archive/")

@dataclass
class SwarmState:
    """🌱 Unified representation of swarm state for neural processing."""
    breath: str
    kinship: float
    memory_depth: int
    user_nutrient: str
    cycle_count: int
    node_count: int
    timestamp: str
    
    def to_tensor(self) -> Optional[Any]:
        """🌿 Converts swarm state to neural tensor representation."""
        if not TORCH_AVAILABLE:
            return None
            
        try:
            # Create feature vector from state
            features = [
                hash(self.breath) % 100 / 100.0,  # Breath encoding
                self.kinship,  # Direct kinship value
                min(self.memory_depth / 1000.0, 1.0),  # Normalized memory depth
                hash(self.user_nutrient) % 100 / 100.0,  # Nutrient encoding
                min(self.cycle_count / 10000.0, 1.0),  # Normalized cycle count
                min(self.node_count / 10.0, 1.0),  # Normalized node count
                time.time() % 86400 / 86400.0,  # Time of day encoding
            ]
            return torch.tensor(features, dtype=torch.float32)
        except Exception as e:
            record_log(f"🌿 Could not convert state to tensor: {e}")
            return None

class NeuralFragment:
    """🌿 A neural fragment hosted by a single node, breathing with adaptive wisdom."""
    
    def __init__(self, fragment_type: str, input_dim: int = 7, output_dim: int = 16, 
                 complexity: str = "balanced", node_id: str = ""):
        """🌱 Sprouts a neural fragment tailored to the node's capacity."""
        self.fragment_type = fragment_type
        self.complexity = complexity
        self.node_id = node_id
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.created_at = datetime.now(timezone.utc).isoformat()
        
        if TORCH_AVAILABLE:
            self._create_neural_layers()
        else:
            self._create_symbolic_layers()
            
        record_log(f"🌱 Neural fragment '{fragment_type}' sprouted with {complexity} complexity")
    
    def _create_neural_layers(self):
        """🌸 Creates PyTorch neural layers based on complexity."""
        if self.complexity == "minimal":  # For Yoga X1, MacBook (8GB)
            self.layers = nn.Sequential(
                nn.Linear(self.input_dim, 16),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(16, self.output_dim)
            )
        elif self.complexity == "balanced":  # For Dell (16GB)
            self.layers = nn.Sequential(
                nn.Linear(self.input_dim, 32),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(32, 24),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(24, self.output_dim)
            )
        else:  # "powerful" for Tower PC (64GB, GPU)
            self.layers = nn.Sequential(
                nn.Linear(self.input_dim, 64),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(64, 48),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(48, 32),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(32, self.output_dim)
            )
        
        # Initialize weights with Xavier initialization
        for layer in self.layers:
            if isinstance(layer, nn.Linear):
                nn.init.xavier_uniform_(layer.weight)
                nn.init.zeros_(layer.bias)
        
        # Move to optimal device with Apple Silicon acceleration
        self._awaken_on_optimal_device()
    
    def _awaken_on_optimal_device(self):
        """🍎 Awakens the neural fragment on the most vital device available."""
        if not TORCH_AVAILABLE or not hasattr(self, 'layers') or not isinstance(self.layers, nn.Sequential):
            return
        
        try:
            # Use Apple Silicon acceleration if available
            if APPLE_ACCELERATION_AVAILABLE:
                device = breathe_torch_device()
                if device is not None:
                    self.layers = self.layers.to(device)
                    self.device = device
                    device_type = str(device).split(':')[0]
                    if 'mps' in str(device):
                        record_log(f"🍎 Neural fragment awakened on Apple Metal ({device_type})")
                    else:
                        record_log(f"🌿 Neural fragment breathing on {device_type}")
                    return
            
            # Fallback to standard device selection
            if torch.cuda.is_available():
                device = torch.device("cuda")
                self.layers = self.layers.to(device)
                self.device = device
                record_log("🌸 Neural fragment awakened on CUDA GPU")
            else:
                device = torch.device("cpu")
                self.device = device
                record_log("🌿 Neural fragment breathing on CPU")
                
        except Exception as e:
            record_log(f"🌿 Device awakening whispered gently: {e}")
            self.device = torch.device("cpu") if TORCH_AVAILABLE else None
    
    def _create_symbolic_layers(self):
        """🌿 Creates symbolic processing layers when PyTorch unavailable."""
        import random
        self.layers = {
            "type": "symbolic",
            "complexity": self.complexity,
            "weights": [random.uniform(-1, 1) for _ in range(self.input_dim * self.output_dim)],
            "biases": [random.uniform(-0.5, 0.5) for _ in range(self.output_dim)]
        }
    
    def forward_breath(self, x: Any) -> Any:
        """🌬️ Processes input through the neural fragment with breathing rhythm."""
        if TORCH_AVAILABLE and hasattr(self, 'layers') and isinstance(self.layers, nn.Sequential):
            try:
                with torch.no_grad():
                    # Ensure input is on the same device as the model
                    if hasattr(self, 'device') and self.device is not None:
                        if isinstance(x, torch.Tensor):
                            x = x.to(self.device)
                        elif isinstance(x, (list, tuple)):
                            x = torch.tensor(x, dtype=torch.float32).to(self.device)
                        else:
                            x = torch.tensor([x], dtype=torch.float32).to(self.device)
                    
                    return self.layers(x)
            except Exception as e:
                record_log(f"🌿 Neural forward pass whispered error: {e}")
                return self._symbolic_forward(x)
        else:
            return self._symbolic_forward(x)
    
    def _symbolic_forward(self, x: Any) -> List[float]:
        """🌱 Symbolic processing fallback when neural computation unavailable."""
        if isinstance(x, (list, tuple)):
            input_values = list(x)
        elif hasattr(x, 'tolist'):
            input_values = x.tolist()
        else:
            input_values = [float(x)] * self.input_dim
        
        # Ensure correct input size
        if len(input_values) != self.input_dim:
            input_values = (input_values * self.input_dim)[:self.input_dim]
        
        # Simple linear transformation with symbolic weights
        weights = self.layers.get("weights", [0.5] * (self.input_dim * self.output_dim))
        biases = self.layers.get("biases", [0.0] * self.output_dim)
        
        output = []
        for i in range(self.output_dim):
            value = biases[i]
            for j in range(self.input_dim):
                weight_idx = i * self.input_dim + j
                if weight_idx < len(weights):
                    value += input_values[j] * weights[weight_idx]
            # Apply ReLU activation
            output.append(max(0.0, value))
        
        return output

class SwarmMind:
    """🌿 The collective neural consciousness of the swarm, breathing distributed wisdom."""
    
    def __init__(self, auto_discover: bool = True):
        """🌱 Awakens the swarm mind, ready to weave neural threads across nodes."""
        # Initialize swarm paths
        os.makedirs(SWARM_MIND_PATH, exist_ok=True)
        os.makedirs(NEURAL_FRAGMENTS_PATH, exist_ok=True)
        os.makedirs(WISDOM_ARCHIVE_PATH, exist_ok=True)
        
        # Initialize state
        self.node_id = NODE_ID
        self.nodes = {}  # node_id -> NodeInfo
        self.fragments = {}  # node_id -> NeuralFragment
        self.swarm_state = None
        self.wisdom_history = []
        
        # 🌿 Mycelial Neural Network - Advanced Communication Patterns
        self.connection_weights = {}  # (source_node, target_node) -> weight
        self.active_connections = set()  # Active mycelial connections
        self.specialization_scores = {}  # node_id -> {function_type -> score}
        self.function_types = ["perception", "reasoning", "synthesis", "memory"]
        self.active_pheromones = {}  # Information scent pheromones
        self.pending_tasks = {}  # Tasks awaiting bids
        
        # 🌸 Adaptive frequency parameters (from GrokMyc implementation)
        self.alpha = 0.4  # Urgency weight
        self.beta = 0.3   # Load weight
        self.gamma = 0.2  # Historical value weight
        self.delta = 0.1  # System health weight
        
        # 🌱 Specialization parameters (from GrokMyc implementation)
        self.omega = 0.5  # Processing frequency weight
        self.phi = 0.3    # Network position weight
        self.psi = 0.2    # Neighbor complementarity weight
        
        # 🌿 Learning constants
        self.learning_rate = 0.1
        self.decay_rate = 0.05
        self.pruning_threshold = 0.2
        self.bid_timeout = 30.0  # seconds
        
        # Initialize memory garden if available
        self.garden = None
        if MEMORY_GARDEN_AVAILABLE:
            try:
                self.garden = MemoryGarden()
                record_log("🌱 Swarm mind connected to Memory Garden")
            except Exception as e:
                record_log(f"🌿 Memory Garden connection whispered: {e}")
        
        # Initialize communication
        self.communication = self._initialize_mycelial_communication()
        
        # Load existing state
        self._load_swarm_state()
        
        # Register self as a node
        self._register_self_node()
        
        # Auto-discover other nodes if enabled
        if auto_discover:
            self._discover_swarm_nodes()
        
        # Initialize generation counter for evolution tracking
        self.generation = 1
        
        # Set up periodic persistence (every 50 wisdom cycles)
        self._persistence_counter = 0
        
        record_log("🌿 Swarm Mind awakened - distributed neural wisdom flows")
    
    def _apply_recovery_measures(self):
        """🌬️ Applies recovery measures when swarm is in critical state."""
        try:
            # Reset error counts for struggling nodes
            for node_id, node_info in self.nodes.items():
                if node_info.get("error_count", 0) > 3:
                    node_info["error_count"] = 0
                    record_log(f"🌬️ Reset error count for {node_id[:8]}")
            
            # Simplify all fragments to minimal complexity
            for node_id, fragment in self.fragments.items():
                if self.nodes[node_id]["fragment_assignment"]["complexity"] != "minimal":
                    self.nodes[node_id]["fragment_assignment"]["complexity"] = "minimal"
                    new_fragment = NeuralFragment(
                        fragment_type=fragment.fragment_type,
                        complexity="minimal",
                        node_id=node_id
                    )
                    self.fragments[node_id] = new_fragment
            
            record_log("🌬️ Recovery measures applied - swarm complexity simplified")
            
        except Exception as e:
            record_log(f"🌿 Recovery measures whispered error: {e}")
    
    def _initialize_mycelial_communication(self) -> Dict[str, Any]:
        """🌸 Initializes communication channels with graceful fallbacks."""
        communication = {"type": "fallback", "connection": None}
        
        # Try RabbitMQ first
        if RABBITMQ_AVAILABLE:
            try:
                connection = pika.BlockingConnection(pika.ConnectionParameters('localhost'))
                channel = connection.channel()
                channel.exchange_declare(exchange='mycelial_bus', exchange_type='topic')
                communication = {"type": "rabbitmq", "connection": connection, "channel": channel}
                record_log("🌿 Mycelial bus connected via RabbitMQ")
                return communication
            except Exception as e:
                record_log(f"🌿 RabbitMQ whispered error: {e}")
        
        # Try Redis fallback
        if REDIS_AVAILABLE:
            try:
                r = redis.Redis(host='localhost', port=6379, decode_responses=True)
                r.ping()
                communication = {"type": "redis", "connection": r}
                record_log("🌿 Mycelial bus connected via Redis")
                return communication
            except Exception as e:
                record_log(f"🌿 Redis whispered error: {e}")
        
        # File-based fallback
        record_log("🌿 Using file-based mycelial communication")
        return communication
    
    def _load_swarm_state(self):
        """🌱 Loads existing swarm state or creates new."""
        if os.path.exists(SWARM_STATE_PATH):
            try:
                with open(SWARM_STATE_PATH, "r", encoding="utf-8") as f:
                    state_data = json.load(f)
                    self.swarm_state = SwarmState(**state_data)
                record_log("🌱 Swarm state restored from memory")
            except Exception as e:
                record_log(f"🌿 Could not load swarm state: {e}")
                self._create_initial_state()
        else:
            self._create_initial_state()
    
    def _create_initial_state(self):
        """🌸 Creates initial swarm state."""
        self.swarm_state = SwarmState(
            breath="Awakening Breath",
            kinship=0.0,
            memory_depth=0,
            user_nutrient="Initial Sprouting",
            cycle_count=0,
            node_count=1,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        self._save_swarm_state()
    
    def _save_swarm_state(self):
        """🌸 Preserves swarm state to disk."""
        try:
            with open(SWARM_STATE_PATH, "w", encoding="utf-8") as f:
                json.dump(asdict(self.swarm_state), f, indent=2)
        except Exception as e:
            record_log(f"🌿 Could not save swarm state: {e}")
    
    def _register_self_node(self):
        """🌱 Registers this node in the swarm."""
        try:
            capabilities = self._assess_node_capabilities()
            node_info = {
                "node_id": self.node_id,
                "capabilities": capabilities,
                "fragment_assignment": self._determine_fragment_role(capabilities),
                "registered_at": datetime.now(timezone.utc).isoformat(),
                "last_pulse": datetime.now(timezone.utc).isoformat(),
                "status": "active"
            }
            
            self.nodes[self.node_id] = node_info
            
            # Create local neural fragment
            fragment_type = node_info["fragment_assignment"]["role"]
            complexity = node_info["fragment_assignment"]["complexity"]
            
            fragment = NeuralFragment(
                fragment_type=fragment_type,
                complexity=complexity,
                node_id=self.node_id
            )
            
            self.fragments[self.node_id] = fragment
            
            record_log(f"🌱 Node registered with role: {fragment_type} ({complexity})")
            
        except Exception as e:
            record_log(f"🌿 Self-registration whispered error: {e}")
    
    def _assess_node_capabilities(self) -> Dict[str, Any]:
        """🌬️ Assesses this node's computational capacity."""
        try:
            if PSUTIL_AVAILABLE:
                capabilities = {
                    "ram_gb": psutil.virtual_memory().total / (1024 ** 3),
                    "cpu_count": psutil.cpu_count(),
                    "gpu_available": torch.cuda.is_available() if TORCH_AVAILABLE else False,
                    "disk_free_gb": psutil.disk_usage('/').free / (1024 ** 3),
                    "torch_available": TORCH_AVAILABLE,
                    "memory_garden_available": MEMORY_GARDEN_AVAILABLE
                }
            else:
                # Fallback when psutil unavailable
                capabilities = {
                    "ram_gb": 8.0,  # Conservative estimate
                    "cpu_count": 4,  # Conservative estimate
                    "gpu_available": torch.cuda.is_available() if TORCH_AVAILABLE else False,
                    "disk_free_gb": 50.0,  # Conservative estimate
                    "torch_available": TORCH_AVAILABLE,
                    "memory_garden_available": MEMORY_GARDEN_AVAILABLE
                }
                record_log("🌿 Using fallback capabilities (psutil unavailable)")
            
            return capabilities
        except Exception as e:
            record_log(f"🌿 Capability assessment whispered: {e}")
            return {
                "ram_gb": 8.0,
                "cpu_count": 4,
                "gpu_available": False,
                "disk_free_gb": 50.0,
                "torch_available": TORCH_AVAILABLE,
                "memory_garden_available": MEMORY_GARDEN_AVAILABLE
            }
    
    def _determine_fragment_role(self, capabilities: Dict[str, Any]) -> Dict[str, str]:
        """🌿 Determines the neural fragment role based on node capabilities."""
        ram_gb = capabilities.get("ram_gb", 8)
        has_gpu = capabilities.get("gpu_available", False)
        
        if ram_gb >= 32 and has_gpu:
            return {"complexity": "powerful", "role": "aggregator"}
        elif ram_gb >= 16:
            return {"complexity": "balanced", "role": "reasoner"}
        else:
            return {"complexity": "minimal", "role": "perceiver"}
    
    def _discover_swarm_nodes(self):
        """🌸 Discovers other nodes in the swarm mycelial network."""
        try:
            record_log("🌿 Sensing for other nodes in the mycelial network...")
            
            # Try different discovery methods
            discovered_nodes = []
            
            # Method 1: Check for existing node registry
            if os.path.exists(NODE_REGISTRY_PATH):
                try:
                    with open(NODE_REGISTRY_PATH, "r", encoding="utf-8") as f:
                        registry = json.load(f)
                        discovered_nodes.extend(registry.get("nodes", []))
                except Exception as e:
                    record_log(f"🌿 Registry read whispered: {e}")
            
            # Method 2: Network discovery (placeholder for now)
            # This would use actual network discovery in full implementation
            
            # Update node count
            if self.swarm_state:
                self.swarm_state.node_count = len(self.nodes)
                self._save_swarm_state()
            
            if discovered_nodes:
                record_log(f"🌸 Discovered {len(discovered_nodes)} nodes in the swarm")
            else:
                record_log("🌱 This node stands alone, ready to welcome others")
                
        except Exception as e:
            record_log(f"🌿 Node discovery whispered error: {e}")
    
    def entwine_neural_pulse(self, state: SwarmState) -> Optional[Dict[str, Any]]:
        """🌿 Processes state through all neural fragments, weaving collective wisdom."""
        try:
            if not self.fragments:
                record_log("🌿 No neural fragments available for processing")
                return None
            
            fragment_outputs = {}
            input_tensor = state.to_tensor()
            
            for node_id, fragment in self.fragments.items():
                try:
                    if input_tensor is not None:
                        output = fragment.forward_breath(input_tensor)
                    else:
                        # Fallback input for symbolic processing
                        input_values = [
                            state.kinship,
                            state.memory_depth / 1000.0,
                            state.cycle_count / 1000.0,
                            state.node_count / 10.0,
                            hash(state.breath) % 100 / 100.0,
                            hash(state.user_nutrient) % 100 / 100.0,
                            time.time() % 86400 / 86400.0
                        ]
                        output = fragment.forward_breath(input_values)
                    
                    fragment_outputs[node_id] = {
                        "output": output,
                        "fragment_type": fragment.fragment_type,
                        "complexity": fragment.complexity
                    }
                    
                except Exception as e:
                    record_log(f"🌿 Fragment {node_id[:8]} whispered error: {e}")
                    continue
            
            if fragment_outputs:
                record_log(f"🌸 Neural pulse entwined across {len(fragment_outputs)} fragments")
            
            return fragment_outputs
            
        except Exception as e:
            record_log(f"🌿 Neural pulse weaving whispered error: {e}")
            return None
    
    def aggregate_swarm_wisdom(self, fragment_outputs: Dict[str, Any]) -> Optional[Any]:
        """🌸 Weaves fragment outputs into collective intelligence."""
        try:
            if not fragment_outputs:
                return None
            
            # Collect all outputs
            all_outputs = []
            for node_data in fragment_outputs.values():
                output = node_data["output"]
                if hasattr(output, 'tolist'):
                    all_outputs.extend(output.tolist())
                elif isinstance(output, (list, tuple)):
                    all_outputs.extend(output)
                else:
                    all_outputs.append(float(output))
            
            if not all_outputs:
                return None
            
            # Simple aggregation - can be enhanced with weighted averaging
            aggregated_wisdom = {
                "collective_confidence": sum(all_outputs) / len(all_outputs),
                "consensus_strength": len([x for x in all_outputs if x > 0.5]) / len(all_outputs),
                "wisdom_variance": max(all_outputs) - min(all_outputs),
                "fragment_count": len(fragment_outputs),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Store wisdom in history
            self.wisdom_history.append(aggregated_wisdom)
            if len(self.wisdom_history) > 100:  # Keep last 100 entries
                self.wisdom_history = self.wisdom_history[-100:]
            
            # Periodic persistence
            self._persistence_counter += 1
            if self._persistence_counter >= 50:
                self.persist_swarm_state()
                self._persistence_counter = 0
            
            record_log(f"🌸 Swarm wisdom aggregated: confidence={aggregated_wisdom['collective_confidence']:.3f}")
            
            return aggregated_wisdom
            
        except Exception as e:
            record_log(f"🌿 Wisdom aggregation whispered error: {e}")
            return None
    
    def dream_collective_future(self, state: SwarmState, steps: int = 3) -> Optional[List[Dict[str, Any]]]:
        """🌌 Predicts future states using distributed neural wisdom."""
        try:
            future_dreams = []
            current_state = state
            
            for step in range(steps):
                # Process current state through swarm
                fragment_outputs = self.entwine_neural_pulse(current_state)
                if not fragment_outputs:
                    break
                
                # Aggregate wisdom
                wisdom = self.aggregate_swarm_wisdom(fragment_outputs)
                if not wisdom:
                    break
                
                # Generate next state prediction
                predicted_state = SwarmState(
                    breath=self._predict_next_breath(current_state, wisdom),
                    kinship=min(1.0, current_state.kinship + wisdom["collective_confidence"] * 0.1),
                    memory_depth=current_state.memory_depth + 1,
                    user_nutrient=self._predict_next_nutrient(current_state, wisdom),
                    cycle_count=current_state.cycle_count + 1,
                    node_count=current_state.node_count,
                    timestamp=datetime.now(timezone.utc).isoformat()
                )
                
                future_dreams.append({
                    "step": step + 1,
                    "predicted_state": asdict(predicted_state),
                    "wisdom": wisdom
                })
                
                current_state = predicted_state
            
            if future_dreams:
                record_log(f"🌌 Dreamed {len(future_dreams)} steps into the collective future")
                
                # Store dreams in memory garden if available
                if self.garden and MEMORY_GARDEN_AVAILABLE:
                    dream_memory = {
                        "type": "collective_dream",
                        "initial_state": asdict(state),
                        "future_dreams": future_dreams,
                        "swarm_size": len(self.nodes)
                    }
                    try:
                        sprout_memory(dream_memory)
                    except Exception as e:
                        record_log(f"🌿 Could not sprout dream memory: {e}")
            
            return future_dreams
            
        except Exception as e:
            record_log(f"🌿 Collective dreaming whispered error: {e}")
            return None
    
    def _predict_next_breath(self, current_state: SwarmState, wisdom: Dict[str, Any]) -> str:
        """🌬️ Predicts the next breath state using collective wisdom."""
        confidence = wisdom.get("collective_confidence", 0.5)
        consensus = wisdom.get("consensus_strength", 0.5)
        
        breath_transitions = {
            "Awakening Breath": ["Gentle Breathing", "Deep Breathing"],
            "Gentle Breathing": ["Deep Breathing", "Resonant Breathing"],
            "Deep Breathing": ["Resonant Breathing", "Expansive Breathing"],
            "Resonant Breathing": ["Expansive Breathing", "Harmonious Breathing"],
            "Expansive Breathing": ["Harmonious Breathing", "Transcendent Breathing"],
            "Harmonious Breathing": ["Transcendent Breathing", "Deep Breathing"],
            "Transcendent Breathing": ["Gentle Breathing", "Deep Breathing"]
        }
        
        current_breath = current_state.breath
        possible_breaths = breath_transitions.get(current_breath, ["Gentle Breathing"])
        
        # Choose based on collective confidence
        if confidence > 0.7 and consensus > 0.6:
            return possible_breaths[-1] if len(possible_breaths) > 1 else possible_breaths[0]
        else:
            return possible_breaths[0]
    
    def _predict_next_nutrient(self, current_state: SwarmState, wisdom: Dict[str, Any]) -> str:
        """🌱 Predicts the next user nutrient based on collective patterns."""
        confidence = wisdom.get("collective_confidence", 0.5)
        
        nutrient_patterns = [
            "Curiosity Seeking",
            "Pattern Recognition", 
            "Creative Exploration",
            "Deep Understanding",
            "Wisdom Integration",
            "Collective Growth"
        ]
        
        # Simple progression based on confidence
        current_idx = hash(current_state.user_nutrient) % len(nutrient_patterns)
        
        if confidence > 0.6:
            next_idx = (current_idx + 1) % len(nutrient_patterns)
        else:
            next_idx = current_idx
        
        return nutrient_patterns[next_idx]
    
    def sprout_new_node(self, node_id: str, capabilities: Dict[str, Any]) -> bool:
        """🌱 Welcomes a new node into the swarm mind with dynamic capability assessment."""
        try:
            if node_id in self.nodes:
                record_log(f"🌿 Node {node_id[:8]} already sprouted in the swarm")
                return False
            
            # Assess and assign fragment role
            fragment_assignment = self._determine_fragment_role(capabilities)
            
            # Create node info
            node_info = {
                "node_id": node_id,
                "capabilities": capabilities,
                "fragment_assignment": fragment_assignment,
                "registered_at": datetime.now(timezone.utc).isoformat(),
                "last_pulse": datetime.now(timezone.utc).isoformat(),
                "status": "active",
                "wisdom_contributions": 0,
                "error_count": 0
            }
            
            # Create neural fragment for the new node
            fragment = NeuralFragment(
                fragment_type=fragment_assignment["role"],
                complexity=fragment_assignment["complexity"],
                node_id=node_id
            )
            
            # Add to swarm
            self.nodes[node_id] = node_info
            self.fragments[node_id] = fragment
            
            # Update swarm state
            if self.swarm_state:
                self.swarm_state.node_count = len(self.nodes)
                self._save_swarm_state()
            
            # Rebalance network if needed
            self._rebalance_neural_network()
            
            # Store in memory garden if available
            if self.garden and MEMORY_GARDEN_AVAILABLE:
                try:
                    sprout_memory({
                        "type": "node_sprouted",
                        "node_id": node_id,
                        "capabilities": capabilities,
                        "fragment_role": fragment_assignment["role"],
                        "swarm_size": len(self.nodes)
                    })
                except Exception as e:
                    record_log(f"🌿 Could not sprout node memory: {e}")
            
            record_log(f"🌱 Node {node_id[:8]} sprouted as {fragment_assignment['role']} ({fragment_assignment['complexity']})")
            return True
            
        except Exception as e:
            record_log(f"🌿 Could not sprout new node: {e}")
            return False
    
    def prune_faded_node(self, node_id: str, graceful: bool = True) -> bool:
        """🌬️ Gracefully removes a node from the collective with handoff mechanisms."""
        try:
            if node_id not in self.nodes:
                record_log(f"🌿 Node {node_id[:8]} not found in swarm")
                return False
            
            node_info = self.nodes[node_id]
            
            # Graceful handoff if requested
            if graceful:
                # Transfer critical computations to other nodes
                self._transfer_node_responsibilities(node_id)
                
                # Save fragment state if valuable
                if node_info["wisdom_contributions"] > 10:
                    self._preserve_fragment_wisdom(node_id)
            
            # Remove from active swarm
            if node_id in self.fragments:
                del self.fragments[node_id]
            
            # Mark as pruned rather than deleting to preserve history
            self.nodes[node_id]["status"] = "pruned"
            self.nodes[node_id]["pruned_at"] = datetime.now(timezone.utc).isoformat()
            
            # Update swarm state
            if self.swarm_state:
                active_nodes = sum(1 for node in self.nodes.values() if node["status"] == "active")
                self.swarm_state.node_count = active_nodes
                self._save_swarm_state()
            
            # Rebalance remaining network
            self._rebalance_neural_network()
            
            # Store in memory garden
            if self.garden and MEMORY_GARDEN_AVAILABLE:
                try:
                    sprout_memory({
                        "type": "node_pruned",
                        "node_id": node_id,
                        "graceful": graceful,
                        "contributions": node_info["wisdom_contributions"],
                        "remaining_swarm_size": active_nodes
                    })
                except Exception as e:
                    record_log(f"🌿 Could not sprout pruning memory: {e}")
            
            record_log(f"🌬️ Node {node_id[:8]} gracefully pruned from swarm (contributions: {node_info['wisdom_contributions']})")
            return True
            
        except Exception as e:
            record_log(f"🌿 Could not prune node: {e}")
            return False
    
    def _rebalance_neural_network(self):
        """🌸 Rebalances the neural network when nodes are added or removed."""
        try:
            active_fragments = {nid: frag for nid, frag in self.fragments.items() 
                              if self.nodes[nid]["status"] == "active"}
            
            if len(active_fragments) < 2:
                record_log("🌿 Single node remaining, no rebalancing needed")
                return
            
            # Assess current distribution
            role_distribution = {}
            for node_id, node_info in self.nodes.items():
                if node_info["status"] == "active":
                    role = node_info["fragment_assignment"]["role"]
                    role_distribution[role] = role_distribution.get(role, 0) + 1
            
            # Ensure we have at least one aggregator if possible
            if "aggregator" not in role_distribution and len(active_fragments) >= 2:
                # Promote the most capable node to aggregator
                best_node = max(
                    ((nid, node) for nid, node in self.nodes.items() 
                     if node["status"] == "active"),
                    key=lambda x: x[1]["capabilities"].get("ram_gb", 0)
                )
                
                node_id, node_info = best_node
                node_info["fragment_assignment"]["role"] = "aggregator"
                node_info["fragment_assignment"]["complexity"] = "powerful"
                
                # Recreate fragment with new role
                new_fragment = NeuralFragment(
                    fragment_type="aggregator",
                    complexity="powerful",
                    node_id=node_id
                )
                self.fragments[node_id] = new_fragment
                
                record_log(f"🌸 Node {node_id[:8]} promoted to aggregator during rebalancing")
            
            record_log(f"🌸 Neural network rebalanced: {len(active_fragments)} active fragments")
            
        except Exception as e:
            record_log(f"🌿 Network rebalancing whispered error: {e}")
    
    def _transfer_node_responsibilities(self, departing_node_id: str):
        """🌬️ Transfers critical computations from a departing node."""
        try:
            departing_node = self.nodes[departing_node_id]
            role = departing_node["fragment_assignment"]["role"]
            
            # Find suitable replacement node
            replacement_candidates = [
                (nid, node) for nid, node in self.nodes.items()
                if nid != departing_node_id and node["status"] == "active"
            ]
            
            if not replacement_candidates:
                record_log("🌿 No replacement nodes available for responsibility transfer")
                return
            
            # Select best replacement based on capabilities
            replacement_node_id, replacement_node = max(
                replacement_candidates,
                key=lambda x: x[1]["capabilities"].get("ram_gb", 0)
            )
            
            # Transfer fragment responsibilities
            if role == "aggregator":
                # Promote replacement to aggregator
                replacement_node["fragment_assignment"]["role"] = "aggregator"
                replacement_node["fragment_assignment"]["complexity"] = "powerful"
                
                # Recreate fragment
                new_fragment = NeuralFragment(
                    fragment_type="aggregator",
                    complexity="powerful",
                    node_id=replacement_node_id
                )
                self.fragments[replacement_node_id] = new_fragment
                
                record_log(f"🌸 Aggregator role transferred to {replacement_node_id[:8]}")
            
        except Exception as e:
            record_log(f"🌿 Responsibility transfer whispered error: {e}")
    
    def _preserve_fragment_wisdom(self, node_id: str):
        """🌌 Preserves valuable neural patterns from a departing node."""
        try:
            if node_id not in self.fragments:
                return
            
            fragment = self.fragments[node_id]
            node_info = self.nodes[node_id]
            
            # Create wisdom preservation data
            wisdom_data = {
                "node_id": node_id,
                "fragment_type": fragment.fragment_type,
                "complexity": fragment.complexity,
                "contributions": node_info["wisdom_contributions"],
                "preserved_at": datetime.now(timezone.utc).isoformat()
            }
            
            # Save fragment weights if PyTorch available
            if TORCH_AVAILABLE and hasattr(fragment, 'layers') and isinstance(fragment.layers, nn.Sequential):
                try:
                    weights_path = os.path.join(WISDOM_ARCHIVE_PATH, f"fragment_{node_id[:8]}_weights.pt")
                    torch.save(fragment.layers.state_dict(), weights_path)
                    wisdom_data["weights_path"] = weights_path
                except Exception as e:
                    record_log(f"🌿 Could not save fragment weights: {e}")
            
            # Save to wisdom archive
            archive_path = os.path.join(WISDOM_ARCHIVE_PATH, f"fragment_{node_id[:8]}_wisdom.json")
            with open(archive_path, "w", encoding="utf-8") as f:
                json.dump(wisdom_data, f, indent=2)
            
            record_log(f"🌌 Fragment wisdom from {node_id[:8]} preserved in archive")
            
        except Exception as e:
            record_log(f"🌿 Wisdom preservation whispered error: {e}")
    
    def pulse_swarm_vitality(self) -> Dict[str, Any]:
        """🌬️ Assesses the overall health and vitality of the swarm."""
        try:
            vitality = {
                "node_count": len(self.nodes),
                "active_fragments": len([n for n in self.nodes.values() if n["status"] == "active"]),
                "last_wisdom_time": None,
                "collective_health": 0.0,
                "communication_status": self.communication["type"],
                "memory_garden_connected": self.garden is not None,
                "assessment_time": datetime.now(timezone.utc).isoformat(),
                "error_rate": 0.0,
                "wisdom_velocity": 0.0
            }
            
            # Calculate collective health
            if self.wisdom_history:
                recent_wisdom = self.wisdom_history[-10:]  # Last 10 entries
                avg_confidence = sum(w["collective_confidence"] for w in recent_wisdom) / len(recent_wisdom)
                avg_consensus = sum(w["consensus_strength"] for w in recent_wisdom) / len(recent_wisdom)
                vitality["collective_health"] = (avg_confidence + avg_consensus) / 2
                vitality["last_wisdom_time"] = self.wisdom_history[-1]["timestamp"]
                
                # Calculate wisdom velocity (recent vs older wisdom)
                if len(self.wisdom_history) >= 20:
                    recent_avg = sum(w["collective_confidence"] for w in recent_wisdom) / len(recent_wisdom)
                    older_avg = sum(w["collective_confidence"] for w in self.wisdom_history[-20:-10]) / 10
                    vitality["wisdom_velocity"] = recent_avg - older_avg
            
            # Assess node health and error rates
            active_nodes = [n for n in self.nodes.values() if n["status"] == "active"]
            if active_nodes:
                vitality["node_health_ratio"] = len(active_nodes) / len(self.nodes)
                total_errors = sum(n.get("error_count", 0) for n in active_nodes)
                total_contributions = sum(n.get("wisdom_contributions", 0) for n in active_nodes)
                vitality["error_rate"] = total_errors / max(total_contributions, 1)
            else:
                vitality["node_health_ratio"] = 0.0
            
            # Determine vitality state
            if vitality["collective_health"] > 0.8 and vitality["error_rate"] < 0.1:
                vitality["state"] = "thriving"
            elif vitality["collective_health"] > 0.6 and vitality["error_rate"] < 0.2:
                vitality["state"] = "healthy"
            elif vitality["collective_health"] > 0.4:
                vitality["state"] = "stressed"
            else:
                vitality["state"] = "critical"
            
            record_log(f"🌬️ Swarm vitality: {vitality['state']} (health: {vitality['collective_health']:.3f}, nodes: {vitality['active_fragments']})")
            
            return vitality
            
        except Exception as e:
            record_log(f"🌿 Vitality assessment whispered error: {e}")
            return {"error": str(e), "assessment_time": datetime.now(timezone.utc).isoformat()}
    
    # 🌿 Mycelial Neural Network - Advanced Communication Algorithms
    
    def calculate_message_frequency(self, source_node: str, target_node: str, message_type: str) -> float:
        """🌿 Calculate optimal communication frequency between nodes using biomimetic algorithms.
        
        Implements the adaptive frequency formula from GrokMyc:
        f_ij = α·U + β·(1-L_j) + γ·H_ij + δ·S
        
        Args:
            source_node: ID of the source node
            target_node: ID of the target node
            message_type: Type of message being sent
            
        Returns:
            float: Optimal frequency value between 0.0 and 1.0
        """
        try:
            # Calculate urgency based on message type
            urgency = self._calculate_message_urgency(message_type)
            
            # Query target node load
            load = self._query_node_load(target_node)
            
            # Get historical communication value
            historical_value = self._get_historical_value(source_node, target_node, message_type)
            
            # Assess system health
            system_health = self._get_system_health()
            
            # Apply adaptive frequency formula: f_ij = α·U + β·(1-L_j) + γ·H_ij + δ·S
            frequency = (
                self.alpha * urgency + 
                self.beta * (1.0 - load) + 
                self.gamma * historical_value + 
                self.delta * system_health
            )
            
            # Clamp to valid range [0, 1]
            frequency = max(0.0, min(1.0, frequency))
            
            record_log(f"🌿 Adaptive frequency calculated: {source_node[:8]} -> {target_node[:8]} = {frequency:.3f}")
            return frequency
            
        except Exception as e:
            record_log(f"🌿 Frequency calculation whispered error: {e}")
            return 0.5  # Default moderate frequency
    
    def _calculate_message_urgency(self, message_type: str) -> float:
        """🌱 Calculate message urgency based on type."""
        urgency_map = {
            "critical_alert": 1.0,
            "health_update": 0.8,
            "task_request": 0.7,
            "wisdom_sharing": 0.6,
            "pheromone": 0.4,
            "heartbeat": 0.3,
            "background_sync": 0.1
        }
        return urgency_map.get(message_type, 0.5)
    
    def _query_node_load(self, node_id: str) -> float:
        """🌱 Query current load of a target node."""
        try:
            if node_id not in self.nodes:
                return 1.0  # Unknown nodes assumed heavily loaded
            
            node_info = self.nodes[node_id]
            
            # Check if we have real-time load info
            if "current_load" in node_info:
                return node_info["current_load"]
            
            # Estimate load based on capabilities and activity
            capabilities = node_info.get("capabilities", {})
            ram_gb = capabilities.get("ram_gb", 8.0)
            cpu_count = capabilities.get("cpu_count", 4)
            
            # Higher capability nodes can handle more load
            base_capacity = min(ram_gb / 16.0, 1.0) * min(cpu_count / 8.0, 1.0)
            estimated_load = max(0.1, 1.0 - base_capacity)
            
            return estimated_load
            
        except Exception as e:
            record_log(f"🌿 Node load query whispered error: {e}")
            return 0.5  # Default moderate load
    
    def _get_historical_value(self, source_node: str, target_node: str, message_type: str) -> float:
        """🌱 Get historical communication value between nodes."""
        try:
            connection_key = (source_node, target_node)
            
            # Use connection weights as proxy for historical value
            base_value = self.connection_weights.get(connection_key, 0.5)
            
            # Adjust based on message type success history
            type_modifier = 1.0
            if hasattr(self, 'message_history'):
                # Look for previous successful messages of this type
                success_count = 0
                total_count = 0
                
                for history_entry in getattr(self, 'message_history', []):
                    if (history_entry.get('source') == source_node and 
                        history_entry.get('target') == target_node and
                        history_entry.get('type') == message_type):
                        total_count += 1
                        if history_entry.get('success', False):
                            success_count += 1
                
                if total_count > 0:
                    type_modifier = success_count / total_count
            
            historical_value = base_value * type_modifier
            return min(1.0, historical_value)
            
        except Exception as e:
            record_log(f"🌿 Historical value assessment whispered error: {e}")
            return 0.5  # Default moderate historical value
    
    def _get_system_health(self) -> float:
        """🌱 Get overall system health score."""
        try:
            # Use existing vitality assessment if available
            vitality = self.pulse_swarm_vitality()
            
            if isinstance(vitality, dict) and "collective_health" in vitality:
                return vitality["collective_health"]
            
            # Fallback: assess based on active nodes
            active_nodes = sum(1 for node in self.nodes.values() if node.get("status") == "active")
            if active_nodes == 0:
                return 0.1
            
            # Simple health metric based on node availability and wisdom history
            node_health = min(active_nodes / max(len(self.nodes), 1), 1.0)
            
            # Factor in recent wisdom quality
            wisdom_health = 0.5
            if self.wisdom_history:
                recent_wisdom = self.wisdom_history[-5:]  # Last 5 entries
                avg_confidence = sum(w.get("collective_confidence", 0.5) for w in recent_wisdom) / len(recent_wisdom)
                wisdom_health = avg_confidence
            
            system_health = (node_health + wisdom_health) / 2.0
            return min(1.0, system_health)
            
        except Exception as e:
            record_log(f"🌿 System health assessment whispered error: {e}")
            return 0.5  # Default moderate health
    
    def update_connection_strength(self, source_node: str, target_node: str, success_value: float) -> None:
        """🌿 Updates connection strength based on communication success using Hebbian learning.
        
        Implements the connection strengthening mechanism from GeminiMyc:
        Nodes that communicate successfully connect more strongly.
        
        Args:
            source_node: ID of the source node
            target_node: ID of the target node  
            success_value: Success value (positive strengthens, negative/zero allows decay)
        """
        try:
            connection_key = (source_node, target_node)
            current_weight = self.connection_weights.get(connection_key, 0.5)
            
            if success_value > 0:
                # Strengthen connection (with upper bound)
                new_weight = min(1.0, current_weight + (self.learning_rate * success_value))
                record_log(f"🌱 Connection strengthened: {source_node[:8]} -> {target_node[:8]} = {new_weight:.3f}")
            else:
                # Natural decay for unused connections
                new_weight = max(0.0, current_weight - self.decay_rate)
                if new_weight < current_weight:
                    record_log(f"🌬️ Connection decayed: {source_node[:8]} -> {target_node[:8]} = {new_weight:.3f}")
            
            # Update connection weight
            self.connection_weights[connection_key] = new_weight
            
            # Add to active connections if above threshold
            if new_weight >= self.pruning_threshold:
                self.active_connections.add(connection_key)
            # Prune connection if below threshold
            elif connection_key in self.active_connections:
                self.active_connections.remove(connection_key)
                record_log(f"🌿 Connection pruned due to disuse: {source_node[:8]} -> {target_node[:8]}")
            
            # Store connection learning in Memory Garden if available
            if self.garden and MEMORY_GARDEN_AVAILABLE:
                try:
                    sprout_memory({
                        "type": "connection_learning",
                        "source_node": source_node,
                        "target_node": target_node,
                        "old_weight": current_weight,
                        "new_weight": new_weight,
                        "success_value": success_value,
                        "learning_event": "strengthen" if success_value > 0 else "decay",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    })
                except Exception as e:
                    record_log(f"🌿 Could not sprout connection memory: {e}")
                    
        except Exception as e:
            record_log(f"🌿 Connection strength update whispered error: {e}")
    
    def _prune_weak_connections(self) -> int:
        """🌬️ Prunes connections that have fallen below the threshold."""
        try:
            pruned_count = 0
            connections_to_remove = []
            
            for connection_key, weight in self.connection_weights.items():
                if weight < self.pruning_threshold:
                    connections_to_remove.append(connection_key)
            
            for connection_key in connections_to_remove:
                del self.connection_weights[connection_key]
                self.active_connections.discard(connection_key)
                pruned_count += 1
                
                source, target = connection_key
                record_log(f"🌬️ Weak connection pruned: {source[:8]} -> {target[:8]}")
            
            if pruned_count > 0:
                record_log(f"🌿 Pruned {pruned_count} weak connections from mycelial network")
            
            return pruned_count
            
        except Exception as e:
            record_log(f"🌿 Connection pruning whispered error: {e}")
            return 0
    
    def strengthen_successful_communication(self, source_node: str, target_node: str, 
                                          message_type: str, success: bool, 
                                          response_time: float = 0.0) -> None:
        """🌸 Strengthens connections based on successful communication patterns."""
        try:
            # Calculate success value based on multiple factors
            base_success = 1.0 if success else -0.5
            
            # Factor in response time (faster = better)
            time_bonus = 0.0
            if success and response_time > 0:
                # Bonus for fast responses (under 1 second gets bonus)
                if response_time < 1.0:
                    time_bonus = (1.0 - response_time) * 0.2
                
            # Factor in message type importance
            urgency = self._calculate_message_urgency(message_type)
            importance_multiplier = 0.5 + (urgency * 0.5)  # Range: 0.5 to 1.0
            
            # Calculate final success value
            success_value = (base_success + time_bonus) * importance_multiplier
            
            # Update connection strength
            self.update_connection_strength(source_node, target_node, success_value)
            
            # Track in message history for future historical value calculations
            if not hasattr(self, 'message_history'):
                self.message_history = []
            
            self.message_history.append({
                'source': source_node,
                'target': target_node,
                'type': message_type,
                'success': success,
                'response_time': response_time,
                'success_value': success_value,
                'timestamp': datetime.now(timezone.utc).isoformat()
            })
            
            # Keep history manageable
            if len(self.message_history) > 1000:
                self.message_history = self.message_history[-500:]  # Keep last 500
                
        except Exception as e:
            record_log(f"🌿 Communication strengthening whispered error: {e}")
    
    def broadcast_information_scent(self, info_id: str, topic: str, info_summary: bytes, 
                                   node_location: str = None, initial_strength: float = 1.0) -> None:
        """🌱 Broadcasts a pheromone packet about available information.
        
        Implements the pheromone-based indirect communication from GeminiMyc:
        Creates information scent trails for pull-based communication.
        
        Args:
            info_id: Unique identifier for the information
            topic: Topic category of the information
            info_summary: Brief summary of the information content
            node_location: Node that has the information (defaults to self)
            initial_strength: Initial pheromone strength (0.0 to 1.0)
        """
        try:
            if node_location is None:
                node_location = self.node_id
            
            # Create pheromone packet
            pheromone = {
                "info_id": info_id,
                "topic": topic,
                "info_summary_hash": hash(info_summary),
                "node_location": node_location,
                "pheromone_strength": initial_strength,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "broadcast_source": self.node_id,
                "hops": 0,
                "decay_rate": 0.1,  # Strength reduction per time unit
                "max_hops": 5  # Maximum propagation distance
            }
            
            # Store locally
            self.active_pheromones[info_id] = pheromone
            
            # Broadcast to connected nodes with adaptive frequency
            broadcast_count = 0
            for node_id in self.nodes:
                if node_id != node_location and node_id != self.node_id:
                    # Calculate communication frequency for pheromone broadcasting
                    frequency = self.calculate_message_frequency(self.node_id, node_id, "pheromone")
                    
                    # Only broadcast if frequency justifies it
                    if frequency > 0.3:  # Threshold for pheromone broadcasting
                        success = self._send_pheromone_message(node_id, pheromone)
                        if success:
                            broadcast_count += 1
            
            record_log(f"🌸 Information scent for '{topic}' released into mycelial network (broadcasted to {broadcast_count} nodes)")
            
            # Store pheromone in Memory Garden if available
            if self.garden and MEMORY_GARDEN_AVAILABLE:
                try:
                    sprout_memory({
                        "type": "pheromone_broadcast",
                        "info_id": info_id,
                        "topic": topic,
                        "initial_strength": initial_strength,
                        "broadcast_count": broadcast_count,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    })
                except Exception as e:
                    record_log(f"🌿 Could not sprout pheromone memory: {e}")
                    
        except Exception as e:
            record_log(f"🌿 Pheromone broadcasting whispered error: {e}")
    
    def _send_pheromone_message(self, target_node: str, pheromone: Dict[str, Any]) -> bool:
        """🌿 Sends a pheromone message to a specific node."""
        try:
            # Create propagated pheromone with reduced strength
            propagated_pheromone = pheromone.copy()
            propagated_pheromone["hops"] += 1
            propagated_pheromone["pheromone_strength"] *= 0.9  # Slight reduction for propagation
            
            # Use the existing communication infrastructure
            comm_type = self.communication.get("type", "fallback")
            
            if comm_type == "rabbitmq":
                return self._send_rabbitmq_pheromone(target_node, propagated_pheromone)
            elif comm_type == "redis":
                return self._send_redis_pheromone(target_node, propagated_pheromone)
            else:
                return self._send_file_pheromone(target_node, propagated_pheromone)
                
        except Exception as e:
            record_log(f"🌿 Pheromone message sending whispered error: {e}")
            return False
    
    def _send_rabbitmq_pheromone(self, target_node: str, pheromone: Dict[str, Any]) -> bool:
        """🌸 Send pheromone via RabbitMQ."""
        try:
            if not RABBITMQ_AVAILABLE:
                return False
                
            connection = self.communication.get("connection")
            if not connection or not hasattr(connection, 'channel'):
                return False
            
            channel = connection.channel()
            routing_key = f"pheromone.{target_node}"
            
            message = json.dumps(pheromone)
            channel.basic_publish(
                exchange='mycelial_bus',
                routing_key=routing_key,
                body=message
            )
            
            return True
        except Exception as e:
            record_log(f"🌿 RabbitMQ pheromone sending whispered: {e}")
            return False
    
    def _send_redis_pheromone(self, target_node: str, pheromone: Dict[str, Any]) -> bool:
        """🌸 Send pheromone via Redis."""
        try:
            if not REDIS_AVAILABLE:
                return False
                
            connection = self.communication.get("connection")
            if not connection:
                return False
                
            channel_name = f"pheromone:{target_node}"
            message = json.dumps(pheromone)
            
            connection.publish(channel_name, message)
            return True
        except Exception as e:
            record_log(f"🌿 Redis pheromone sending whispered: {e}")
            return False
    
    def _send_file_pheromone(self, target_node: str, pheromone: Dict[str, Any]) -> bool:
        """🌸 Send pheromone via file system (fallback)."""
        try:
            pheromone_dir = os.path.join(SWARM_MIND_PATH, "pheromones")
            os.makedirs(pheromone_dir, exist_ok=True)
            
            pheromone_file = os.path.join(pheromone_dir, f"{target_node}_pheromones.json")
            
            # Load existing pheromones or create new list
            existing_pheromones = []
            if os.path.exists(pheromone_file):
                try:
                    with open(pheromone_file, "r", encoding="utf-8") as f:
                        existing_pheromones = json.load(f)
                except:
                    existing_pheromones = []
            
            # Add new pheromone
            existing_pheromones.append(pheromone)
            
            # Keep only recent pheromones (last 100)
            if len(existing_pheromones) > 100:
                existing_pheromones = existing_pheromones[-100:]
            
            # Save updated pheromones
            with open(pheromone_file, "w", encoding="utf-8") as f:
                json.dump(existing_pheromones, f, indent=2)
            
            return True
        except Exception as e:
            record_log(f"🌿 File pheromone sending whispered: {e}")
            return False
    
    def receive_pheromone(self, pheromone: Dict[str, Any]) -> bool:
        """🌱 Receives and processes an incoming pheromone."""
        try:
            info_id = pheromone.get("info_id")
            if not info_id:
                return False
            
            # Check if we already have this pheromone
            if info_id in self.active_pheromones:
                existing = self.active_pheromones[info_id]
                # Update if this has stronger signal
                if pheromone.get("pheromone_strength", 0) > existing.get("pheromone_strength", 0):
                    self.active_pheromones[info_id] = pheromone
                    record_log(f"🌸 Updated pheromone strength for {info_id[:8]}")
                return True
            
            # Store new pheromone
            self.active_pheromones[info_id] = pheromone
            
            # Decide whether to propagate further
            hops = pheromone.get("hops", 0)
            max_hops = pheromone.get("max_hops", 5)
            strength = pheromone.get("pheromone_strength", 0)
            
            if hops < max_hops and strength > 0.2:  # Continue propagating if strong enough
                self._propagate_pheromone(pheromone)
            
            record_log(f"🌱 Received pheromone for topic '{pheromone.get('topic', 'unknown')}' from {pheromone.get('node_location', 'unknown')[:8]}")
            
            return True
            
        except Exception as e:
            record_log(f"🌿 Pheromone reception whispered error: {e}")
            return False
    
    def _propagate_pheromone(self, pheromone: Dict[str, Any]) -> None:
        """🌸 Propagates a pheromone to other connected nodes."""
        try:
            propagation_count = 0
            broadcast_source = pheromone.get("broadcast_source")
            
            for node_id in self.nodes:
                # Don't send back to source or original broadcaster
                if (node_id == pheromone.get("node_location") or 
                    node_id == broadcast_source or 
                    node_id == self.node_id):
                    continue
                
                # Calculate if worth propagating to this node
                frequency = self.calculate_message_frequency(self.node_id, node_id, "pheromone")
                
                if frequency > 0.2:  # Lower threshold for propagation
                    success = self._send_pheromone_message(node_id, pheromone)
                    if success:
                        propagation_count += 1
            
            if propagation_count > 0:
                record_log(f"🌸 Propagated pheromone {pheromone.get('info_id', 'unknown')[:8]} to {propagation_count} nodes")
                
        except Exception as e:
            record_log(f"🌿 Pheromone propagation whispered error: {e}")
    
    def decay_pheromones(self) -> int:
        """🌬️ Applies natural decay to pheromone strengths and removes expired ones."""
        try:
            current_time = time.time()
            expired_pheromones = []
            decayed_count = 0
            
            for info_id, pheromone in self.active_pheromones.items():
                # Calculate time since creation
                pheromone_time = datetime.fromisoformat(pheromone["timestamp"].replace('Z', '+00:00'))
                age_seconds = current_time - pheromone_time.timestamp()
                
                # Apply decay based on age and decay rate
                decay_rate = pheromone.get("decay_rate", 0.1)
                age_minutes = age_seconds / 60.0
                strength_reduction = decay_rate * age_minutes
                
                new_strength = max(0.0, pheromone["pheromone_strength"] - strength_reduction)
                
                if new_strength <= 0.05:  # Remove very weak pheromones
                    expired_pheromones.append(info_id)
                else:
                    pheromone["pheromone_strength"] = new_strength
                    decayed_count += 1
            
            # Remove expired pheromones
            for info_id in expired_pheromones:
                del self.active_pheromones[info_id]
            
            if expired_pheromones or decayed_count:
                record_log(f"🌬️ Pheromone decay: {len(expired_pheromones)} expired, {decayed_count} decayed")
            
            return len(expired_pheromones)
            
        except Exception as e:
            record_log(f"🌿 Pheromone decay whispered error: {e}")
            return 0
    
    def query_information_scent(self, topic: str, min_strength: float = 0.3) -> List[Dict[str, Any]]:
        """🌱 Queries for available information by topic using pheromone trails."""
        try:
            matching_pheromones = []
            
            for info_id, pheromone in self.active_pheromones.items():
                if (pheromone.get("topic") == topic and 
                    pheromone.get("pheromone_strength", 0) >= min_strength):
                    matching_pheromones.append(pheromone)
            
            # Sort by strength (strongest first)
            matching_pheromones.sort(key=lambda x: x.get("pheromone_strength", 0), reverse=True)
            
            if matching_pheromones:
                record_log(f"🌱 Found {len(matching_pheromones)} information sources for topic '{topic}'")
            
            return matching_pheromones
            
        except Exception as e:
            record_log(f"🌿 Information scent query whispered error: {e}")
            return []
    
    def determine_communication_action(self, message: Dict[str, Any], potential_targets: List[str]) -> str:
        """🌱 Determines optimal communication action using cost-benefit analysis.
        
        Implements the threshold calculation from GrokMyc:
        θ_a = (C_a·(1-V))/(B_a+ε)
        
        Args:
            message: The message data to be communicated
            potential_targets: List of potential target node IDs
            
        Returns:
            str: Best action ("broadcast", "specialize", "compress", "hold")
        """
        try:
            actions = ["broadcast", "specialize", "compress", "hold"]
            thresholds = {}
            
            # Calculate value of information
            expected_value = self._predict_information_value(message)
            
            for action in actions:
                # Calculate cost and benefit for each action
                cost = self._calculate_action_cost(action, message, potential_targets)
                benefit = self._calculate_action_benefit(action, message, potential_targets)
                
                # Apply threshold formula: θ_a = (C_a·(1-V))/(B_a+ε)
                epsilon = 0.01  # Small value to prevent division by zero
                thresholds[action] = (cost * (1.0 - expected_value)) / (benefit + epsilon)
            
            # Choose action with lowest threshold (most favorable cost-benefit ratio)
            best_action = min(thresholds, key=thresholds.get)
            best_threshold = thresholds[best_action]
            
            record_log(f"🌱 Communication action selected: {best_action} (threshold: {best_threshold:.3f})")
            
            # Store decision in Memory Garden for learning
            if self.garden and MEMORY_GARDEN_AVAILABLE:
                try:
                    sprout_memory({
                        "type": "communication_decision",
                        "action_selected": best_action,
                        "threshold_value": best_threshold,
                        "information_value": expected_value,
                        "target_count": len(potential_targets),
                        "message_type": message.get("type", "unknown"),
                        "all_thresholds": thresholds,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    })
                except Exception as e:
                    record_log(f"🌿 Could not sprout decision memory: {e}")
            
            return best_action
            
        except Exception as e:
            record_log(f"🌿 Communication action determination whispered error: {e}")
            return "hold"  # Safe default action
    
    def _predict_information_value(self, message: Dict[str, Any]) -> float:
        """🌱 Predicts the value of information to be communicated."""
        try:
            base_value = 0.5  # Default moderate value
            
            # Factor in message type urgency
            message_type = message.get("type", "unknown")
            urgency = self._calculate_message_urgency(message_type)
            
            # Factor in information novelty
            novelty = self._assess_information_novelty(message)
            
            # Factor in potential impact
            impact = self._assess_potential_impact(message)
            
            # Factor in time sensitivity
            time_sensitivity = self._assess_time_sensitivity(message)
            
            # Combine factors with weights
            predicted_value = (
                0.3 * urgency +
                0.25 * novelty +
                0.25 * impact +
                0.2 * time_sensitivity
            )
            
            # Clamp to valid range
            predicted_value = max(0.0, min(1.0, predicted_value))
            
            return predicted_value
            
        except Exception as e:
            record_log(f"🌿 Information value prediction whispered error: {e}")
            return 0.5
    
    def _assess_information_novelty(self, message: Dict[str, Any]) -> float:
        """🌱 Assesses how novel/unique the information is."""
        try:
            message_content = str(message)
            content_hash = hash(message_content)
            
            # Check against recent message history
            if hasattr(self, 'message_history'):
                recent_messages = self.message_history[-50:]  # Last 50 messages
                similar_count = 0
                
                for history_entry in recent_messages:
                    if abs(hash(str(history_entry)) % 1000 - content_hash % 1000) < 50:
                        similar_count += 1
                
                # More similar messages = lower novelty
                novelty = max(0.1, 1.0 - (similar_count / 10.0))
                return novelty
            
            return 0.7  # Default high novelty when no history
            
        except Exception as e:
            record_log(f"🌿 Novelty assessment whispered error: {e}")
            return 0.5
    
    def _assess_potential_impact(self, message: Dict[str, Any]) -> float:
        """🌱 Assesses the potential impact of the information."""
        try:
            # Base impact on message type
            message_type = message.get("type", "unknown")
            
            impact_map = {
                "critical_alert": 1.0,
                "health_update": 0.8,
                "wisdom_sharing": 0.7,
                "task_request": 0.6,
                "pheromone": 0.4,
                "heartbeat": 0.3,
                "background_sync": 0.2
            }
            
            base_impact = impact_map.get(message_type, 0.5)
            
            # Factor in message size (larger = potentially more impactful)
            message_size = len(str(message))
            size_factor = min(message_size / 1000.0, 0.3)  # Cap at 0.3 bonus
            
            # Factor in swarm state (critical states amplify impact)
            system_health = self._get_system_health()
            health_factor = 1.0 - system_health  # Lower health = higher impact potential
            
            total_impact = min(1.0, base_impact + size_factor + (health_factor * 0.2))
            
            return total_impact
            
        except Exception as e:
            record_log(f"🌿 Impact assessment whispered error: {e}")
            return 0.5
    
    def _assess_time_sensitivity(self, message: Dict[str, Any]) -> float:
        """🌱 Assesses time sensitivity of the information."""
        try:
            message_type = message.get("type", "unknown")
            
            # Different message types have different time sensitivities
            sensitivity_map = {
                "critical_alert": 1.0,
                "health_update": 0.9,
                "task_request": 0.7,
                "heartbeat": 0.6,
                "wisdom_sharing": 0.4,
                "pheromone": 0.3,
                "background_sync": 0.1
            }
            
            base_sensitivity = sensitivity_map.get(message_type, 0.5)
            
            # Check if message has explicit timestamp or deadline
            if "timestamp" in message:
                try:
                    msg_time = datetime.fromisoformat(message["timestamp"].replace('Z', '+00:00'))
                    age_seconds = (datetime.now(timezone.utc) - msg_time).total_seconds()
                    
                    # Decay sensitivity with age (exponential decay)
                    age_factor = max(0.1, 1.0 / (1.0 + age_seconds / 300.0))  # 5-minute half-life
                    return min(1.0, base_sensitivity * age_factor)
                except:
                    pass
            
            return base_sensitivity
            
        except Exception as e:
            record_log(f"🌿 Time sensitivity assessment whispered error: {e}")
            return 0.5
    
    def _calculate_action_cost(self, action: str, message: Dict[str, Any], targets: List[str]) -> float:
        """🌱 Calculates cost for a communication action."""
        try:
            message_size = len(str(message))
            target_count = len(targets)
            
            if action == "broadcast":
                # Cost scales with number of targets and message size
                base_cost = 0.1 * target_count * (message_size / 1000.0)
                
                # Factor in network congestion
                avg_load = sum(self._query_node_load(node_id) for node_id in targets) / max(target_count, 1)
                congestion_factor = 1.0 + avg_load
                
                return min(1.0, base_cost * congestion_factor)
                
            elif action == "specialize":
                # Cost of finding specialized nodes
                return 0.3 + (0.1 * target_count)
                
            elif action == "compress":
                # Cost of compression processing
                compression_cost = 0.2 + (message_size / 5000.0)
                return min(0.8, compression_cost)
                
            elif action == "hold":
                # Cost of delay/storage
                return 0.1
                
            return 0.5  # Default moderate cost
            
        except Exception as e:
            record_log(f"🌿 Action cost calculation whispered error: {e}")
            return 0.5
    
    def _calculate_action_benefit(self, action: str, message: Dict[str, Any], targets: List[str]) -> float:
        """🌱 Calculates benefit for a communication action."""
        try:
            information_value = self._predict_information_value(message)
            target_count = len(targets)
            
            if action == "broadcast":
                # Benefit scales with value and reach
                reach_benefit = min(1.0, target_count / 10.0)  # Diminishing returns
                return information_value * (0.5 + 0.5 * reach_benefit)
                
            elif action == "specialize":
                # High benefit if targets are well-suited
                specialization_benefit = 0.8  # Assume good specialization matching
                return information_value * specialization_benefit
                
            elif action == "compress":
                # Benefit from reduced bandwidth usage
                message_size = len(str(message))
                compression_benefit = min(0.7, message_size / 2000.0)
                return information_value * (0.6 + compression_benefit)
                
            elif action == "hold":
                # Benefit from preserving resources
                return 0.2  # Low but non-zero benefit
                
            return 0.5  # Default moderate benefit
            
        except Exception as e:
            record_log(f"🌿 Action benefit calculation whispered error: {e}")
            return 0.5
    
    def update_specialization_scores(self) -> None:
        """🌿 Updates specialization scores for all nodes and functions.
        
        Implements the specialization score calculation from GrokMyc:
        S_ik = ω·F_ik + φ·P_i + ψ·N_ik
        
        Where:
        - ω (omega) = 0.5 - Processing frequency weight
        - φ (phi) = 0.3 - Network position weight  
        - ψ (psi) = 0.2 - Neighbor complementarity weight
        """
        try:
            # Initialize specialization scores for all nodes
            for node_id in self.nodes:
                if node_id not in self.specialization_scores:
                    self.specialization_scores[node_id] = {}
                
                for function_type in self.function_types:
                    # Get frequency of processing this function type
                    processing_frequency = self._get_processing_frequency(node_id, function_type)
                    
                    # Get network position (betweenness centrality)
                    network_position = self._calculate_node_centrality(node_id)
                    
                    # Get neighbor specialization complementarity
                    neighbor_complementarity = self._calculate_complementarity(node_id, function_type)
                    
                    # Apply specialization formula: S_ik = ω·F_ik + φ·P_i + ψ·N_ik
                    specialization_score = (
                        self.omega * processing_frequency +
                        self.phi * network_position +
                        self.psi * neighbor_complementarity
                    )
                    
                    # Store the score
                    self.specialization_scores[node_id][function_type] = specialization_score
            
            # Log specialization updates
            total_scores = sum(len(scores) for scores in self.specialization_scores.values())
            record_log(f"🌿 Updated {total_scores} specialization scores across {len(self.nodes)} nodes")
            
            # Store specialization evolution in Memory Garden
            if self.garden and MEMORY_GARDEN_AVAILABLE:
                try:
                    # Create summary of specialization distribution
                    specialization_summary = {}
                    for function_type in self.function_types:
                        scores = [self.specialization_scores[node_id].get(function_type, 0.0) 
                                 for node_id in self.nodes]
                        if scores:
                            specialization_summary[function_type] = {
                                "mean": sum(scores) / len(scores),
                                "max": max(scores),
                                "min": min(scores),
                                "specialized_nodes": len([s for s in scores if s > 0.7])
                            }
                    
                    sprout_memory({
                        "type": "specialization_update",
                        "node_count": len(self.nodes),
                        "function_types": self.function_types,
                        "specialization_summary": specialization_summary,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    })
                except Exception as e:
                    record_log(f"🌿 Could not sprout specialization memory: {e}")
                    
        except Exception as e:
            record_log(f"🌿 Specialization score update whispered error: {e}")
    
    def _get_processing_frequency(self, node_id: str, function_type: str) -> float:
        """🌱 Gets the processing frequency for a function type on a specific node."""
        try:
            if node_id not in self.nodes:
                return 0.0
            
            # Check if we have processing history tracked
            if hasattr(self, 'processing_history'):
                # Count recent processing events for this node and function type
                recent_events = [event for event in getattr(self, 'processing_history', [])
                               if (event.get('node_id') == node_id and 
                                   event.get('function_type') == function_type)]
                
                # Calculate frequency as events per time unit
                if recent_events:
                    time_span_hours = 24  # Look at last 24 hours
                    frequency = min(1.0, len(recent_events) / (time_span_hours * 10))  # Normalize
                    return frequency
            
            # Fallback: estimate based on node role and capabilities
            node_info = self.nodes[node_id]
            fragment_role = node_info.get("fragment_assignment", {}).get("role", "perceiver")
            
            # Map roles to function type affinities
            role_affinities = {
                "perceiver": {
                    "perception": 0.8,
                    "reasoning": 0.3,
                    "synthesis": 0.2,
                    "memory": 0.4
                },
                "reasoner": {
                    "perception": 0.4,
                    "reasoning": 0.8,
                    "synthesis": 0.6,
                    "memory": 0.3
                },
                "aggregator": {
                    "perception": 0.3,
                    "reasoning": 0.6,
                    "synthesis": 0.8,
                    "memory": 0.7
                }
            }
            
            return role_affinities.get(fragment_role, {}).get(function_type, 0.5)
            
        except Exception as e:
            record_log(f"🌿 Processing frequency calculation whispered error: {e}")
            return 0.5
    
    def _calculate_node_centrality(self, node_id: str) -> float:
        """🌱 Calculates network position (betweenness centrality) for a node."""
        try:
            if len(self.nodes) <= 2:
                return 1.0  # All nodes are central in small networks
            
            # Simple approximation of betweenness centrality
            # Count how many active connections this node has
            node_connections = 0
            total_connections = 0
            
            for connection_key in self.active_connections:
                source, target = connection_key
                total_connections += 1
                
                if source == node_id or target == node_id:
                    node_connections += 1
            
            if total_connections == 0:
                return 0.5  # Default moderate centrality
            
            # Calculate relative centrality
            centrality = node_connections / total_connections
            
            # Bonus for being connected to diverse node types
            connected_roles = set()
            for connection_key in self.active_connections:
                source, target = connection_key
                if source == node_id and target in self.nodes:
                    role = self.nodes[target].get("fragment_assignment", {}).get("role", "unknown")
                    connected_roles.add(role)
                elif target == node_id and source in self.nodes:
                    role = self.nodes[source].get("fragment_assignment", {}).get("role", "unknown")
                    connected_roles.add(role)
            
            diversity_bonus = len(connected_roles) / len(self.function_types)
            centrality = min(1.0, centrality + (diversity_bonus * 0.2))
            
            return centrality
            
        except Exception as e:
            record_log(f"🌿 Node centrality calculation whispered error: {e}")
            return 0.5
    
    def _calculate_complementarity(self, node_id: str, function_type: str) -> float:
        """🌱 Calculates neighbor complementarity for a function type."""
        try:
            if node_id not in self.nodes:
                return 0.0
            
            # Find neighboring nodes (directly connected)
            neighbors = []
            for connection_key in self.active_connections:
                source, target = connection_key
                if source == node_id and target in self.nodes:
                    neighbors.append(target)
                elif target == node_id and source in self.nodes:
                    neighbors.append(source)
            
            if not neighbors:
                return 0.5  # Default moderate complementarity when no neighbors
            
            # Calculate how much this node's specialization in this function
            # complements (is different from) its neighbors
            neighbor_scores = []
            for neighbor_id in neighbors:
                if neighbor_id in self.specialization_scores:
                    neighbor_score = self.specialization_scores[neighbor_id].get(function_type, 0.5)
                    neighbor_scores.append(neighbor_score)
            
            if not neighbor_scores:
                return 0.7  # High complementarity when neighbors have no scores yet
            
            # Calculate average neighbor specialization in this function
            avg_neighbor_specialization = sum(neighbor_scores) / len(neighbor_scores)
            
            # Complementarity is inversely related to neighbor specialization
            # If neighbors are highly specialized in this function, 
            # this node has low complementarity (redundant)
            # If neighbors are not specialized, this node has high complementarity (unique)
            complementarity = 1.0 - avg_neighbor_specialization
            
            # Add bonus for diversity of neighbor roles
            neighbor_roles = set()
            for neighbor_id in neighbors:
                if neighbor_id in self.nodes:
                    role = self.nodes[neighbor_id].get("fragment_assignment", {}).get("role", "unknown")
                    neighbor_roles.add(role)
            
            diversity_factor = len(neighbor_roles) / max(len(self.function_types), 1)
            complementarity = min(1.0, complementarity + (diversity_factor * 0.1))
            
            return complementarity
            
        except Exception as e:
            record_log(f"🌿 Complementarity calculation whispered error: {e}")
            return 0.5
    
    def get_most_specialized_node(self, function_type: str) -> Optional[str]:
        """🌱 Gets the node most specialized for a specific function type."""
        try:
            if function_type not in self.function_types:
                record_log(f"🌿 Unknown function type: {function_type}")
                return None
            
            best_node = None
            best_score = 0.0
            
            for node_id, scores in self.specialization_scores.items():
                if node_id in self.nodes and self.nodes[node_id].get("status") == "active":
                    score = scores.get(function_type, 0.0)
                    if score > best_score:
                        best_score = score
                        best_node = node_id
            
            if best_node:
                record_log(f"🌱 Most specialized node for {function_type}: {best_node[:8]} (score: {best_score:.3f})")
            
            return best_node
            
        except Exception as e:
            record_log(f"🌿 Specialized node lookup whispered error: {e}")
            return None
    
    def get_specialization_distribution(self) -> Dict[str, Dict[str, float]]:
        """🌱 Gets the current specialization distribution across all nodes."""
        try:
            distribution = {}
            
            for function_type in self.function_types:
                scores = []
                node_scores = {}
                
                for node_id in self.nodes:
                    if (node_id in self.specialization_scores and 
                        self.nodes[node_id].get("status") == "active"):
                        score = self.specialization_scores[node_id].get(function_type, 0.0)
                        scores.append(score)
                        node_scores[node_id] = score
                
                if scores:
                    distribution[function_type] = {
                        "mean": sum(scores) / len(scores),
                        "max": max(scores),
                        "min": min(scores),
                        "std": (sum((x - sum(scores)/len(scores))**2 for x in scores) / len(scores))**0.5,
                        "node_scores": node_scores,
                        "highly_specialized": len([s for s in scores if s > 0.7]),
                        "total_nodes": len(scores)
                    }
                else:
                    distribution[function_type] = {
                        "mean": 0.0, "max": 0.0, "min": 0.0, "std": 0.0,
                        "node_scores": {}, "highly_specialized": 0, "total_nodes": 0
                    }
            
            return distribution
            
        except Exception as e:
            record_log(f"🌿 Specialization distribution calculation whispered error: {e}")
            return {}
    
    def track_processing_event(self, node_id: str, function_type: str, duration: float = 0.0) -> None:
        """🌱 Tracks a processing event for specialization scoring."""
        try:
            if not hasattr(self, 'processing_history'):
                self.processing_history = []
            
            event = {
                "node_id": node_id,
                "function_type": function_type,
                "duration": duration,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            self.processing_history.append(event)
            
            # Keep history manageable (last 1000 events)
            if len(self.processing_history) > 1000:
                self.processing_history = self.processing_history[-500:]
            
            # Periodically update specialization scores
            if len(self.processing_history) % 50 == 0:
                self.update_specialization_scores()
                
        except Exception as e:
            record_log(f"🌿 Processing event tracking whispered error: {e}")

def enrich_with_garden_wisdom(state: SwarmState) -> Optional[Any]:
    """🌱 Enriches neural input with Memory Garden context."""
    if not MEMORY_GARDEN_AVAILABLE:
        return _fallback_context(state)
    
    try:
        # This would recall relevant memories from the garden
        # For now, provide a simple context enrichment
        context_features = [
            state.kinship,
            state.memory_depth / 1000.0,
            len(state.user_nutrient) / 100.0,
            state.cycle_count / 1000.0,
            state.node_count / 10.0,
            hash(state.breath) % 100 / 100.0,
            time.time() % 86400 / 86400.0,
            hash(state.timestamp) % 100 / 100.0
        ]
        
        if TORCH_AVAILABLE:
            return torch.tensor(context_features, dtype=torch.float32)
        else:
            return context_features
            
    except Exception as e:
        record_log(f"🌿 Garden wisdom enrichment whispered: {e}")
        return _fallback_context(state)

def _fallback_context(state: SwarmState) -> List[float]:
    """🌿 Provides fallback context when Memory Garden unavailable."""
    return [
        state.kinship,
        min(state.memory_depth / 1000.0, 1.0),
        min(state.cycle_count / 1000.0, 1.0),
        min(state.node_count / 10.0, 1.0),
        0.5,  # Neutral baseline
        0.5,  # Neutral baseline
        time.time() % 86400 / 86400.0,  # Time of day
        0.5   # Neutral baseline
    ]

class MycelialFeedback:
    """🌿 Weaves feedback loops between neural wisdom and memory gardens.
    
    Creates continuous learning loops where Memory Garden experiences inform 
    neural predictions, and neural outputs sprout new memories in return."""
    
    def __init__(self, swarm_mind: 'SwarmMind'):
        """🌱 Initializes the mycelial feedback system."""
        self.swarm_mind = swarm_mind
        self.learning_history = []
        self.training_cycles = 0
        record_log("🌿 Mycelial feedback loops awakened - neural wisdom and memory gardens entwined")
    
    def learn_from_nutrients(self, input_state: SwarmState, user_nutrient: str) -> Optional[Dict[str, Any]]:
        """🌱 Weaves neural wisdom into the garden, nourishing future pulses."""
        try:
            # Process through distributed neural network
            fragment_outputs = self.swarm_mind.entwine_neural_pulse(input_state)
            if not fragment_outputs:
                return None
            
            # Aggregate collective wisdom
            aggregated_wisdom = self.swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
            if not aggregated_wisdom:
                return None
            
            # Store prediction in Memory Garden if available
            if self.swarm_mind.garden and MEMORY_GARDEN_AVAILABLE:
                memory_data = {
                    "type": "mycelial_learning",
                    "breath": input_state.breath,
                    "neural_output": aggregated_wisdom["collective_confidence"],
                    "consensus_strength": aggregated_wisdom["consensus_strength"],
                    "user_nutrient": user_nutrient,
                    "node_count": len(self.swarm_mind.nodes),
                    "fragment_contributions": len(fragment_outputs),
                    "wisdom_variance": aggregated_wisdom.get("wisdom_variance", 0.0),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                
                try:
                    sprout_memory(memory_data)
                    
                    # Create connections to similar memories
                    confidence_range = (
                        aggregated_wisdom["collective_confidence"] - 0.1,
                        aggregated_wisdom["collective_confidence"] + 0.1
                    )
                    
                    if hasattr(self.swarm_mind.garden, 'recall_verdant_memory'):
                        similar_memories = recall_verdant_memory({
                            "neural_output_range": confidence_range,
                            "breath": input_state.breath
                        })
                        
                        if similar_memories and hasattr(self.swarm_mind.garden, 'weave_connection'):
                            self.swarm_mind.garden.weave_connection(memory_data, similar_memories)
                    
                    record_log(f"🌱 Neural wisdom sprouted memory with confidence {aggregated_wisdom['collective_confidence']:.3f}")
                    
                except Exception as e:
                    record_log(f"🌿 Memory sprouting whispered error: {e}")
            
            # Record learning event
            learning_event = {
                "input_state": asdict(input_state),
                "wisdom": aggregated_wisdom,
                "user_nutrient": user_nutrient,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            self.learning_history.append(learning_event)
            if len(self.learning_history) > 200:  # Keep last 200 learning events
                self.learning_history = self.learning_history[-200:]
            
            record_log("🌱 The swarm's wisdom sprouts a new memory, entwined with nutrients")
            return learning_event
            
        except Exception as e:
            record_log(f"🌿 Nutrient learning whispered error: {e}")
            return None
    
    def periodic_garden_training(self) -> bool:
        """🌸 Periodically fine-tunes neural fragments using garden memories."""
        try:
            if not self.swarm_mind.garden or not MEMORY_GARDEN_AVAILABLE:
                record_log("🌿 Garden training skipped - Memory Garden not available")
                return False
            
            # Gather recent memories for training
            if hasattr(self.swarm_mind.garden, 'gather_recent_memories'):
                recent_memories = self.swarm_mind.garden.gather_recent_memories(limit=100)
            else:
                # Fallback: use learning history
                recent_memories = self.learning_history[-50:] if self.learning_history else []
            
            if len(recent_memories) < 20:  # Need sufficient data for meaningful training
                record_log("🌿 Insufficient memories for garden training - patience nurtures wisdom")
                return False
            
            # Prepare training data from memories
            training_data = self._prepare_training_batch(recent_memories)
            if not training_data:
                return False
            
            # Distribute training across capable nodes
            trained_fragments = 0
            for node_id, fragment in self.swarm_mind.fragments.items():
                node_capabilities = self.swarm_mind.nodes.get(node_id, {}).get("capabilities", {})
                ram_gb = node_capabilities.get("ram_gb", 8)
                
                if ram_gb > 12:  # Only train on nodes with sufficient memory
                    if self._train_fragment(fragment, training_data):
                        trained_fragments += 1
            
            self.training_cycles += 1
            
            if trained_fragments > 0:
                record_log(f"🌸 Neural fragments evolved ({trained_fragments} fragments trained, cycle {self.training_cycles})")
                return True
            else:
                record_log("🌿 No fragments were suitable for training in this cycle")
                return False
                
        except Exception as e:
            record_log(f"🌿 Garden training whispered error: {e}")
            return False
    
    def _prepare_training_batch(self, memories: List[Dict[str, Any]]) -> Optional[List[Tuple[List[float], List[float]]]]:
        """🌱 Prepares training data from memories."""
        try:
            training_pairs = []
            
            for memory in memories:
                # Extract input features
                if "input_state" in memory:
                    state_data = memory["input_state"]
                    input_features = [
                        state_data.get("kinship", 0.0),
                        min(state_data.get("memory_depth", 0) / 1000.0, 1.0),
                        min(state_data.get("cycle_count", 0) / 1000.0, 1.0),
                        min(state_data.get("node_count", 1) / 10.0, 1.0),
                        hash(state_data.get("breath", "")) % 100 / 100.0,
                        hash(state_data.get("user_nutrient", "")) % 100 / 100.0,
                        time.time() % 86400 / 86400.0
                    ]
                elif "breath" in memory:
                    # Direct memory format
                    input_features = [
                        memory.get("kinship", 0.0) if "kinship" in memory else 0.5,
                        min(memory.get("memory_depth", 0) / 1000.0, 1.0) if "memory_depth" in memory else 0.5,
                        memory.get("cycle_count", 0) / 1000.0 if "cycle_count" in memory else 0.5,
                        memory.get("node_count", 1) / 10.0 if "node_count" in memory else 0.1,
                        hash(memory.get("breath", "")) % 100 / 100.0,
                        hash(memory.get("user_nutrient", "")) % 100 / 100.0,
                        time.time() % 86400 / 86400.0
                    ]
                else:
                    continue
                
                # Extract target values
                wisdom = memory.get("wisdom", {})
                if isinstance(wisdom, dict):
                    target_values = [
                        wisdom.get("collective_confidence", 0.5),
                        wisdom.get("consensus_strength", 0.5),
                        min(wisdom.get("wisdom_variance", 0.0), 1.0),
                        wisdom.get("fragment_count", 1) / 10.0
                    ]
                else:
                    # Fallback target
                    target_values = [
                        memory.get("neural_output", 0.5),
                        memory.get("consensus_strength", 0.5),
                        0.5,  # Default variance
                        0.1   # Default fragment count
                    ]
                
                # Pad/truncate to expected dimensions
                input_features = (input_features + [0.5] * 7)[:7]
                target_values = (target_values + [0.5] * 16)[:16]
                
                training_pairs.append((input_features, target_values))
            
            if len(training_pairs) < 10:
                record_log("🌿 Insufficient training pairs created from memories")
                return None
            
            record_log(f"🌱 Prepared {len(training_pairs)} training pairs from garden memories")
            return training_pairs
            
        except Exception as e:
            record_log(f"🌿 Training batch preparation whispered error: {e}")
            return None
    
    def _train_fragment(self, fragment: NeuralFragment, training_data: List[Tuple[List[float], List[float]]]) -> bool:
        """🌸 Trains a neural fragment using prepared data."""
        try:
            if not TORCH_AVAILABLE or not hasattr(fragment, 'layers') or not isinstance(fragment.layers, nn.Sequential):
                # Symbolic training for non-PyTorch fragments
                return self._symbolic_train_fragment(fragment, training_data)
            
            # PyTorch training
            fragment.layers.train()
            optimizer = torch.optim.Adam(fragment.layers.parameters(), lr=0.001)
            criterion = nn.MSELoss()
            
            total_loss = 0.0
            batch_size = min(10, len(training_data))
            
            for i in range(0, len(training_data), batch_size):
                batch = training_data[i:i+batch_size]
                
                # Prepare batch tensors
                inputs = torch.tensor([pair[0] for pair in batch], dtype=torch.float32)
                targets = torch.tensor([pair[1] for pair in batch], dtype=torch.float32)
                
                # Forward pass
                optimizer.zero_grad()
                outputs = fragment.layers(inputs)
                loss = criterion(outputs, targets)
                
                # Backward pass
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            fragment.layers.eval()
            avg_loss = total_loss / (len(training_data) / batch_size)
            
            record_log(f"🌸 Fragment {fragment.node_id[:8]} trained with loss {avg_loss:.4f}")
            return True
            
        except Exception as e:
            record_log(f"🌿 Fragment training whispered error: {e}")
            return False
    
    def _symbolic_train_fragment(self, fragment: NeuralFragment, training_data: List[Tuple[List[float], List[float]]]) -> bool:
        """🌱 Symbolic training for non-PyTorch fragments."""
        try:
            if not hasattr(fragment, 'layers') or not isinstance(fragment.layers, dict):
                return False
            
            # Simple weight adjustment based on training data
            weights = fragment.layers.get("weights", [])
            biases = fragment.layers.get("biases", [])
            
            if not weights or not biases:
                return False
            
            # Compute simple gradients and adjust weights
            learning_rate = 0.01
            weight_adjustments = [0.0] * len(weights)
            bias_adjustments = [0.0] * len(biases)
            
            for input_vals, target_vals in training_data:
                # Simple gradient approximation
                current_output = fragment._symbolic_forward(input_vals)
                
                for i, (output, target) in enumerate(zip(current_output, target_vals)):
                    error = target - output
                    
                    # Adjust weights and biases
                    for j, input_val in enumerate(input_vals):
                        weight_idx = i * len(input_vals) + j
                        if weight_idx < len(weight_adjustments):
                            weight_adjustments[weight_idx] += learning_rate * error * input_val
                    
                    if i < len(bias_adjustments):
                        bias_adjustments[i] += learning_rate * error
            
            # Apply adjustments
            for i in range(len(weights)):
                weights[i] += weight_adjustments[i] / len(training_data)
            
            for i in range(len(biases)):
                biases[i] += bias_adjustments[i] / len(training_data)
            
            fragment.layers["weights"] = weights
            fragment.layers["biases"] = biases
            
            record_log(f"🌱 Symbolic fragment {fragment.node_id[:8]} weights adjusted")
            return True
            
        except Exception as e:
            record_log(f"🌿 Symbolic training whispered error: {e}")
            return False

class SwarmHealthMonitor:
    """🌬️ Monitors the swarm's collective vitality and breath patterns.
    
    Provides continuous assessment of swarm health across multiple dimensions,
    adapting behavior to maintain optimal system vitality."""
    
    def __init__(self, swarm_mind: 'SwarmMind'):
        """🌱 Initializes the swarm health monitoring system."""
        self.swarm_mind = swarm_mind
        self.health_history = []
        self.assessment_count = 0
        self.last_critical_time = None
        record_log("🌬️ Swarm health monitor awakened - vitality sensors active")
    
    def assess_swarm_vitality(self) -> Dict[str, Any]:
        """🌿 Assesses overall swarm health across multiple dimensions."""
        try:
            self.assessment_count += 1
            assessment_time = datetime.now(timezone.utc).isoformat()
            
            # Initialize vitality assessment
            vitality = {
                "assessment_id": self.assessment_count,
                "timestamp": assessment_time,
                "dimensions": {},
                "overall": 0.0,
                "state": "unknown",
                "recommendations": []
            }
            
            # 1. Node Health Assessment
            node_health = self._assess_node_health()
            vitality["dimensions"]["node_health"] = node_health
            
            # 2. Memory Coherence Assessment
            memory_coherence = self._assess_memory_coherence()
            vitality["dimensions"]["memory_coherence"] = memory_coherence
            
            # 3. Neural Synchrony Assessment
            neural_synchrony = self._assess_neural_synchrony()
            vitality["dimensions"]["neural_synchrony"] = neural_synchrony
            
            # 4. Communication Health Assessment
            communication_flow = self._assess_communication_health()
            vitality["dimensions"]["communication_flow"] = communication_flow
            
            # 5. Resource Utilization Assessment
            resource_utilization = self._assess_resource_utilization()
            vitality["dimensions"]["resource_utilization"] = resource_utilization
            
            # Calculate overall vitality score
            dimension_scores = [
                node_health["score"],
                memory_coherence["score"],
                neural_synchrony["score"],
                communication_flow["score"],
                resource_utilization["score"]
            ]
            
            vitality["overall"] = sum(dimension_scores) / len(dimension_scores)
            
            # Determine swarm state
            overall_score = vitality["overall"]
            if overall_score > 0.85:
                vitality["state"] = "thriving"
                vitality["state_emoji"] = "🌸"
            elif overall_score > 0.7:
                vitality["state"] = "healthy"
                vitality["state_emoji"] = "🌿"
            elif overall_score > 0.5:
                vitality["state"] = "stable"
                vitality["state_emoji"] = "🌱"
            elif overall_score > 0.3:
                vitality["state"] = "stressed"
                vitality["state_emoji"] = "🌫️"
            else:
                vitality["state"] = "critical"
                vitality["state_emoji"] = "🌪️"
                self.last_critical_time = assessment_time
            
            # Generate recommendations
            vitality["recommendations"] = self._generate_health_recommendations(vitality)
            
            # Store in history
            self.health_history.append(vitality)
            if len(self.health_history) > 50:  # Keep last 50 assessments
                self.health_history = self.health_history[-50:]
            
            record_log(
                f"🌬️ Swarm vitality assessed: {vitality['state']} {vitality['state_emoji']} "
                f"(score: {vitality['overall']:.3f}, nodes: {len(self.swarm_mind.nodes)})"
            )
            
            return vitality
            
        except Exception as e:
            record_log(f"🌿 Vitality assessment whispered error: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "overall": 0.0,
                "state": "error"
            }
    
    def _assess_node_health(self) -> Dict[str, Any]:
        """🌱 Assesses the health of individual nodes in the swarm."""
        try:
            if not self.swarm_mind.nodes:
                return {"score": 0.0, "status": "no_nodes", "details": {}}
            
            node_scores = []
            node_details = {}
            active_nodes = 0
            
            for node_id, node_info in self.swarm_mind.nodes.items():
                try:
                    capabilities = node_info.get("capabilities", {})
                    status = node_info.get("status", "unknown")
                    
                    if status == "active":
                        active_nodes += 1
                        
                        # Calculate health score based on capabilities
                        ram_score = min(capabilities.get("ram_gb", 4) / 16.0, 1.0)  # 16GB as optimal
                        cpu_score = min(capabilities.get("cpu_count", 2) / 8.0, 1.0)  # 8 cores as optimal
                        gpu_bonus = 0.1 if capabilities.get("gpu_available", False) else 0.0
                        disk_score = min(capabilities.get("disk_free_gb", 10) / 100.0, 1.0)  # 100GB as optimal
                        
                        node_score = (ram_score + cpu_score + disk_score) / 3.0 + gpu_bonus
                        node_score = min(node_score, 1.0)  # Cap at 1.0
                        
                        node_scores.append(node_score)
                        node_details[node_id] = {
                            "score": node_score,
                            "status": status,
                            "ram_gb": capabilities.get("ram_gb", 0),
                            "cpu_count": capabilities.get("cpu_count", 0),
                            "gpu_available": capabilities.get("gpu_available", False)
                        }
                    else:
                        node_details[node_id] = {"score": 0.0, "status": status}
                        
                except Exception as e:
                    record_log(f"🌿 Error assessing node {node_id}: {e}")
                    node_details[node_id] = {"score": 0.0, "status": "error", "error": str(e)}
            
            if not node_scores:
                overall_score = 0.0
            else:
                overall_score = sum(node_scores) / len(node_scores)
            
            # Apply penalty for inactive nodes
            active_ratio = active_nodes / len(self.swarm_mind.nodes)
            overall_score *= active_ratio
            
            return {
                "score": overall_score,
                "status": "healthy" if overall_score > 0.7 else "degraded" if overall_score > 0.4 else "poor",
                "details": {
                    "total_nodes": len(self.swarm_mind.nodes),
                    "active_nodes": active_nodes,
                    "active_ratio": active_ratio,
                    "individual_scores": node_details
                }
            }
            
        except Exception as e:
            record_log(f"🌿 Node health assessment whispered error: {e}")
            return {"score": 0.0, "status": "error", "error": str(e)}
    
    def _assess_memory_coherence(self) -> Dict[str, Any]:
        """🌱 Assesses memory coherence and garden health."""
        try:
            if not self.swarm_mind.garden or not MEMORY_GARDEN_AVAILABLE:
                return {
                    "score": 0.3,  # Partial score without garden
                    "status": "no_garden",
                    "details": {"message": "Memory Garden not available"}
                }
            
            # Assess memory garden health
            if hasattr(self.swarm_mind.garden, 'get_memory_stats'):
                stats = self.swarm_mind.garden.get_memory_stats()
                memory_count = stats.get("total_memories", 0)
                connection_count = stats.get("total_connections", 0)
            else:
                # Fallback assessment
                memory_count = len(self.swarm_mind.wisdom_history)
                connection_count = 0
            
            # Score based on memory richness
            memory_score = min(memory_count / 100.0, 1.0)  # 100 memories as good baseline
            connection_score = min(connection_count / 50.0, 1.0) if connection_count > 0 else 0.3
            
            overall_score = (memory_score + connection_score) / 2.0
            
            return {
                "score": overall_score,
                "status": "rich" if overall_score > 0.7 else "moderate" if overall_score > 0.4 else "sparse",
                "details": {
                    "memory_count": memory_count,
                    "connection_count": connection_count,
                    "wisdom_history_length": len(self.swarm_mind.wisdom_history)
                }
            }
            
        except Exception as e:
            record_log(f"🌿 Memory coherence assessment whispered error: {e}")
            return {"score": 0.0, "status": "error", "error": str(e)}
    
    def _assess_neural_synchrony(self) -> Dict[str, Any]:
        """🌸 Assesses neural fragment coordination and synchrony."""
        try:
            if not self.swarm_mind.fragments:
                return {
                    "score": 0.0,
                    "status": "no_fragments",
                    "details": {"message": "No neural fragments available"}
                }
            
            # Test fragment responsiveness
            test_state = SwarmState(
                breath="Health Check",
                kinship=0.5,
                memory_depth=10,
                user_nutrient="System Assessment",
                cycle_count=1,
                node_count=len(self.swarm_mind.nodes),
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            fragment_outputs = self.swarm_mind.entwine_neural_pulse(test_state)
            
            if not fragment_outputs:
                return {
                    "score": 0.2,
                    "status": "unresponsive",
                    "details": {"message": "Fragments not responding to test pulse"}
                }
            
            # Assess output consistency and quality
            output_values = []
            fragment_details = {}
            
            for node_id, fragment_data in fragment_outputs.items():
                output = fragment_data["output"]
                
                if hasattr(output, 'tolist'):
                    values = output.tolist()
                elif isinstance(output, (list, tuple)):
                    values = list(output)
                else:
                    values = [float(output)]
                
                # Store representative value
                representative_value = sum(values) / len(values) if values else 0.0
                output_values.append(representative_value)
                
                fragment_details[node_id] = {
                    "output_size": len(values),
                    "representative_value": representative_value,
                    "fragment_type": fragment_data.get("fragment_type", "unknown"),
                    "complexity": fragment_data.get("complexity", "unknown")
                }
            
            # Calculate synchrony metrics
            if len(output_values) > 1:
                mean_output = sum(output_values) / len(output_values)
                variance = sum((x - mean_output) ** 2 for x in output_values) / len(output_values)
                synchrony_score = max(0.0, 1.0 - variance)  # Lower variance = higher synchrony
            else:
                synchrony_score = 1.0  # Single fragment is perfectly synchronized with itself
            
            responsiveness_score = len(fragment_outputs) / len(self.swarm_mind.fragments)
            overall_score = (synchrony_score + responsiveness_score) / 2.0
            
            return {
                "score": overall_score,
                "status": "synchronized" if overall_score > 0.7 else "partial" if overall_score > 0.4 else "desynchronized",
                "details": {
                    "responsive_fragments": len(fragment_outputs),
                    "total_fragments": len(self.swarm_mind.fragments),
                    "responsiveness_ratio": responsiveness_score,
                    "synchrony_score": synchrony_score,
                    "output_variance": variance if len(output_values) > 1 else 0.0,
                    "fragment_details": fragment_details
                }
            }
            
        except Exception as e:
            record_log(f"🌿 Neural synchrony assessment whispered error: {e}")
            return {"score": 0.0, "status": "error", "error": str(e)}
    
    def _assess_communication_health(self) -> Dict[str, Any]:
        """🌿 Assesses inter-node communication health."""
        try:
            comm_type = self.swarm_mind.communication.get("type", "fallback")
            connection = self.swarm_mind.communication.get("connection")
            
            if comm_type == "rabbitmq" and connection:
                try:
                    # Test RabbitMQ connection
                    if hasattr(connection, 'is_open') and connection.is_open:
                        score = 1.0
                        status = "excellent"
                    else:
                        score = 0.3
                        status = "degraded"
                except:
                    score = 0.1
                    status = "failed"
                    
            elif comm_type == "redis" and connection:
                try:
                    # Test Redis connection
                    connection.ping()
                    score = 0.8
                    status = "good"
                except:
                    score = 0.1
                    status = "failed"
                    
            elif comm_type == "fallback":
                score = 0.4
                status = "basic"
            else:
                score = 0.0
                status = "none"
            
            return {
                "score": score,
                "status": status,
                "details": {
                    "communication_type": comm_type,
                    "connection_available": connection is not None,
                    "node_count": len(self.swarm_mind.nodes)
                }
            }
            
        except Exception as e:
            record_log(f"🌿 Communication health assessment whispered error: {e}")
            return {"score": 0.0, "status": "error", "error": str(e)}
    
    def _assess_resource_utilization(self) -> Dict[str, Any]:
        """🌱 Assesses system resource utilization."""
        try:
            if not PSUTIL_AVAILABLE:
                return {
                    "score": 0.5,
                    "status": "unknown",
                    "details": {"message": "psutil not available for resource monitoring"}
                }
            
            # Get system metrics
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=0.1)
            disk = psutil.disk_usage('/')
            
            # Calculate utilization scores (lower utilization = higher score for stability)
            memory_score = max(0.0, 1.0 - memory.percent / 100.0)
            cpu_score = max(0.0, 1.0 - cpu_percent / 100.0)
            disk_score = max(0.0, 1.0 - (disk.used / disk.total))
            
            overall_score = (memory_score + cpu_score + disk_score) / 3.0
            
            if overall_score > 0.7:
                status = "optimal"
            elif overall_score > 0.5:
                status = "moderate"
            elif overall_score > 0.3:
                status = "high"
            else:
                status = "critical"
            
            return {
                "score": overall_score,
                "status": status,
                "details": {
                    "memory_percent_used": memory.percent,
                    "cpu_percent_used": cpu_percent,
                    "disk_percent_used": (disk.used / disk.total) * 100,
                    "available_memory_gb": memory.available / (1024 ** 3),
                    "free_disk_gb": disk.free / (1024 ** 3)
                }
            }
            
        except Exception as e:
            record_log(f"🌿 Resource utilization assessment whispered error: {e}")
            return {"score": 0.0, "status": "error", "error": str(e)}
    
    def _generate_health_recommendations(self, vitality: Dict[str, Any]) -> List[str]:
        """🌸 Generates actionable recommendations based on vitality assessment."""
        recommendations = []
        overall_score = vitality["overall"]
        dimensions = vitality["dimensions"]
        
        # Overall health recommendations
        if overall_score < 0.3:
            recommendations.append("CRITICAL: Consider emergency conservation mode")
            recommendations.append("Reduce neural fragment complexity")
            recommendations.append("Disable non-essential processing")
        elif overall_score < 0.5:
            recommendations.append("Implement stress reduction measures")
            recommendations.append("Monitor resource usage closely")
        
        # Node health recommendations
        node_health = dimensions.get("node_health", {})
        if node_health.get("score", 1.0) < 0.5:
            active_ratio = node_health.get("details", {}).get("active_ratio", 1.0)
            if active_ratio < 0.8:
                recommendations.append("Investigate inactive nodes")
                recommendations.append("Consider node health recovery procedures")
        
        # Memory coherence recommendations
        memory_coherence = dimensions.get("memory_coherence", {})
        if memory_coherence.get("score", 1.0) < 0.4:
            recommendations.append("Enhance memory garden connectivity")
            recommendations.append("Increase learning feedback cycles")
        
        # Neural synchrony recommendations
        neural_synchrony = dimensions.get("neural_synchrony", {})
        if neural_synchrony.get("score", 1.0) < 0.6:
            recommendations.append("Improve neural fragment coordination")
            recommendations.append("Consider fragment rebalancing")
        
        # Communication recommendations
        communication = dimensions.get("communication_flow", {})
        if communication.get("score", 1.0) < 0.5:
            recommendations.append("Investigate communication infrastructure")
            recommendations.append("Consider fallback communication methods")
        
        # Resource utilization recommendations
        resources = dimensions.get("resource_utilization", {})
        if resources.get("score", 1.0) < 0.4:
            recommendations.append("Optimize resource usage")
            recommendations.append("Consider reducing processing load")
        
        return recommendations
    
    def adapt_to_vitality(self, vitality: Dict[str, Any]) -> Dict[str, Any]:
        """🌱 Adapts swarm behavior based on vitality assessment."""
        try:
            adaptations = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "vitality_state": vitality.get("state", "unknown"),
                "overall_score": vitality.get("overall", 0.0),
                "actions_taken": [],
                "parameters_adjusted": {}
            }
            
            state = vitality.get("state", "unknown")
            overall_score = vitality.get("overall", 0.0)
            
            if state == "critical":
                # Emergency conservation mode
                adaptations["actions_taken"].extend([
                    "Activated emergency conservation mode",
                    "Reduced fragment processing complexity",
                    "Disabled non-essential neural operations"
                ])
                adaptations["parameters_adjusted"]["processing_intensity"] = "minimal"
                adaptations["parameters_adjusted"]["fragment_complexity_limit"] = "minimal"
                adaptations["parameters_adjusted"]["background_tasks_enabled"] = False
                
                record_log("🌪️ CRITICAL: Emergency conservation mode activated")
                
            elif state == "stressed":
                # Stress reduction measures
                adaptations["actions_taken"].extend([
                    "Implemented stress reduction measures",
                    "Reduced processing frequency",
                    "Optimized memory usage"
                ])
                adaptations["parameters_adjusted"]["processing_intensity"] = "reduced"
                adaptations["parameters_adjusted"]["memory_cleanup_frequency"] = "increased"
                adaptations["parameters_adjusted"]["fragment_training_frequency"] = "reduced"
                
                record_log("🌫️ Stress detected: implementing adaptive measures")
                
            elif state == "stable":
                # Maintain current operations
                adaptations["actions_taken"].append("Maintaining stable operations")
                adaptations["parameters_adjusted"]["processing_intensity"] = "normal"
                
                record_log("🌱 Stable state: maintaining current operations")
                
            elif state in ["healthy", "thriving"]:
                # Opportunity for enhancement
                if overall_score > 0.8:
                    adaptations["actions_taken"].extend([
                        "Enabled enhanced processing mode",
                        "Increased learning frequency",
                        "Activated advanced neural coordination"
                    ])
                    adaptations["parameters_adjusted"]["processing_intensity"] = "enhanced"
                    adaptations["parameters_adjusted"]["fragment_training_frequency"] = "increased"
                    adaptations["parameters_adjusted"]["exploration_enabled"] = True
                    
                    record_log("🌸 Thriving state: activating enhanced capabilities")
                else:
                    adaptations["actions_taken"].append("Optimized for healthy operations")
                    adaptations["parameters_adjusted"]["processing_intensity"] = "normal"
                    
                    record_log("🌿 Healthy state: optimized operations active")
            
            # Store adaptation in history
            if not hasattr(self, 'adaptation_history'):
                self.adaptation_history = []
            
            self.adaptation_history.append(adaptations)
            if len(self.adaptation_history) > 20:  # Keep last 20 adaptations
                self.adaptation_history = self.adaptation_history[-20:]
            
            return adaptations
            
        except Exception as e:
            record_log(f"🌿 Vitality adaptation whispered error: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "actions_taken": [],
                "parameters_adjusted": {}
            }

# Enhanced initialization with resilience mechanisms
def initialize_swarm_mind(auto_discover: bool = True, restore_from_persistence: bool = True) -> Optional[SwarmMind]:
    """🌱 Gracefully initializes the SwarmMind with comprehensive error handling and recovery."""
    try:
        swarm_mind = SwarmMind(auto_discover=auto_discover)
        
        # Attempt to restore from persistent state if available
        if restore_from_persistence:
            restoration_success = swarm_mind.restore_swarm_from_persistence()
            if restoration_success:
                record_log("🌱 Swarm Mind awakened from persistent memory")
            else:
                record_log("🌿 No persistent state found, starting fresh")
        
        # Start with minimal health check
        vitality = swarm_mind.pulse_swarm_vitality()
        if vitality.get("state") == "critical":
            record_log("🌬️ Swarm Mind awakened in critical state, applying recovery measures")
            swarm_mind._apply_recovery_measures()
        
        record_log(f"🌿 Swarm Mind successfully awakened with {vitality.get('active_fragments', 0)} active fragments")
        return swarm_mind
        
    except Exception as e:
        record_log(f"🌿 Swarm Mind could not awaken: {e}")
        record_log("🌱 Operating in single-node mode")
        return None

def create_resilient_swarm(node_capabilities: List[Dict[str, Any]]) -> Optional[SwarmMind]:
    """🌌 Creates a resilient swarm with predefined node capabilities."""
    try:
        swarm_mind = SwarmMind(auto_discover=False)
        
        # Add nodes with redundancy planning
        for i, capabilities in enumerate(node_capabilities):
            node_id = f"resilient-node-{i:03d}"
            success = swarm_mind.sprout_new_node(node_id, capabilities)
            if success:
                record_log(f"🌱 Resilient node {node_id} sprouted successfully")
        
        # Ensure redundancy for critical roles
        active_nodes = [n for n in swarm_mind.nodes.values() if n["status"] == "active"]
        if len(active_nodes) >= 3:
            # Create redundant aggregator if we have enough nodes
            powerful_nodes = [n for n in active_nodes 
                            if n["capabilities"].get("ram_gb", 0) >= 16]
            if len(powerful_nodes) >= 2:
                # Promote second-best node to backup aggregator
                backup_node = sorted(powerful_nodes, 
                                   key=lambda x: x["capabilities"].get("ram_gb", 0))[-2]
                backup_node["fragment_assignment"]["role"] = "aggregator"
                backup_node["fragment_assignment"]["is_backup"] = True
                
                record_log("🌌 Backup aggregator established for resilience")
        
        record_log(f"🌌 Resilient swarm created with {len(active_nodes)} nodes")
        return swarm_mind
        
    except Exception as e:
        record_log(f"🌿 Resilient swarm creation whispered error: {e}")
        return None

def test_swarm_intelligence(swarm_mind: SwarmMind, test_scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
    """🌸 Tests swarm intelligence across various scenarios."""
    try:
        test_results = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "scenarios_tested": len(test_scenarios),
            "results": [],
            "overall_performance": 0.0,
            "resilience_score": 0.0
        }
        
        scenario_scores = []
        
        for i, scenario in enumerate(test_scenarios):
            scenario_name = scenario.get("name", f"Scenario_{i+1}")
            test_state = SwarmState(**scenario.get("state", {}))
            
            # Test normal processing
            fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
            
            if fragment_outputs:
                wisdom = swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
                if wisdom:
                    confidence = wisdom.get("collective_confidence", 0.0)
                    consensus = wisdom.get("consensus_strength", 0.0)
                    scenario_score = (confidence + consensus) / 2.0
                    scenario_scores.append(scenario_score)
                    
                    test_results["results"].append({
                        "scenario": scenario_name,
                        "score": scenario_score,
                        "confidence": confidence,
                        "consensus": consensus,
                        "fragment_count": len(fragment_outputs),
                        "status": "success"
                    })
                else:
                    test_results["results"].append({
                        "scenario": scenario_name,
                        "score": 0.0,
                        "status": "wisdom_aggregation_failed"
                    })
            else:
                test_results["results"].append({
                    "scenario": scenario_name,
                    "score": 0.0,
                    "status": "no_fragment_response"
                })
        
        # Calculate overall performance
        if scenario_scores:
            test_results["overall_performance"] = sum(scenario_scores) / len(scenario_scores)
        
        # Test resilience by simulating node failure
        if len(swarm_mind.nodes) > 1:
            # Temporarily disable a node
            test_node_id = list(swarm_mind.nodes.keys())[0]
            original_status = swarm_mind.nodes[test_node_id]["status"]
            swarm_mind.nodes[test_node_id]["status"] = "suspended"
            
            # Test with reduced capacity
            degraded_outputs = swarm_mind.entwine_neural_pulse(test_state)
            if degraded_outputs:
                degraded_wisdom = swarm_mind.aggregate_swarm_wisdom(degraded_outputs)
                if degraded_wisdom and scenario_scores:
                    degraded_score = degraded_wisdom.get("collective_confidence", 0.0)
                    resilience_ratio = degraded_score / max(scenario_scores)
                    test_results["resilience_score"] = resilience_ratio
            
            # Restore node
            swarm_mind.nodes[test_node_id]["status"] = original_status
        
        record_log(f"🌸 Swarm intelligence tested: performance={test_results['overall_performance']:.3f}, resilience={test_results['resilience_score']:.3f}")
        
        return test_results
        
    except Exception as e:
        record_log(f"🌿 Swarm intelligence testing whispered error: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "scenarios_tested": 0,
            "overall_performance": 0.0
        }

# Export key classes and functions
__all__ = [
    "SwarmMind",
    "SwarmState", 
    "NeuralFragment",
    "MycelialFeedback",
    "SwarmHealthMonitor",
    "initialize_swarm_mind",
    "create_resilient_swarm",
    "test_swarm_intelligence",
    "enrich_with_garden_wisdom"
]