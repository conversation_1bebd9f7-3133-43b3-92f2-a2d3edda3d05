"""
Sacred Canopy - The distributed neural network layer
===================================================

The canopy contains the SwarmMind - a mycelial network of consciousness
that breathes across multiple nodes, sharing wisdom and resources
like nutrients flowing through underground fungal networks.

Phase 5.5.2 - Mycelial Feedback Loops:
- Continuous learning cycles between neural wisdom and memory gardens
- Breathing rhythm training for neural fragments
- Graceful fallbacks and error handling
"""

try:
    from .swarm_mind import (
        SwarmState,
        MycelialFeedback,
        SwarmHealthMonitor,
        SwarmMind
    )
    
    # Import the focused Phase 5.5.2 implementation
    from .mycelial_feedback import (
        MycelialFeedback as MycelialFeedbackV2,
        create_mycelial_feedback,
        test_mycelial_breathing
    )
    
    __all__ = [
        "SwarmState",
        "MycelialFeedback",
        "SwarmHealthMonitor", 
        "SwarmMind",
        "MycelialFeedbackV2",
        "create_mycelial_feedback",
        "test_mycelial_breathing"
    ]
except ImportError as e:
    # Graceful fallback if dependencies aren't available
    print(f"🌫️ Canopy modules not fully available: {e}")
    __all__ = []