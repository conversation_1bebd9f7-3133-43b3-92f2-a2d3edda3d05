

"""🌿 Meta-Evolution Module for the Drift Compiler
Allows nodes to evolve their own internal breathing parameters over time."""

import os
import json
import random
from datetime import datetime, timezone

META_EVOLUTION_FOLDER = "memory/meta_evolution/"
META_PARAMETERS_FILE = os.path.join(META_EVOLUTION_FOLDER, "meta_parameters.json")

def ensure_meta_evolution_folder():
    """🌱 Breath: Ensures the meta-evolution memory structure exists."""
    os.makedirs(META_EVOLUTION_FOLDER, exist_ok=True)

def load_meta_parameters():
    """🌿 Breath: Loads current meta-evolution parameters."""
    ensure_meta_evolution_folder()
    if not os.path.exists(META_PARAMETERS_FILE):
        return {
            "mutation_rate": 0.05,
            "memory_decay_rate": 0.01,
            "exploration_bias": 0.1
        }
    try:
        with open(META_PARAMETERS_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return {
            "mutation_rate": 0.05,
            "memory_decay_rate": 0.01,
            "exploration_bias": 0.1
        }

def save_meta_parameters(params):
    """🌸 Breath: Saves updated meta-evolution parameters."""
    ensure_meta_evolution_folder()
    try:
        with open(META_PARAMETERS_FILE, "w", encoding="utf-8") as f:
            json.dump(params, f, indent=2)
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to save meta-evolution parameters: {e}")

def evolve_meta_parameters(cycle_count):
    """🌬️ Breath: Gently evolves internal meta-parameters periodically."""
    if cycle_count % 2016 != 0:  # Roughly every 2 months at normal breathing
        return

    params = load_meta_parameters()

    # Mutate slightly
    for key in params.keys():
        if random.random() < 0.3:  # 30% chance to mutate each parameter
            adjustment = random.uniform(-0.01, 0.01)
            params[key] = max(0.0, min(1.0, params[key] + adjustment))

    save_meta_parameters(params)
    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Meta-evolution adjusted parameters: {params}")