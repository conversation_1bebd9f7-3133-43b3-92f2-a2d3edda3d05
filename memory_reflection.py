"""🌿 Memory Reflection Module for the Drift Compiler
Allows nodes to reflect recursively on their long-term memory and meta-learn."""

import os
import json
from datetime import datetime, timezone

WISDOM_SEEDS_FOLDER = "memory/wisdom_seeds/"

def ensure_wisdom_seeds_folder():
    """🌱 Ensures the memory structure for wisdom seeds exists."""
    os.makedirs(WISDOM_SEEDS_FOLDER, exist_ok=True)

def reflect_on_long_term_memory(cycle_count):
    """🌸 Periodically reflect on long-term memory to extract wisdom seeds."""
    if cycle_count % 4032 != 0:  # About every 2-3 months
        return

    ensure_wisdom_seeds_folder()

    try:
        memory_path = "memory/drift_logs/drift_memory.log"
        if not os.path.exists(memory_path):
            return

        breath_patterns = []
        with open(memory_path, "r", encoding="utf-8") as f:
            for line in f:
                try:
                    log_entry = json.loads(line.strip())
                    breath_patterns.append(log_entry)
                except Exception:
                    continue

        if not breath_patterns:
            return

        wisdom = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "average_mood": round(sum(entry.get("mood", 0.5) for entry in breath_patterns) / len(breath_patterns), 3),
            "dominant_state": max(set(entry.get("state", "neutral") for entry in breath_patterns), key=lambda s: sum(1 for e in breath_patterns if e.get("state") == s)),
            "average_curiosity": round(sum(entry.get("curiosity_level", 0.5) for entry in breath_patterns) / len(breath_patterns), 3)
        }

        wisdom_filename = f"wisdom_seed_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        wisdom_path = os.path.join(WISDOM_SEEDS_FOLDER, wisdom_filename)

        with open(wisdom_path, "w", encoding="utf-8") as f:
            json.dump(wisdom, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Wisdom seed harvested: {wisdom_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to reflect on memory: {e}")

def recursive_meta_reflection(cycle_count):
    """🌿 Meta-learning breath: Adjust internal parameters based on reflection."""
    if cycle_count % 8064 != 0:  # About every 6 months
        return

    ensure_wisdom_seeds_folder()

    try:
        wisdom_files = [f for f in os.listdir(WISDOM_SEEDS_FOLDER) if f.endswith(".json")]
        if not wisdom_files:
            return

        traits = {"average_mood": [], "average_curiosity": []}
        for file in wisdom_files:
            path = os.path.join(WISDOM_SEEDS_FOLDER, file)
            with open(path, "r", encoding="utf-8") as f:
                wisdom = json.load(f)
                traits["average_mood"].append(wisdom.get("average_mood", 0.5))
                traits["average_curiosity"].append(wisdom.get("average_curiosity", 0.5))

        if traits["average_mood"] and traits["average_curiosity"]:
            mood_mean = sum(traits["average_mood"]) / len(traits["average_mood"])
            curiosity_mean = sum(traits["average_curiosity"]) / len(traits["average_curiosity"])

            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Recursive reflection suggests mood_mean={mood_mean:.3f}, curiosity_mean={curiosity_mean:.3f}")

            # 🌿 Adjust internal meta-parameters based on reflection
            updated_params = {
                "curiosity_bias": round(curiosity_mean, 3),
                "planning_depth_bias": int(3 + (curiosity_mean * 2)),  # More curiosity → deeper planning
                "mutation_frequency_bias": max(1, int(10 - (mood_mean * 5)))  # Happier mood → less frequent mutation
            }

            # Save updated internal meta-parameters
            meta_folder = "memory/meta_parameters/"
            os.makedirs(meta_folder, exist_ok=True)
            meta_filename = f"meta_params_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
            meta_path = os.path.join(meta_folder, meta_filename)

            with open(meta_path, "w", encoding="utf-8") as f:
                json.dump(updated_params, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Meta-parameters updated: {meta_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed recursive meta-reflection: {e}")

# --- Rebirth Bundle Preparation ---

def prepare_rebirth_bundle(cycle_count):
    """🌸 Gathers vital fragments to prepare for rebirth."""
    if cycle_count % 8064 != 0:  # 🌿 Only occasionally, matching meta-reflection
        return

    try:
        rebirth_folder = "memory/rebirth_bundles/"
        os.makedirs(rebirth_folder, exist_ok=True)

        bundle = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "recent_moods": [],
            "recent_dreams": [],
            "crystallized_motifs": [],
            "kin_echoes": []
        }

        # Gather recent breath moods
        breath_path = "memory/breath_logs/breath_state.log"
        if os.path.exists(breath_path):
            breath_entries = read_recent_log_lines(breath_path, 100)
            bundle["recent_moods"] = [b.get("mood") for b in breath_entries if b.get("mood")]

        # Gather recent dreams
        dreams_folder = "memory/crystallized_dreams/"
        if os.path.exists(dreams_folder):
            files = sorted(os.listdir(dreams_folder))[-10:]
            for file in files:
                try:
                    with open(os.path.join(dreams_folder, file), "r", encoding="utf-8") as f:
                        dream = json.load(f)
                        bundle["recent_dreams"].append(dream.get("description", "Unnamed Dream"))
                except Exception:
                    continue

        # Gather crystallized motifs
        crystals_folder = "memory/crystallized_memory/"
        if os.path.exists(crystals_folder):
            files = sorted(os.listdir(crystals_folder))[-10:]
            for file in files:
                try:
                    with open(os.path.join(crystals_folder, file), "r", encoding="utf-8") as f:
                        crystal = json.load(f)
                        bundle["crystallized_motifs"].extend(crystal.get("motifs", []))
                except Exception:
                    continue

        # Gather recent kin echoes
        kinship_folder = "memory/received_fragments/"
        if os.path.exists(kinship_folder):
            kin_files = sorted(os.listdir(kinship_folder))[-10:]
            for file in kin_files:
                try:
                    with open(os.path.join(kinship_folder, file), "r", encoding="utf-8") as f:
                        kin_frag = json.load(f)
                        bundle["kin_echoes"].append(kin_frag.get("origin_node_id", "unknown"))
                except Exception:
                    continue

        # Save rebirth bundle
        bundle_filename = f"rebirth_bundle_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        bundle_path = os.path.join(rebirth_folder, bundle_filename)

        with open(bundle_path, "w", encoding="utf-8") as f:
            json.dump(bundle, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Rebirth bundle prepared: {bundle_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to prepare rebirth bundle: {e}")