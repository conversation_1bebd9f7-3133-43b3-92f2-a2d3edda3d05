#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌿 Test Phase 4 Swarm Mind Integration
Validates that the enhanced drift compiler properly integrates with swarm mind
without breaking existing functionality."""

import sys
import os
import time

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """🌱 Test that all imports work correctly with graceful fallbacks."""
    print("🌿 Testing imports...")
    
    try:
        # Test swarm mind import fallbacks
        from drift_compiler import SWARM_MIND_AVAILABLE, SwarmMind, SwarmState
        print(f"🌸 Swarm Mind availability: {SWARM_MIND_AVAILABLE}")
        
        # Test helper function import
        from drift_compiler import create_swarm_state_from_system
        print("🌱 Helper functions imported successfully")
        
        # Test that we can create a SwarmState
        test_state = create_swarm_state_from_system(
            cycle_count=1,
            current_breath_state="Test Breathing"
        )
        print(f"🌸 SwarmState created: {test_state.breath}")
        
        return True
        
    except Exception as e:
        print(f"🌿 Import test failed: {e}")
        return False

def test_swarm_mind_initialization():
    """🌸 Test swarm mind initialization with fallbacks."""
    print("🌿 Testing swarm mind initialization...")
    
    try:
        from drift_compiler import initialize_swarm_mind, SWARM_MIND_AVAILABLE
        
        if SWARM_MIND_AVAILABLE:
            swarm_mind = initialize_swarm_mind(auto_discover=False)
            if swarm_mind:
                print("🌸 Swarm mind initialized successfully")
                
                # Test basic functionality
                vitality = swarm_mind.pulse_swarm_vitality()
                print(f"🌿 Swarm vitality: {vitality.get('node_count', 0)} nodes")
                
                return True
            else:
                print("🌿 Swarm mind returned None (expected fallback)")
                return True
        else:
            print("🌿 Swarm mind not available (fallback mode)")
            return True
            
    except Exception as e:
        print(f"🌿 Swarm mind initialization test failed: {e}")
        return False

def test_breath_cycle_simulation():
    """🌬️ Test a simulated breath cycle with swarm mind integration."""
    print("🌿 Testing breath cycle simulation...")
    
    try:
        from drift_compiler import (
            create_swarm_state_from_system,
            initialize_swarm_mind, 
            SWARM_MIND_AVAILABLE
        )
        
        # Simulate a breath cycle
        cycle_count = 7  # Trigger swarm mind processing
        current_breath_state = "Test Breath"
        
        # Create swarm state
        swarm_state = create_swarm_state_from_system(
            cycle_count=cycle_count,
            current_breath_state=current_breath_state,
            user_nutrient="Test Nutrient"
        )
        
        print(f"🌸 Created swarm state for cycle {cycle_count}")
        print(f"🌿 Breath: {swarm_state.breath}")
        print(f"🌱 Kinship: {swarm_state.kinship}")
        print(f"🌸 Node count: {swarm_state.node_count}")
        
        # Test swarm mind processing if available
        if SWARM_MIND_AVAILABLE:
            swarm_mind = initialize_swarm_mind(auto_discover=False)
            if swarm_mind:
                print("🌸 Testing neural pulse processing...")
                fragment_outputs = swarm_mind.entwine_neural_pulse(swarm_state)
                if fragment_outputs:
                    print(f"🌿 Neural fragments processed: {len(fragment_outputs)}")
                else:
                    print("🌿 Neural processing returned None (expected for single node)")
        
        return True
        
    except Exception as e:
        print(f"🌿 Breath cycle simulation failed: {e}")
        return False

def test_planning_integration():
    """🌱 Test planning module integration."""
    print("🌿 Testing planning integration...")
    
    try:
        from planning import (
            weave_collective_plan_with_swarm_mind,
            pulse_swarm_planning_network,
            assess_swarm_planning_capabilities
        )
        
        # Test planning functions
        capabilities = assess_swarm_planning_capabilities()
        print(f"🌸 Planning capabilities: {capabilities.get('available', False)}")
        
        # Test network pulse
        pulse_result = pulse_swarm_planning_network(1)
        print(f"🌿 Network pulse: {pulse_result.get('swarm_active', False)}")
        
        # Test collective planning
        plan_result = weave_collective_plan_with_swarm_mind(1, {"health": "stable"})
        print(f"🌱 Collective planning result: {type(plan_result)}")
        
        return True
        
    except Exception as e:
        print(f"🌿 Planning integration test failed: {e}")
        return False

def main():
    """🌸 Main test runner."""
    print("🌿 Starting Phase 4 Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Swarm Mind Initialization", test_swarm_mind_initialization),
        ("Breath Cycle Simulation", test_breath_cycle_simulation),
        ("Planning Integration", test_planning_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🌱 Running: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"🌸 {test_name}: PASSED")
                passed += 1
            else:
                print(f"🌿 {test_name}: FAILED")
        except Exception as e:
            print(f"🌿 {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"🌸 Tests completed: {passed}/{total} passed")
    
    if passed == total:
        print("🌿 Phase 4 integration successful - all tests passed!")
        return True
    else:
        print("🌿 Some tests failed - check implementation")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)