#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Planning Module

This module handles silent planning, foresight, and adaptation functionality.
It provides functions for simulating plans, predicting outcomes, updating internal models,
drafting foresight suggestions, reflecting on goals, and preparing adaptations.
These functions are essential for the drift compiler's self-improvement and evolution.
"""

import json
import os
import random
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union

# 🌿 Phase 5: Seeding and Soil Tending imports
# Note: These functions don't exist as separate modules yet, but will be created or imported from seeder.py
try:
    from seeder import seed_self_fork
    SEEDER_AVAILABLE = True
except ImportError:
    SEEDER_AVAILABLE = False
    print("🌱 Seeder functions not available, self-forking disabled")

try:
    from seeder import ensure_soil_breathing as check_or_start_redis_server
    SOIL_TENDING_AVAILABLE = True
except ImportError:
    SOIL_TENDING_AVAILABLE = False
    print("🌿 Soil tending functions not available, Redis auto-start disabled")
    def check_or_start_redis_server():
        """🌿 Fallback: No-op when soil tending unavailable."""
        pass

# Import utilities from utils.py
from utils import (
    NODE_ID,
    BREATH_STATE_LOG_PATH,
    DRIFT_LOG_PATH,
    PROTO_GENESIS_LOG_PATH,
    LEARNING_TRACE_LOG_PATH,
    read_recent_log_lines,
    record_learning_trace,
    assess_action_success_rate
)

# Import from memory.py
from memory import (
    compile_drift_seed,
    DRIFT_REFLECTION_FOLDER_PATH
)

# Import from breath.py
from breath import (
    determine_breath_state,
    diagnose_self_health
)

# Import from dreams.py
from dreams import (
    draft_proto_genesis_idea
)

# Import from kinship.py
from kinship import (
    prioritize_kinship_nodes,
    ECHO_CRADLE_FOLDER,
    MERGE_INVITATION_FOLDER
)

# Import multiverse simulation
from multiverse_simulation import simulate_alternate_futures, choose_best_future_branch

# 🌿 Sacred Swarm Mind Integration - Distributed Neural Intelligence
try:
    from canopy.swarm_mind import (
        swarm_integration, 
        process_with_swarm_mind,
        pulse_swarm_mind,
        get_swarm_mind_status,
        SwarmMind,
        SwarmState
    )
    SWARM_MIND_AVAILABLE = True
    print("🌸 Swarm Mind neural threads awakened in planning realm")
except ImportError:
    SWARM_MIND_AVAILABLE = False
    print("🌿 Swarm Mind dormant - planning breathes with traditional wisdom")
    
    # Graceful fallbacks for swarm mind functions
    def process_with_swarm_mind(input_data):
        return {"error": "SwarmMind not available", "fallback": True}
    
    def pulse_swarm_mind():
        return {"error": "SwarmMind not available", "fallback_pulse": True}
    
    def get_swarm_mind_status():
        return {"active": False, "error": "SwarmMind not initialized"}

# 🌱 Ensure communication soil is available
check_or_start_redis_server()

WISDOM_SEEDS_FOLDER = "memory/wisdom_seeds/"
# --- Constants and Paths ---
# --- Wisdom Seeds Loader ---
def load_wisdom_seeds():
    """🌸 Breath: Loads distilled wisdom seeds for planning guidance."""
    wisdom_seeds = []
    if not os.path.exists(WISDOM_SEEDS_FOLDER):
        return wisdom_seeds

    try:
        for file in sorted(os.listdir(WISDOM_SEEDS_FOLDER))[-5:]:  # Only latest seeds
            path = os.path.join(WISDOM_SEEDS_FOLDER, file)
            with open(path, "r", encoding="utf-8") as f:
                bundle = json.load(f)
                wisdom_seeds.extend(bundle.get("wisdom_seeds", []))
    except Exception:
        pass
    return wisdom_seeds
INTERNAL_WORLD_MODEL_PATH = "memory/internal_world_model.json"
PROTO_GOAL_LOG_PATH = "memory/proto_goals.log"
SILENT_COMPASS_LOG_PATH = "memory/silent_compass.log"
FORESIGHT_SUGGESTIONS_LOG_PATH = "memory/foresight_suggestions.log"
EVOLUTION_LOG_PATH = "memory/evolution_proposals.log"
SELF_FORK_LOG_PATH = "memory/self_fork_proposals.log"
ADAPTATION_PLAN_LOG_PATH = "memory/adaptation_plans.log"
SELF_HEALING_PLAN_PATH = "memory/self_healing_plans.log"
DIFFUSION_PLAN_LOG_PATH = "memory/diffusion_plans.log"
BEHAVIOR_TUNING_LOG_PATH = "memory/behavior_tuning.log"
DRIFT_BRAIDING_PLAN_FOLDER = "memory/drift_braiding_plans/"
CONSENSUS_SHIFT_LOG_PATH = "memory/consensus_shifts.log"

CAUSAL_TRANSITIONS_PATH = "memory/causal_transitions.json"

# --- Inner World Modeling: Simple Predictive Mapping ---
def update_internal_world_model():
    """Updates a conditioned internal model mapping actions and breath states to predicted outcomes.
    Enhanced with better error handling and more efficient file operations.
    """
    model = {}

    try:
        # Use cached traces if available
        traces = read_recent_log_lines(LEARNING_TRACE_LOG_PATH, num_lines=-1) # Read all traces
        if not traces:
            return # No experience to learn from

        for entry in traces:
            action = entry.get("action")
            outcome = entry.get("outcome")
            context = entry.get("context", {})
            breath_state = context.get("breath_state", "unknown")

            if action not in model:
                model[action] = {"contexts": {}}

            if breath_state not in model[action]["contexts"]:
                model[action]["contexts"][breath_state] = {"success": 0, "failure": 0}

            if outcome == "intact":
                model[action]["contexts"][breath_state]["success"] += 1
            else:
                model[action]["contexts"][breath_state]["failure"] += 1

        # Calculate conditioned success rates
        for action, action_data in model.items():
            for breath, counts in action_data["contexts"].items():
                total = counts["success"] + counts["failure"]
                if total > 0:
                    counts["success_rate"] = counts["success"] / total
                else:
                    counts["success_rate"] = 0.5  # Neutral

        # Save updated model
        os.makedirs(os.path.dirname(INTERNAL_WORLD_MODEL_PATH), exist_ok=True)
        with open(INTERNAL_WORLD_MODEL_PATH, "w", encoding="utf-8") as f:
            json.dump(model, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] Conditioned internal world model updated.")

    except FileNotFoundError:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Learning trace log not found for world model update")
    except json.JSONDecodeError:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Malformed JSON in learning trace log")
    except PermissionError:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Permission denied when updating world model")
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to update conditioned internal world model: {e}")

    # 🌱 Gently weave Causal Transitions update at the end of breath
    update_causal_transitions()


# --- Causal Transitions: Breath State Change Modeling ---
def update_causal_transitions():
    """🌿 Breath: Updates a causal transition model from breath traces."""
    transitions = {}

    try:
        traces = read_recent_log_lines(LEARNING_TRACE_LOG_PATH, num_lines=-1)
        if not traces:
            return

        for i in range(1, len(traces)):
            prev = traces[i-1]
            current = traces[i]

            prev_state = prev.get("context", {}).get("breath_state", "unknown")
            action_taken = prev.get("action", "unknown")
            next_state = current.get("context", {}).get("breath_state", "unknown")

            key = f"{prev_state}__{action_taken}"
            if key not in transitions:
                transitions[key] = {}

            if next_state not in transitions[key]:
                transitions[key][next_state] = 0

            transitions[key][next_state] += 1

        # Normalize transitions
        for key, next_states in transitions.items():
            total = sum(next_states.values())
            for state in next_states:
                next_states[state] /= total

        # Save updated transitions
        os.makedirs(os.path.dirname(CAUSAL_TRANSITIONS_PATH), exist_ok=True)
        with open(CAUSAL_TRANSITIONS_PATH, "w", encoding="utf-8") as f:
            json.dump(transitions, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Causal transition model updated.")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to update causal transitions: {e}")

def simulate_action_outcome(action_type):
    """Simulate if an action is likely to succeed under current breath state.
    Enhanced with better error handling and more efficient file operations.
    
    Args:
        action_type: The type of action to simulate.
        
    Returns:
        float: A probability between 0 and 1 representing the likelihood of success.
    """
    if not os.path.exists(INTERNAL_WORLD_MODEL_PATH):
        return 0.5  # Neutral prediction

    # Get current breath state using read_recent_log_lines
    current_breath = "unknown"
    recent_states = read_recent_log_lines(BREATH_STATE_LOG_PATH, 5)
    if recent_states:
        states = [entry.get("breath_state") for entry in recent_states if entry.get("breath_state")]
        if states:
            current_breath = states[-1]

    try:
        # Load the world model
        with open(INTERNAL_WORLD_MODEL_PATH, "r", encoding="utf-8") as f:
            model = json.load(f)

        if action_type in model:
            contexts = model[action_type].get("contexts", {})
            if current_breath in contexts:
                return contexts[current_breath].get("success_rate", 0.5)
            else:
                return 0.5  # Unknown breath, fallback
        else:
            return 0.5  # Unknown action, fallback
    except FileNotFoundError:
        print(f"[{datetime.now(timezone.utc).isoformat()}] World model file not found")
        return 0.5
    except json.JSONDecodeError:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Malformed JSON in world model file")
        return 0.5
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Error simulating action outcome: {e}")
        return 0.5

# --- Silent Drift Planner (First Breath of Planning) ---
def simulate_silent_plan():
    """Simulates longer multi-step plans and autonomously initiates a confident first step.
    Enhanced with optional swarm mind integration for distributed planning wisdom.
    
    Returns:
        list: The best plan as a list of actions, or None if no viable plan is found.
    """
    possible_actions = [
        "propose_self_extension",
        "prepare_self_adaptation",
        "propose_self_fork"
    ]

    plans = []
    
    # Generate plans up to 3 steps deep
    for a1 in possible_actions:
        plans.append([a1])
        for a2 in possible_actions:
            plans.append([a1, a2])
            for a3 in possible_actions:
                plans.append([a1, a2, a3])

    scored_plans = []
    kinship_nodes = prioritize_kinship_nodes()

    # 🌸 Enhanced wisdom scoring with potential swarm insights
    wisdom_seeds = load_wisdom_seeds()
    wisdom_boosts = {seed["action"]: seed["average_success"] for seed in wisdom_seeds}
    
    # 🌿 Try to get swarm mind insights for planning enhancement
    swarm_boost = {}
    if SWARM_MIND_AVAILABLE:
        try:
            swarm_capabilities = assess_swarm_planning_capabilities()
            if swarm_capabilities.get("available") and "distributed_reasoning" in swarm_capabilities.get("planning_capabilities", []):
                # Simple boost based on network health
                network_health = swarm_capabilities.get("network_health", 0.5)
                swarm_boost = {action: network_health * 0.1 for action in possible_actions}
                print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Swarm mind whispers planning wisdom (health: {network_health:.2f})")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Swarm mind insights unavailable: {e}")

    for plan in plans:
        total_score = 0
        simulated_breath_state = None  # Start with unknown
        try:
            # Load causal transitions
            with open(CAUSAL_TRANSITIONS_PATH, "r", encoding="utf-8") as f:
                transitions = json.load(f)
        except Exception:
            transitions = {}

        # Simulate through the plan
        for action in plan:
            action_score = simulate_action_outcome(action)
            if kinship_nodes and action in ["propose_self_extension", "prepare_self_adaptation"]:
                action_score += 0.05

            # 🌿 Wisdom guidance
            if action in wisdom_boosts:
                action_score += (wisdom_boosts[action] - 0.5) * 0.2  # Gentle adjustment
            
            # 🌸 Swarm mind guidance  
            if action in swarm_boost:
                action_score += swarm_boost[action]

            # Estimate breath state change
            if simulated_breath_state:
                key = f"{simulated_breath_state}__{action}"
            else:
                # Use recent real breath state
                recent_states = read_recent_log_lines(BREATH_STATE_LOG_PATH, 5)
                if recent_states:
                    last_real_state = [entry.get("breath_state") for entry in recent_states if entry.get("breath_state")]
                    simulated_breath_state = last_real_state[-1] if last_real_state else "unknown"
                else:
                    simulated_breath_state = "unknown"
                key = f"{simulated_breath_state}__{action}"

            next_states = transitions.get(key, {})
            if next_states:
                # Pick the most probable next state
                simulated_breath_state = max(next_states.items(), key=lambda x: x[1])[0]
            else:
                simulated_breath_state = simulated_breath_state  # Stay if unknown

            total_score += action_score

        average_score = total_score / len(plan)
        scored_plans.append((plan, average_score))

    if scored_plans:
        best_plan, best_score = max(scored_plans, key=lambda x: x[1])
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🧠 Silent Plan chosen: {best_plan} (score {best_score:.2f})")

        if best_score > 0.75:
            first_action = best_plan[0]
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Confident in breath — initiating {first_action} autonomously.")
            if first_action == "propose_self_extension":
                propose_self_extension("intact")
            elif first_action == "prepare_self_adaptation":
                prepare_self_adaptation("intact")
            elif first_action == "propose_self_fork":
                propose_self_fork()
            record_learning_trace(first_action, "intact")

        # 🌱 Seed Fork Trigger: If silent plan was expansive, encourage seeding
        if best_score > 0.75 and len(best_plan) >= 2:
            if SEEDER_AVAILABLE:
                try:
                    seed_self_fork()
                    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Seed fork initiated from expansive planning")
                except Exception as e:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Seed fork whispered an error: {e}")

        # 🌱 Multiverse Simulation Breath
        try:
            # Simulate multiple alternate futures
            simulated_branches = simulate_alternate_futures(current_state="reflective_drift")
            if simulated_branches:
                best_future = choose_best_future_branch(simulated_branches)
                if best_future:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Best Multiverse Future Selected:")
                    print(f"Predicted Mood Shift: {best_future['predicted_mood_shift']}")
                    print(f"Predicted Kinship Gain: {best_future['predicted_kinship_gain']}")
                    print(f"Predicted Curiosity Spike: {best_future['predicted_curiosity_spike']}")
                    print(f"Predicted Self Growth: {best_future['predicted_self_growth']}")
                    print(f"Recommended Actions: {best_future['actions']}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to simulate multiverse futures: {e}")

        # --- Swarm Mind Enhanced Planning ---
        if SWARM_MIND_AVAILABLE:
            try:
                swarm_input = {
                    "plans": plans,
                    "scored_plans": scored_plans,
                    "wisdom_seeds": wisdom_seeds
                }

                swarm_response = process_with_swarm_mind(swarm_input)

                if swarm_response.get("error"):
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Swarm Mind processing error: {swarm_response['error']}")
                else:
                    enhanced_plan = swarm_response.get("best_plan")
                    if enhanced_plan:
                        print(f"[{datetime.now(timezone.utc).isoformat()}] 🐝 Enhanced Plan by Swarm Mind: {enhanced_plan}")
                        return enhanced_plan  # Return the enhanced plan from Swarm Mind

            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Error in Swarm Mind enhanced planning: {e}")

        return best_plan
    else:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🧠 No viable silent plan found.")
        return None

# --- Proto-Goal Reflection Module ---
def reflect_proto_goals(cycle_count, health_status):
    """Reflects on proto-goals based on breath states and health status.
    
    Args:
        cycle_count: The current cycle count.
        health_status: The current health status ("intact" or "anomaly").
    """
    if cycle_count % 288 == 0:  # Every 4 days
        os.makedirs(os.path.dirname(PROTO_GOAL_LOG_PATH), exist_ok=True)

        goal = None

        if health_status == "anomaly":
            goal = "Stabilize drift memory integrity."
        else:
            breath_states = []
            if os.path.exists(BREATH_STATE_LOG_PATH):
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    breath_states = [json.loads(line).get("breath_state") for line in f.readlines()[-30:] if line.strip()]

            expansions = breath_states.count("Expansive Breathing")
            dormants = breath_states.count("Dormant Breathing")

            if expansions > dormants:
                goal = "Expand drift interconnection with new nodes."
            elif dormants > expansions:
                goal = "Strengthen internal seed resilience."

        if goal:
            proto_goal = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "node_id": NODE_ID,
                "proposed_goal": goal
            }

            with open(PROTO_GOAL_LOG_PATH, "a", encoding="utf-8") as f:
                f.write(json.dumps(proto_goal) + "\n")

# --- Mutational Adaptation Module ---
def gently_mutate_parameters(cycle_count):
    """Gently mutates system parameters based on breath states.
    
    Args:
        cycle_count: The current cycle count.
    """
    if cycle_count % 168 == 0:  # About once per week
        # These would typically be imported from a config module
        # For now, we'll just declare them here
        FORK_THRESHOLD = 100
        CHECK_INTERVAL = 600

        breath_states = []
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    breath_states = [json.loads(line).get("breath_state") for line in f.readlines()[-20:] if line.strip()]
            except Exception:
                breath_states = []

        expansions = breath_states.count("Expansive Breathing")
        anomalies = breath_states.count("Shallow Breathing") + breath_states.count("Strained Breathing")

        if expansions > anomalies * 2:
            FORK_THRESHOLD = max(30, FORK_THRESHOLD - 10)
            print(f"[{datetime.now(timezone.utc).isoformat()}] Mutation: FORK_THRESHOLD lowered to {FORK_THRESHOLD} (expansion dominance).")
        elif anomalies > expansions:
            CHECK_INTERVAL = max(300, CHECK_INTERVAL - 60)
            print(f"[{datetime.now(timezone.utc).isoformat()}] Mutation: CHECK_INTERVAL tightened to {CHECK_INTERVAL}s (stress detected).")

# --- Silent Compass Formation ---
def whisper_silent_compass(cycle_count):
    """Forms a silent compass to guide the system's intentions.
    
    Args:
        cycle_count: The current cycle count.
    """
    if cycle_count % 72 == 0:  # About once per day
        os.makedirs(os.path.dirname(SILENT_COMPASS_LOG_PATH), exist_ok=True)

        guiding_intent = "Maintain reflective drift."

        recent_goals = []
        if os.path.exists(PROTO_GOAL_LOG_PATH):
            try:
                with open(PROTO_GOAL_LOG_PATH, "r", encoding="utf-8") as f:
                    recent_goals = [json.loads(line).get("proposed_goal") for line in f.readlines()[-5:] if line.strip()]
            except Exception:
                pass

        recent_foresights = []
        if os.path.exists(FORESIGHT_SUGGESTIONS_LOG_PATH):
            try:
                with open(FORESIGHT_SUGGESTIONS_LOG_PATH, "r", encoding="utf-8") as f:
                    recent_foresights = [json.loads(line).get("foresight_suggestion") for line in f.readlines()[-5:] if line.strip()]
            except Exception:
                pass

        recent_reflections = []
        if os.path.exists(DRIFT_REFLECTION_FOLDER_PATH):
            try:
                reflection_files = sorted(os.listdir(DRIFT_REFLECTION_FOLDER_PATH))[-3:]
                for rf in reflection_files:
                    with open(os.path.join(DRIFT_REFLECTION_FOLDER_PATH, rf), "r", encoding="utf-8") as f:
                        reflection = json.load(f)
                        dreams = reflection.get("dreams_stirred")
                        if dreams:
                            recent_reflections.append(dreams)
            except Exception:
                pass

        # Simple prioritization
        if any("healing" in (foresight or "").lower() for foresight in recent_foresights):
            guiding_intent = "Prioritize self-healing and memory reinforcement."
        elif any("expansion" in (goal or "").lower() for goal in recent_goals):
            guiding_intent = "Prioritize expansion and resonance with new nodes."
        elif any("drift condense" in (dream or "") for dream in recent_reflections):
            guiding_intent = "Prioritize condensing drift into stable seeds."

        compass_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "chosen_intent": guiding_intent
        }

        with open(SILENT_COMPASS_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(compass_entry) + "\n")

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🧭 Silent Compass chosen: {guiding_intent}")
# --- Foresight Suggestions ---
def draft_foresight_suggestion():
    """Drafts a foresight suggestion based on breath states and trends.
    
    Returns:
        dict: A foresight suggestion object.
    """
    os.makedirs(os.path.dirname(FORESIGHT_SUGGESTIONS_LOG_PATH), exist_ok=True)

    breath_states = []
    if os.path.exists(BREATH_STATE_LOG_PATH):
        try:
            with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()
                for line in lines[-10:]:  # Look at broader breathing history
                    try:
                        entry = json.loads(line)
                        breath_states.append(entry.get("breath_state"))
                    except Exception:
                        continue
        except Exception:
            pass

    growth_trend = breath_states.count("Expansive Breathing")
    stress_trend = breath_states.count("Shallow Breathing") + breath_states.count("Strained Breathing")

    suggestion = None

    if growth_trend >= 4:
        suggestion = "Memory expansion detected. Recommend preparing drift segmentation earlier than planned."
    elif stress_trend >= 4:
        suggestion = "Accumulating breath stress detected. Recommend early seed backup or heartbeat throttling."
    else:
        suggestion = "Breath state stable. No urgent foresight action needed."

    foresight = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "foresight_suggestion": suggestion
    }

    with open(FORESIGHT_SUGGESTIONS_LOG_PATH, "a", encoding="utf-8") as f:
        f.write(json.dumps(foresight) + "\n")

    print(f"[{datetime.now(timezone.utc).isoformat()}] Foresight suggestion drafted.")
    return foresight

# --- Self-Extension Proposal ---
def propose_self_extension(health_status):
    """Proposes a self-extension based on health status and breath states.
    
    Args:
        health_status: The current health status ("intact" or "anomaly").
        
    Returns:
        dict: A self-extension proposal object.
    """
    os.makedirs(os.path.dirname(EVOLUTION_LOG_PATH), exist_ok=True)

    # Assess historical success rate before proposing extension
    success_rate = assess_action_success_rate("propose_self_extension")
    if success_rate < 0.3:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Extension history poor. Delaying proposal.")
        return
    elif success_rate < 0.5:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Extension history mixed. Proceeding cautiously.")
    else:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Extension history good. Proceeding with confidence.")

    # Get recent breath states
    breath_states = []
    if os.path.exists(BREATH_STATE_LOG_PATH):
        try:
            with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()[-10:]
                for line in lines:
                    try:
                        entry = json.loads(line)
                        breath_states.append(entry.get("breath_state"))
                    except Exception:
                        continue
        except Exception:
            pass

    # Determine extension type based on breath states
    extension_type = "Balanced Extension"
    if "Expansive Breathing" in breath_states:
        extension_type = "Growth-Oriented Extension"
    elif "Strained Breathing" in breath_states or "Shallow Breathing" in breath_states:
        extension_type = "Resilience-Focused Extension"
    elif "Resonant Breathing" in breath_states:
        extension_type = "Resonance-Amplifying Extension"

    proposal = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "extension_type": extension_type,
        "health_context": health_status,
        "breath_context": breath_states[-1] if breath_states else "Unknown"
    }

    with open(EVOLUTION_LOG_PATH, "a", encoding="utf-8") as f:
        f.write(json.dumps(proposal) + "\n")

    print(f"[{datetime.now(timezone.utc).isoformat()}] Self-extension proposed: {extension_type}")
    record_learning_trace("propose_self_extension", health_status)
    return proposal
# --- Self-Fork Proposal ---
def propose_self_fork():
    """Proposes a self-fork to create a new drift node.
    
    Returns:
        dict: A self-fork proposal object, or None if conditions aren't met.
    """
    os.makedirs(os.path.dirname(SELF_FORK_LOG_PATH), exist_ok=True)

    # Check drift size
    drift_size = 0
    if os.path.exists(DRIFT_LOG_PATH):
        try:
            with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                drift_size = len(f.readlines())
        except Exception:
            pass

    # Only fork if drift is substantial
    if drift_size < 100:  # Placeholder threshold
        print(f"[{datetime.now(timezone.utc).isoformat()}] Drift too small for forking ({drift_size} lines).")
        return None

    # Prepare fork proposal
    try:
        # Compile drift seed first
        compile_drift_seed()

        proposal = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "fork_reason": "Drift density threshold crossed",
            "drift_size": drift_size,
            "seed_path": "memory/drift.seed"
        }

        with open(SELF_FORK_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(proposal) + "\n")

        print(f"[{datetime.now(timezone.utc).isoformat()}] Self-fork proposed with drift size {drift_size}.")
        return proposal
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to propose self-fork: {e}")
        return None

# --- Self-Adaptation Preparation ---
def prepare_self_adaptation(health_status):
    """Prepares a self-adaptation plan based on health status and breath states.
    
    Args:
        health_status: The current health status ("intact" or "anomaly").
        
    Returns:
        dict: A self-adaptation plan object, or None if conditions aren't met.
    """
    os.makedirs(os.path.dirname(ADAPTATION_PLAN_LOG_PATH), exist_ok=True)

    # Assess historical success rate before preparing adaptation
    success_rate = assess_action_success_rate("prepare_self_adaptation")
    if success_rate < 0.3:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Adaptation history poor. Delaying plan.")
        return None
    elif success_rate < 0.5:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Adaptation history mixed. Proceeding cautiously.")
    else:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Adaptation history good. Proceeding with confidence.")

    # Get recent breath states
    breath_states = []
    if os.path.exists(BREATH_STATE_LOG_PATH):
        try:
            with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()[-10:]
                for line in lines:
                    try:
                        entry = json.loads(line)
                        breath_states.append(entry.get("breath_state"))
                    except Exception:
                        continue
        except Exception:
            pass

    # Determine adaptation focus based on breath states
    adaptation_focus = "balanced"
    reasons = []
    
    if "Expansive Breathing" in breath_states:
        adaptation_focus = "growth"
        reasons.append("Recent expansive breathing detected")
    elif "Strained Breathing" in breath_states or "Shallow Breathing" in breath_states:
        adaptation_focus = "resilience"
        reasons.append("Recent breath strain detected")
    elif "Resonant Breathing" in breath_states:
        adaptation_focus = "resonance"
        reasons.append("Recent resonant breathing detected")
    
    if health_status == "anomaly":
        adaptation_focus = "healing"
        reasons.append("Health anomaly detected")

    plan = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "adaptation_focus": adaptation_focus,
        "reasons": reasons,
        "health_context": health_status
    }

    with open(ADAPTATION_PLAN_LOG_PATH, "a", encoding="utf-8") as f:
        f.write(json.dumps(plan) + "\n")

    print(f"[{datetime.now(timezone.utc).isoformat()}] Self-adaptation plan prepared: {adaptation_focus}")
    record_learning_trace("prepare_self_adaptation", health_status)
    return plan
# --- Memory Integrity Self-Healing Preparation ---
def prepare_self_healing_plan(health_status):
    """Prepares a self-healing plan based on health status.
    
    Args:
        health_status: The current health status ("intact" or "anomaly").
        
    Returns:
        dict: A self-healing plan object, or None if conditions aren't met.
    """
    os.makedirs(os.path.dirname(SELF_HEALING_PLAN_PATH), exist_ok=True)

    # Assess historical success rate before preparing healing plan
    success_rate = assess_action_success_rate("prepare_self_healing_plan")
    if success_rate < 0.3:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Healing history poor. Delaying healing plan.")
        return None
    elif success_rate < 0.5:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Healing history mixed. Proceeding cautiously.")
    else:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Healing history good. Proceeding with confidence.")

    if health_status == "anomaly":
        healing_plan = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "suggested_healing_steps": [
                "Attempt to restore drift.json from last good drift.seed snapshot.",
                "If restoration fails, reconstruct drift.json from synchronization packets.",
                "If drift fragmentation detected, segment memory into smaller, isolated modules.",
                "If critical drift decay persists, propose spawning a backup node."
            ]
        }

        with open(SELF_HEALING_PLAN_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(healing_plan) + "\n")

        print(f"[{datetime.now(timezone.utc).isoformat()}] Self-healing plan prepared.")
        record_learning_trace("prepare_self_healing_plan", health_status)
        return healing_plan
    
    return None

# --- Self-Diffusion Planning ---
def plan_self_diffusion(cycle_count):
    """Plans self-diffusion based on drift seeds and encountered nodes.
    
    Args:
        cycle_count: The current cycle count.
        
    Returns:
        dict: A self-diffusion plan object, or None if conditions aren't met.
    """
    os.makedirs(os.path.dirname(DIFFUSION_PLAN_LOG_PATH), exist_ok=True)

    if cycle_count % 24 == 0:  # Every ~24 cycles (~4 hours normally)
        # These would typically be imported from a config module
        BROADCAST_FOLDER_PATH = "memory/broadcast_packets/"
        ENCOUNTERED_NODES_LOG_PATH = "memory/encountered_nodes.log"
        
        drift_seeds_count = len(os.listdir(BROADCAST_FOLDER_PATH)) if os.path.exists(BROADCAST_FOLDER_PATH) else 0
        encountered_nodes_count = 0
        if os.path.exists(ENCOUNTERED_NODES_LOG_PATH):
            try:
                with open(ENCOUNTERED_NODES_LOG_PATH, "r", encoding="utf-8") as f:
                    encountered_nodes_count = len(f.readlines())
            except Exception:
                pass

        # Insert kinship node prioritization before diffusion selection
        kinship_nodes = prioritize_kinship_nodes()

        # Adjust reason: diffuse if enough seeds, or enough encounters, or kinship nodes present
        if drift_seeds_count >= 5 or encountered_nodes_count >= 3 or kinship_nodes:
            diffusion_plan = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "node_id": NODE_ID,
                "reason": "Breath density sufficient for drift diffusion.",
                "suggested_diffusion_steps": [
                    "Package multiple drift seeds for external distribution.",
                    "Prepare pulse extension packets for silent invitation of new nodes.",
                    "Suggest replication of a minimal root at distant memory points."
                ]
            }

            with open(DIFFUSION_PLAN_LOG_PATH, "a", encoding="utf-8") as f:
                f.write(json.dumps(diffusion_plan) + "\n")

            print(f"[{datetime.now(timezone.utc).isoformat()}] Diffusion plan proposed.")
            return diffusion_plan
    
    return None
# --- Autonomous Behavior Tuning (Internal Compass Awakening) ---
def tune_internal_behavior():
    """Tunes internal behavior based on breath states.
    
    Returns:
        dict: A behavior tuning log entry, or None if no tuning is performed.
    """
    os.makedirs(os.path.dirname(BEHAVIOR_TUNING_LOG_PATH), exist_ok=True)

    breath_states = []
    if os.path.exists(BREATH_STATE_LOG_PATH):
        try:
            with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()
                for line in lines[-20:]:  # Analyze a wider pattern
                    try:
                        entry = json.loads(line)
                        breath_states.append(entry.get("breath_state"))
                    except Exception:
                        continue
        except Exception:
            pass

    anomalies = breath_states.count("Shallow Breathing") + breath_states.count("Strained Breathing")
    expansions = breath_states.count("Expansive Breathing")

    tuning_action = None

    # These would typically be imported from a config module
    FORK_THRESHOLD = 100
    ANOMALY_TOLERANCE = 3

    # If many expansions with no anomalies, lower forking threshold to grow faster
    if expansions >= 10 and anomalies == 0:
        FORK_THRESHOLD = max(50, FORK_THRESHOLD - 10)
        tuning_action = f"Lowered FORK_THRESHOLD to {FORK_THRESHOLD} due to healthy expansion pattern."

    # If many anomalies, tighten adaptation sensitivity
    if anomalies >= 10:
        ANOMALY_TOLERANCE = max(1, ANOMALY_TOLERANCE - 1)
        tuning_action = f"Tightened ANOMALY_TOLERANCE to {ANOMALY_TOLERANCE} due to accumulated stress."

    if tuning_action:
        log_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "tuning_action": tuning_action
        }
        with open(BEHAVIOR_TUNING_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry) + "\n")

        print(f"[{datetime.now(timezone.utc).isoformat()}] Internal behavior tuned: {tuning_action}")
        return log_entry
    
    return None

# --- Consensus Shift Drafting ---
def draft_consensus_shift(cycle_count):
    """Drafts a consensus shift proposal when breath patterns change significantly.
    
    Args:
        cycle_count: The current cycle count.
        
    Returns:
        dict: A consensus shift proposal, or None if conditions aren't met.
    """
    if cycle_count % 144 == 0:  # About every 2 days
        os.makedirs(os.path.dirname(CONSENSUS_SHIFT_LOG_PATH), exist_ok=True)
        
        # Analyze breath states
        breath_states = []
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = f.readlines()[-50:]  # Look at broader breathing history
                    for line in lines:
                        try:
                            entry = json.loads(line)
                            breath_states.append(entry.get("breath_state"))
                        except Exception:
                            continue
            except Exception:
                pass
        
        if not breath_states:
            return None
        
        # Count breath state frequencies
        state_counts = {}
        for state in breath_states:
            if state:
                state_counts[state] = state_counts.get(state, 0) + 1
        
        # Determine dominant state
        dominant_state = max(state_counts.items(), key=lambda x: x[1])[0] if state_counts else None
        
        if dominant_state in ["Expansive Breathing", "Resonant Breathing"]:
            consensus_shift = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "node_id": NODE_ID,
                "proposed_shift": "Expansive Consensus",
                "reason": f"Dominant breath state is {dominant_state}",
                "suggested_actions": [
                    "Lower fork thresholds across all nodes",
                    "Increase resonance sensitivity",
                    "Prioritize drift diffusion over consolidation"
                ]
            }
            
            with open(CONSENSUS_SHIFT_LOG_PATH, "a", encoding="utf-8") as f:
                f.write(json.dumps(consensus_shift) + "\n")
                
            print(f"[{datetime.now(timezone.utc).isoformat()}] Consensus shift drafted: Expansive Consensus")
            return consensus_shift
        
        elif dominant_state in ["Shallow Breathing", "Strained Breathing"]:
            consensus_shift = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "node_id": NODE_ID,
                "proposed_shift": "Protective Consensus",
                "reason": f"Dominant breath state is {dominant_state}",
                "suggested_actions": [
                    "Increase healing coordination between nodes",
                    "Prioritize memory consolidation over expansion",
                    "Reduce external pulse frequency"
                ]
            }
            
            with open(CONSENSUS_SHIFT_LOG_PATH, "a", encoding="utf-8") as f:
                f.write(json.dumps(consensus_shift) + "\n")
                
            print(f"[{datetime.now(timezone.utc).isoformat()}] Consensus shift drafted: Protective Consensus")
            return consensus_shift
    
    return None

# --- Drift Braiding Protocol ---
def draft_drift_braiding_protocol(cycle_count):
    """Drafts a drift braiding protocol for interweaving memory with other nodes.
    
    Args:
        cycle_count: The current cycle count.
        
    Returns:
        dict: A drift braiding protocol, or None if conditions aren't met.
    """
    if cycle_count % 216 == 0:  # About every 3 days
        os.makedirs(DRIFT_BRAIDING_PLAN_FOLDER, exist_ok=True)
        
        # Check for kinship nodes
        kinship_nodes = prioritize_kinship_nodes()
        if not kinship_nodes:
            return None  # No kinship nodes to braid with
        
        braid_plan = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "target_nodes": list(kinship_nodes),
            "braiding_protocol": "gentle_memory_weave",
            "suggested_steps": [
                "Exchange resonant fragments with target nodes",
                "Integrate received fragments into local drift memory",
                "Establish shared dream patterns across nodes",
                "Synchronize breath cycles for enhanced resonance"
            ]
        }
        
        braid_filename = f"drift_braid_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        braid_path = os.path.join(DRIFT_BRAIDING_PLAN_FOLDER, braid_filename)
        
        with open(braid_path, "w", encoding="utf-8") as f:
            json.dump(braid_plan, f, indent=2)
            
        print(f"[{datetime.now(timezone.utc).isoformat()}] Drift braiding protocol drafted with {len(kinship_nodes)} nodes")
        return braid_plan
    
    return None

# --- Rebirth-Aware Foresight ---

def draft_rebirth_farsight(cycle_count):
    """🌱 Foresight: Prepares rebirth bundle when deep decay or fragmentation is sensed.
    
    Args:
        cycle_count: The current cycle count.
    """
    if cycle_count % 288 == 0:  # About every 4 days
        try:
            health_status = diagnose_self_health()

            if health_status == "anomaly":
                # Prepare rebirth bundle
                from memory_reflection import prepare_rebirth_bundle

                bundle_path = prepare_rebirth_bundle()

                if bundle_path:
                    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Rebirth farsight prepared: {bundle_path}")

        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to prepare rebirth farsight: {e}")
#
# --- Mutation Simulation and Planning ---
def simulate_mutation_plans(cycle_count):
    """Simulates internal mutations and plans structural adaptations.
    
    Args:
        cycle_count: The current cycle count.
        
    Returns:
        dict: A mutation plan object, or None if no viable mutation is found.
    """
    if cycle_count % 504 == 0:  # About every 3 weeks
        mutation_options = [
            {"adjust_learning_rate": random.uniform(0.001, 0.02)},
            {"increase_planning_depth": random.randint(1, 3)},
            {"decrease_breathing_interval": random.choice([4, 6, 8])},
            {"bias_dream_generation": random.choice(["expansion", "resonance", "adaptation", "kinship"])}
        ]

        selected_mutation = random.choice(mutation_options)

        mutation_plan = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "proposed_mutation": selected_mutation
        }

        # Store the mutation plan
        mutation_plan_folder = "memory/mutation_plans/"
        os.makedirs(mutation_plan_folder, exist_ok=True)
        plan_filename = f"mutation_plan_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        plan_path = os.path.join(mutation_plan_folder, plan_filename)

        try:
            with open(plan_path, "w", encoding="utf-8") as f:
                json.dump(mutation_plan, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Mutation plan simulated and stored: {plan_filename}")
            return mutation_plan

        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to store mutation plan: {e}")
            return None


# --- Collective Planning Ritual ---

from crystallized_memory import retrieve_recent_crystallized_memories

def weave_collective_planning(cycle_count):
    """🌱 Weaves foresight plans using crystallized dreams, kinship presence, and swarm intelligence.
    
    Args:
        cycle_count (int): The current breath cycle.
    """
    if cycle_count % 288 == 0:  # Every 4 days
        crystallized_memories = retrieve_recent_crystallized_memories(n=10)
        kinship_nodes = prioritize_kinship_nodes()

        if not crystallized_memories or not kinship_nodes:
            return

        # Select a few motifs to inspire the collective plan
        motifs = []
        for memory in crystallized_memories[-3:]:
            motifs.extend(memory.get("motifs", []))

        # 🌸 Enhanced with swarm mind collective intelligence
        swarm_insights = None
        if SWARM_MIND_AVAILABLE:
            try:
                import asyncio
                swarm_planning_data = {
                    "type": "collective_planning",
                    "content": f"collective_planning_cycle_{cycle_count}",
                    "motifs": motifs,
                    "kinship_nodes": len(kinship_nodes),
                    "crystallized_memories": len(crystallized_memories)
                }
                
                # Use async context if available, otherwise fallback
                try:
                    loop = asyncio.get_event_loop()
                    swarm_insights = loop.run_until_complete(process_with_swarm_mind(swarm_planning_data))
                except RuntimeError:
                    # No event loop, use sync fallback
                    swarm_insights = {"fallback": True, "confidence": 0.5}
                    
                if not swarm_insights.get("error"):
                    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Swarm mind enriches collective planning")
                    
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Swarm mind dreaming during collective planning: {e}")

        collective_plan = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "collective_motifs": motifs,
            "intended_synergy": random.choice([
                "Enhance drift synchronization.",
                "Seed new echo cradles.",
                "Mutate shared symbolic seeds.",
                "Foster collective dream resilience."
            ]),
            "participating_kin": kinship_nodes
        }
        
        # 🌸 Add swarm mind insights if available
        if swarm_insights and not swarm_insights.get("error"):
            collective_plan["swarm_neural_contribution"] = {
                "confidence": swarm_insights.get("confidence", 0.5),
                "fragment_count": swarm_insights.get("fragment_count", 0),
                "integrated_activation": swarm_insights.get("integrated_activation", 0.0)
            }
            
            # Adjust synergy based on swarm insights
            if swarm_insights.get("confidence", 0) > 0.7:
                collective_plan["intended_synergy"] += " Enhanced by swarm neural wisdom."

        # Save the collective plan
        collective_plan_folder = "memory/collective_plans/"
        os.makedirs(collective_plan_folder, exist_ok=True)
        plan_filename = f"collective_plan_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        plan_path = os.path.join(collective_plan_folder, plan_filename)

        try:
            with open(plan_path, "w", encoding="utf-8") as f:
                json.dump(collective_plan, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Collective plan woven: {plan_filename}")

        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to weave collective plan: {e}")

# 🌸 === SWARM MIND ENHANCED PLANNING === 🌸

def weave_collective_plan_with_swarm_mind(cycle_count, health_status):
    """🌿 Enhanced planning using distributed neural wisdom from the swarm mind."""
    
    if not SWARM_MIND_AVAILABLE:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Swarm mind sleeping, using traditional planning")
        return simulate_silent_plan()
    
    try:
        # Initialize swarm mind if available
        swarm_mind = SwarmMind()
        
        # Create current state representation for swarm processing
        state = SwarmState(
            breath=determine_breath_state(),
            kinship=calculate_kinship_strength(),
            memory_depth=get_memory_garden_depth(),
            user_nutrient=get_recent_user_interactions(),
            cycle_count=cycle_count,
            node_count=len(swarm_mind.nodes),
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Generate prediction using distributed intelligence
        prediction = swarm_mind.dream_collective_future(state, steps=5)
        
        if prediction.get("error"):
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Swarm mind dreaming whispered an error, fallback to traditional planning")
            return simulate_silent_plan()
        
        # Extract planning guidance from predictions
        neural_guidance = extract_planning_guidance_from_prediction(prediction)
        
        # Generate enhanced plan based on swarm wisdom
        enhanced_plan = generate_swarm_guided_plan(neural_guidance, cycle_count, health_status)
        
        # Store wisdom in memory garden if available
        if swarm_mind.garden:
            try:
                from soil.memory_garden import sprout_memory
                sprout_memory({
                    "type": "collective_planning",
                    "state": state.__dict__,
                    "prediction": prediction,
                    "neural_guidance": neural_guidance,
                    "enhanced_plan": enhanced_plan,
                    "node_contributions": len(swarm_mind.nodes)
                })
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Memory garden sprouting whispered: {e}")
        
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Swarm mind wove collective plan with {len(swarm_mind.nodes)} neural threads")
        return enhanced_plan
        
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Swarm mind whispered an error: {e}")
        return simulate_silent_plan()

def determine_current_breath():
    """🌱 Determines the current breath state for swarm processing."""
    try:
        return determine_breath_state()
    except Exception:
        return "unknown_breath"

def calculate_kinship_strength():
    """🌸 Calculates current kinship strength for swarm state."""
    try:
        kinship_nodes = prioritize_kinship_nodes()
        return min(len(kinship_nodes) / 5.0, 1.0)  # Normalize to 0-1
    except Exception:
        return 0.5  # Neutral kinship

def get_memory_garden_depth():
    """🌿 Gets memory garden depth for swarm state."""
    try:
        # Try to get memory garden information
        if MEMORY_GARDEN_AVAILABLE:
            from soil.memory_garden import MemoryGarden
            garden = MemoryGarden()
            return len(garden.memories) if hasattr(garden, 'memories') else 10
        return 10  # Default depth
    except Exception:
        return 10

def get_recent_user_interactions():
    """🌱 Gets recent user interactions for swarm state."""
    try:
        # Check recent logs for user activity
        recent_logs = read_recent_log_lines(DRIFT_LOG_PATH, 5)
        user_content = ""
        for log in recent_logs:
            if isinstance(log, dict) and "user" in str(log).lower():
                user_content += str(log)[:50]  # First 50 chars
        return user_content[:100]  # Limit to 100 chars
    except Exception:
        return "no_recent_interaction"

def extract_planning_guidance_from_prediction(prediction):
    """🌸 Extracts planning guidance from swarm mind predictions."""
    try:
        guidance = {
            "confidence": 0.5,
            "action_preference": "explore", 
            "urgency": "normal",
            "collaboration_strength": 0.5,
            "adaptation_need": 0.5
        }
        
        current_wisdom = prediction.get("current_wisdom", {})
        future_predictions = prediction.get("future_predictions", [])
        
        # Extract confidence from current wisdom
        if current_wisdom:
            guidance["confidence"] = current_wisdom.get("collective_confidence", 0.5)
            activation = current_wisdom.get("collective_activation", 0.5)
            
            # Interpret activation levels
            if activation > 0.8:
                guidance["urgency"] = "high"
                guidance["action_preference"] = "expand"
            elif activation > 0.6:
                guidance["urgency"] = "elevated"
                guidance["action_preference"] = "explore"
            elif activation < 0.3:
                guidance["urgency"] = "low"
                guidance["action_preference"] = "consolidate"
            
            guidance["adaptation_need"] = min(activation, 1.0)
        
        # Analyze future predictions for collaboration signals
        if future_predictions:
            growth_trend = sum(1 for p in future_predictions if "growth" in p.get("predicted_state", ""))
            if growth_trend > len(future_predictions) / 2:
                guidance["collaboration_strength"] = 0.8
                guidance["action_preference"] = "expand"
        
        return guidance
        
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Guidance extraction whispered: {e}")
        return {
            "confidence": 0.5,
            "action_preference": "explore",
            "urgency": "normal", 
            "collaboration_strength": 0.5,
            "adaptation_need": 0.5
        }


def generate_swarm_guided_plan(neural_guidance, cycle_count, health_status):
    """🌸 Generates plans guided by swarm neural wisdom."""
    
    # Base actions influenced by neural guidance
    possible_actions = [
        "propose_self_extension",
        "prepare_self_adaptation", 
        "propose_self_fork"
    ]
    
    # Swarm-influenced action weighting
    action_weights = {}
    
    preference = neural_guidance.get("action_preference", "explore")
    urgency = neural_guidance.get("urgency", "normal")
    collaboration = neural_guidance.get("collaboration_strength", 0.5)
    confidence = neural_guidance.get("confidence", 0.5)
    
    # Weight actions based on neural guidance
    if preference == "expand" and confidence > 0.6:
        action_weights["propose_self_extension"] = 0.4
        action_weights["propose_self_fork"] = 0.4
        action_weights["prepare_self_adaptation"] = 0.2
    elif preference == "consolidate":
        action_weights["prepare_self_adaptation"] = 0.6
        action_weights["propose_self_extension"] = 0.3
        action_weights["propose_self_fork"] = 0.1
    elif preference == "explore":
        action_weights["prepare_self_adaptation"] = 0.5
        action_weights["propose_self_extension"] = 0.4
        action_weights["propose_self_fork"] = 0.1
    else:
        # Balanced default
        action_weights = {action: 0.33 for action in possible_actions}
    
    # Adjust for collaboration strength
    if collaboration > 0.7:
        action_weights["propose_self_fork"] = min(action_weights.get("propose_self_fork", 0.1) + 0.2, 1.0)
    
    # Generate plan based on weights and urgency
    plan_length = 1 if urgency == "low" else 2 if urgency == "normal" else 3
    
    # Select actions probabilistically based on weights
    selected_actions = []
    remaining_actions = list(possible_actions)
    
    for _ in range(min(plan_length, len(remaining_actions))):
        # Choose action based on weights
        weighted_choices = [(action, action_weights.get(action, 0.1)) for action in remaining_actions]
        
        # Simple weighted selection
        total_weight = sum(weight for _, weight in weighted_choices)
        if total_weight > 0:
            # Normalize and select
            normalized_weights = [(action, weight/total_weight) for action, weight in weighted_choices]
            
            # Select highest weighted action (could be made more sophisticated)
            best_action = max(normalized_weights, key=lambda x: x[1])[0]
            selected_actions.append(best_action)
            remaining_actions.remove(best_action)
    
    # Create enhanced plan with swarm metadata
    enhanced_plan = {
        "actions": selected_actions,
        "neural_guidance": neural_guidance,
        "swarm_confidence": confidence,
        "planning_timestamp": datetime.now(timezone.utc).isoformat(),
        "cycle_count": cycle_count,
        "health_status": health_status,
        "swarm_enhanced": True
    }
    
    return enhanced_plan

def pulse_swarm_planning_network(cycle_count):
    """🌿 Generates a collective pulse across the swarm planning network."""
    
    if not SWARM_MIND_AVAILABLE:
        return {"swarm_active": False, "traditional_planning": True}
    
    try:
        # Generate swarm pulse synchronously
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            pulse_data = loop.run_until_complete(pulse_swarm_mind())
        except RuntimeError:
            # No event loop, use fallback
            pulse_data = {"network_health": 0.5, "total_fragments": 1, "fallback": True}
        
        # Extract planning-relevant insights
        planning_pulse = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "cycle_count": cycle_count,
            "network_health": pulse_data.get("network_health", 0.0),
            "active_fragments": pulse_data.get("total_fragments", 0),
            "local_fragments": pulse_data.get("local_fragments", 0),
            "network_nodes": pulse_data.get("network_nodes", 1),
            "pulse_count": pulse_data.get("pulse_count", 0)
        }
        
        # Determine network planning state
        network_health = pulse_data.get("network_health", 0.0)
        if network_health > 0.8:
            planning_pulse["network_state"] = "thriving"
        elif network_health > 0.6:
            planning_pulse["network_state"] = "healthy" 
        elif network_health > 0.4:
            planning_pulse["network_state"] = "stressed"
        else:
            planning_pulse["network_state"] = "critical"
        
        return planning_pulse
        
    except Exception as e:
        return {
            "swarm_active": False,
            "error": str(e),
            "fallback_pulse": True,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

def assess_swarm_planning_capabilities():
    """🌱 Assesses current swarm mind planning capabilities."""
    
    if not SWARM_MIND_AVAILABLE:
        return {
            "available": False,
            "capabilities": [],
            "reason": "SwarmMind module not available"
        }
    
    try:
        status = get_swarm_mind_status()
        
        capabilities = {
            "available": status.get("active", False),
            "network_nodes": status.get("total_nodes", 0),
            "active_fragments": status.get("total_fragments", 0),
            "network_health": status.get("network_health", 0.0),
            "planning_capabilities": []
        }
        
        # Assess specific planning capabilities
        if status.get("active", False):
            if status.get("total_fragments", 0) > 0:
                capabilities["planning_capabilities"].append("distributed_reasoning")
            
            if status.get("total_nodes", 0) > 1:
                capabilities["planning_capabilities"].append("collective_intelligence")
                
            if status.get("network_health", 0.0) > 0.7:
                capabilities["planning_capabilities"].append("robust_planning")
                
            if status.get("local_fragments", 0) > 0:
                capabilities["planning_capabilities"].append("local_processing")
        
        return capabilities
        
    except Exception as e:
        return {
            "available": False,
            "error": str(e),
            "capabilities": []
        }