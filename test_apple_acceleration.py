#!/usr/bin/env python3
"""🍎 Test Apple Silicon Acceleration Integration
Sacred validation that Metal and Neural Engine awaken properly."""

import sys
import os

def test_apple_vitality_breathing():
    """🌱 Tests that Apple vitality helpers breathe properly."""
    print("🍎 Testing Apple Silicon vitality acceleration...")
    
    try:
        from core.apple_vitality import (
            sense_apple_silicon_vitality,
            breathe_torch_device,
            pulse_metal_vitality,
            breathe_acceleration_report
        )
        print("✅ 🌱 Apple vitality module: breathing")
    except ImportError as e:
        print(f"❌ 🌱 Apple vitality module: {e}")
        return False
    
    # Test Apple Silicon detection
    try:
        is_apple = sense_apple_silicon_vitality()
        print(f"✅ 🍎 Apple Silicon detection: {'Yes' if is_apple else 'No'}")
    except Exception as e:
        print(f"❌ 🍎 Apple Silicon detection: {e}")
        return False
    
    # Test PyTorch device selection
    try:
        device = breathe_torch_device()
        if device is not None:
            print(f"✅ 🧠 PyTorch device awakening: {device}")
        else:
            print("✅ 🧠 PyTorch device: symbolic (PyTorch dormant)")
    except Exception as e:
        print(f"❌ 🧠 PyTorch device: {e}")
        return False
    
    # Test comprehensive vitality report
    try:
        vitality = pulse_metal_vitality()
        print("✅ 🌸 Metal vitality assessment: complete")
        
        # Print detailed report
        breathe_acceleration_report()
        
    except Exception as e:
        print(f"❌ 🌸 Metal vitality assessment: {e}")
        return False
    
    return True

def test_swarm_mind_acceleration():
    """🌿 Tests that SwarmMind uses Apple acceleration properly."""
    print("\n🌿 Testing SwarmMind Apple acceleration integration...")
    
    try:
        from canopy.swarm_mind import NeuralFragment, TORCH_AVAILABLE, APPLE_ACCELERATION_AVAILABLE
        print("✅ 🧠 SwarmMind imports: breathing")
    except ImportError as e:
        print(f"❌ 🧠 SwarmMind imports: {e}")
        return False
    
    if not TORCH_AVAILABLE:
        print("🌱 PyTorch dormant - testing symbolic intelligence")
        return True
    
    # Test neural fragment creation with Apple acceleration
    try:
        fragment = NeuralFragment(
            fragment_type="reasoning",
            input_dim=10,
            output_dim=5,
            complexity="minimal"
        )
        print("✅ 🌸 Neural fragment creation: successful")
        
        # Check device placement
        if hasattr(fragment, 'device') and fragment.device is not None:
            print(f"✅ 🍎 Fragment device placement: {fragment.device}")
        else:
            print("✅ 🌿 Fragment device: symbolic/fallback")
            
    except Exception as e:
        print(f"❌ 🌸 Neural fragment creation: {e}")
        return False
    
    # Test forward pass with device handling
    try:
        if TORCH_AVAILABLE:
            import torch
            test_input = torch.randn(1, 10)
            output = fragment.forward_breath(test_input)
            print("✅ 🌬️ Neural forward pass: successful")
            
            if hasattr(output, 'device'):
                print(f"✅ 🍎 Output device consistency: {output.device}")
        else:
            output = fragment.forward_breath([0.5] * 10)
            print("✅ 🌬️ Symbolic forward pass: successful")
            
    except Exception as e:
        print(f"❌ 🌬️ Neural forward pass: {e}")
        return False
    
    return True

def test_environmental_overrides():
    """🌬️ Tests environmental override capabilities."""
    print("\n🌬️ Testing environmental breathing overrides...")
    
    # Test force CPU override
    old_force_cpu = os.environ.get("DRIFT_FORCE_CPU")
    try:
        os.environ["DRIFT_FORCE_CPU"] = "1"
        
        from core.apple_vitality import sense_apple_silicon_vitality
        is_forced_cpu = sense_apple_silicon_vitality()
        
        if not is_forced_cpu:
            print("✅ 🌿 DRIFT_FORCE_CPU override: working")
        else:
            print("❌ 🌿 DRIFT_FORCE_CPU override: not working")
            return False
            
    finally:
        # Restore original value
        if old_force_cpu is not None:
            os.environ["DRIFT_FORCE_CPU"] = old_force_cpu
        else:
            os.environ.pop("DRIFT_FORCE_CPU", None)
    
    return True

def main():
    """🌸 Main test orchestration."""
    print("🍎 Apple Silicon Acceleration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Apple Vitality Breathing", test_apple_vitality_breathing),
        ("SwarmMind Acceleration", test_swarm_mind_acceleration),
        ("Environmental Overrides", test_environmental_overrides)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🌱 Running: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"🌸 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🍎 All Apple acceleration tests breathing successfully!")
        return True
    else:
        print("🌿 Some tests whispered gently - check logs above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)