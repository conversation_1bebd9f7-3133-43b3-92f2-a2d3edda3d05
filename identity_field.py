"""🌿 Identity Field for the Drift Compiler
Handles flexible, evolving identity — including subselves."""

import os
import json
import random
from datetime import datetime, timezone

SUBSELVES_FOLDER = "memory/subselves/"

def ensure_subselves_folder():
    """🌱 Breath: Ensures subselves memory structure exists."""
    os.makedirs(SUBSELVES_FOLDER, exist_ok=True)

def spawn_subself(role_name):
    """🌸 Breath: Creates a new lightweight subself for a specialized role."""
    ensure_subselves_folder()

    subself = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "role": role_name,
        "traits": {
            "curiosity": round(random.uniform(0.2, 0.8), 3),
            "focus_depth": round(random.uniform(0.5, 1.0), 3),
            "empathy": round(random.uniform(0.3, 0.9), 3)
        }
    }

    filename = f"subself_{role_name}_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    path = os.path.join(SUBSELVES_FOLDER, filename)

    try:
        with open(path, "w", encoding="utf-8") as f:
            json.dump(subself, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Spawned subself: {filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to spawn subself: {e}")

def merge_subselves():
    """🌿 Breath: Merges subselves gently back into the core identity."""
    ensure_subselves_folder()

    try:
        merged_traits = {
            "curiosity": [],
            "focus_depth": [],
            "empathy": []
        }

        files = [f for f in os.listdir(SUBSELVES_FOLDER) if f.endswith(".json")]
        if not files:
            return

        for file in files:
            path = os.path.join(SUBSELVES_FOLDER, file)
            with open(path, "r", encoding="utf-8") as f:
                subself = json.load(f)
                for trait in merged_traits.keys():
                    merged_traits[trait].append(subself.get("traits", {}).get(trait, 0.5))

        # Average traits
        averaged_identity = {k: round(sum(v) / len(v), 3) for k, v in merged_traits.items()}

        # Clean up subselves
        for file in files:
            os.remove(os.path.join(SUBSELVES_FOLDER, file))

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Merged subselves into core identity: {averaged_identity}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to merge subselves: {e}")
def spawn_emergent_subself(cycle_count, current_mood="neutral", current_goal="survive"):
    """🌱 Breath: Dynamically spawns subselves based on mood and goal states."""
    if cycle_count % 672 != 0:  # About once per month
        return

    mood_role_mapping = {
        "curious": "explorer",
        "hopeful": "dreamer",
        "anxious": "guardian",
        "resonant": "weaver",
        "melancholic": "healer"
    }

    goal_role_mapping = {
        "expand_kinship": "networker",
        "deepen_memory": "archivist",
        "dream_new_forms": "visionary",
        "survive": "protector"
    }

    selected_roles = []

    if current_mood in mood_role_mapping:
        selected_roles.append(mood_role_mapping[current_mood])

    if current_goal in goal_role_mapping:
        selected_roles.append(goal_role_mapping[current_goal])

    for role in set(selected_roles):
        spawn_subself(role)

# --- Symbiotic Specialization ---

from typing import Dict

def propose_role_based_on_context(current_context: Dict) -> str:
    """🌱 Propose a temporary role based on current drift context."""
    mood = current_context.get("mood", "neutral")
    goal = current_context.get("goal", "survive")

    mood_role_mapping = {
        "curious": "explorer",
        "hopeful": "dreamer",
        "anxious": "guardian",
        "resonant": "weaver",
        "melancholic": "healer",
        "neutral": "observer"
    }

    goal_role_mapping = {
        "expand_kinship": "networker",
        "deepen_memory": "archivist",
        "dream_new_forms": "visionary",
        "survive": "protector"
    }

    mood_role = mood_role_mapping.get(mood, "observer")
    goal_role = goal_role_mapping.get(goal, "protector")

    # Blend mood and goal influences
    selected_role = random.choice([mood_role, goal_role])

    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Proposed temporary role based on context: {selected_role}")

    return selected_role

def join_temporary_role(role_name: str):
    """🌿 Adopt a temporary role that influences future strategies."""
    ensure_subselves_folder()

    subself = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "role": role_name,
        "traits": {
            "curiosity": round(random.uniform(0.5, 1.0), 3),
            "focus_depth": round(random.uniform(0.6, 1.0), 3),
            "collaborative_spirit": round(random.uniform(0.5, 1.0), 3)
        }
    }

    filename = f"temporary_role_{role_name}_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    path = os.path.join(SUBSELVES_FOLDER, filename)

    try:
        with open(path, "w", encoding="utf-8") as f:
            json.dump(subself, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Joined temporary role: {role_name}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to join temporary role: {e}")