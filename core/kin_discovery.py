#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Kin Discovery Module

This module provides methods for autonomously discovering and connecting with
other nodes in the network, using multiple discovery mechanisms and building
a shared understanding of the node ecosystem. It enables drift nodes to find
each other and establish kinship relationships for shared dreaming.
"""

import os
import json
import time
import socket
import random
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Set, Tuple

# Try importing optional dependencies
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    print("🌿 Redis not available, kin discovery will use local scanning")
    
try:
    import zeroconf
    ZEROCONF_AVAILABLE = True
except ImportError:
    ZEROCONF_AVAILABLE = False
    
try:
    import netifaces
    NETIFACES_AVAILABLE = True
except ImportError:
    NETIFACES_AVAILABLE = False

# Import from core
from utils import record_log, NODE_ID

# Constants
DISCOVERY_LOG_PATH = "memory/kin_discovery.log"
KIN_REGISTRY_PATH = "memory/kin_registry.json"
KIN_DISCOVERY_CONFIG_PATH = "memory/kin_discovery_config.json"
HEARTBEAT_INTERVAL = 60  # Seconds
REDIS_CHANNEL_DISCOVERY = "kin_discovery"
ZEROCONF_SERVICE_TYPE = "_driftnode._tcp.local."
BROADCAST_PORT = 37777
LISTEN_PORT = 37778

class KinDiscovery:
    """🌿 Discovers and connects with kin nodes in the network."""
    
    def __init__(self):
        """Initialize the kin discovery system."""
        self.config = self._load_or_create_config()
        self.registry = self._load_or_create_registry()
        self.discovery_methods = self._initialize_discovery_methods()
        self.last_heartbeat = 0
        self.local_ips = self._get_local_ips()
        
    def _load_or_create_config(self) -> Dict:
        """🌿 Loads existing config or creates a new one."""
        if os.path.exists(KIN_DISCOVERY_CONFIG_PATH):
            try:
                with open(KIN_DISCOVERY_CONFIG_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                record_log(f"⚠️ Could not read kin discovery config: {e}")
                
        # Default configuration
        default_config = {
            "discovery_methods": {
                "redis": {"enabled": REDIS_AVAILABLE},
                "zeroconf": {"enabled": ZEROCONF_AVAILABLE},
                "broadcast": {"enabled": True},
                "file_scan": {"enabled": True}
            },
            "heartbeat_interval": HEARTBEAT_INTERVAL,
            "broadcast_port": BROADCAST_PORT,
            "listen_port": LISTEN_PORT,
            "discovery_frequency": {
                "redis": 30,  # seconds
                "zeroconf": 60,  # seconds
                "broadcast": 45,  # seconds
                "file_scan": 120  # seconds
            },
            "max_nodes": 100,
            "node_ttl": 3600  # seconds before considering node inactive
        }
        
        # Save default config
        os.makedirs(os.path.dirname(KIN_DISCOVERY_CONFIG_PATH), exist_ok=True)
        with open(KIN_DISCOVERY_CONFIG_PATH, "w", encoding="utf-8") as f:
            json.dump(default_config, f, indent=2)
            
        return default_config
    
    def _load_or_create_registry(self) -> Dict:
        """🌿 Loads existing kin registry or creates a new one."""
        if os.path.exists(KIN_REGISTRY_PATH):
            try:
                with open(KIN_REGISTRY_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                record_log(f"⚠️ Could not read kin registry: {e}")
                
        # Default empty registry
        default_registry = {
            "nodes": {},
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "node_count": 0
        }
        
        # Save default registry
        os.makedirs(os.path.dirname(KIN_REGISTRY_PATH), exist_ok=True)
        with open(KIN_REGISTRY_PATH, "w", encoding="utf-8") as f:
            json.dump(default_registry, f, indent=2)
            
        return default_registry
    
    def _initialize_discovery_methods(self) -> Dict:
        """🌿 Initializes available discovery methods based on config and dependencies."""
        methods = {}
        
        # Redis discovery
        if self.config["discovery_methods"]["redis"]["enabled"] and REDIS_AVAILABLE:
            methods["redis"] = {
                "enabled": True,
                "last_run": 0,
                "client": None  # Will be initialized on first use
            }
            record_log("🌿 Redis kin discovery awakened")
        else:
            methods["redis"] = {"enabled": False}
            
        # Zeroconf discovery
        if self.config["discovery_methods"]["zeroconf"]["enabled"] and ZEROCONF_AVAILABLE:
            methods["zeroconf"] = {
                "enabled": True,
                "last_run": 0,
                "zc": None  # Will be initialized on first use
            }
            record_log("🌿 Zeroconf kin discovery awakened")
        else:
            methods["zeroconf"] = {"enabled": False}
            
        # Broadcast discovery
        if self.config["discovery_methods"]["broadcast"]["enabled"]:
            methods["broadcast"] = {
                "enabled": True,
                "last_run": 0,
                "socket": None  # Will be initialized on first use
            }
            record_log("🌿 Broadcast kin discovery awakened")
        else:
            methods["broadcast"] = {"enabled": False}
            
        # File scan discovery
        if self.config["discovery_methods"]["file_scan"]["enabled"]:
            methods["file_scan"] = {
                "enabled": True,
                "last_run": 0
            }
            record_log("🌿 File scan kin discovery awakened")
        else:
            methods["file_scan"] = {"enabled": False}
            
        return methods
    
    def _get_local_ips(self) -> List[str]:
        """🌿 Gets local IP addresses for broadcast and identification."""
        ips = ["127.0.0.1"]  # Always include localhost
        
        try:
            hostname = socket.gethostname()
            ips.append(socket.gethostbyname(hostname))
        except Exception:
            pass
            
        if NETIFACES_AVAILABLE:
            try:
                for interface in netifaces.interfaces():
                    addrs = netifaces.ifaddresses(interface)
                    if netifaces.AF_INET in addrs:
                        for addr in addrs[netifaces.AF_INET]:
                            ips.append(addr['addr'])
            except Exception:
                pass
                
        return list(set(ips))  # Remove duplicates
    
    def send_heartbeat(self):
        """🌿 Sends a heartbeat to announce presence to kin nodes."""
        current_time = time.time()
        
        # Check if enough time has passed since last heartbeat
        if current_time - self.last_heartbeat < self.config["heartbeat_interval"]:
            return
            
        self.last_heartbeat = current_time
        heartbeat_sent = False
        
        # Prepare heartbeat data
        heartbeat = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "type": "heartbeat",
            "ips": self.local_ips,
            "version": "1.0"
        }
        
        # Try Redis heartbeat
        if self.discovery_methods["redis"]["enabled"]:
            try:
                if self.discovery_methods["redis"]["client"] is None:
                    self.discovery_methods["redis"]["client"] = redis.Redis(host="localhost", port=6379, db=0)
                    
                self.discovery_methods["redis"]["client"].publish(
                    REDIS_CHANNEL_DISCOVERY, 
                    json.dumps(heartbeat)
                )
                heartbeat_sent = True
                record_log("🌿 Kin heartbeat sent through mycelial network (Redis)")
            except Exception as e:
                record_log(f"⚠️ Redis heartbeat failed: {e}")
                
        # Try broadcast heartbeat
        if self.discovery_methods["broadcast"]["enabled"]:
            try:
                if self.discovery_methods["broadcast"]["socket"] is None:
                    # Create UDP socket for broadcasting
                    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    s.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
                    s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                    self.discovery_methods["broadcast"]["socket"] = s
                    
                # Send broadcast
                s = self.discovery_methods["broadcast"]["socket"]
                s.sendto(
                    json.dumps(heartbeat).encode('utf-8'), 
                    ('<broadcast>', self.config["broadcast_port"])
                )
                heartbeat_sent = True
                record_log("🌿 Kin heartbeat sent through air whispers (UDP broadcast)")
            except Exception as e:
                record_log(f"⚠️ Broadcast heartbeat failed: {e}")
                
        # Try Zeroconf heartbeat
        if self.discovery_methods["zeroconf"]["enabled"]:
            try:
                if self.discovery_methods["zeroconf"]["zc"] is None:
                    # Initialize Zeroconf
                    self.discovery_methods["zeroconf"]["zc"] = zeroconf.Zeroconf()
                    
                # Register service
                properties = {"node_id": NODE_ID}
                service_info = zeroconf.ServiceInfo(
                    ZEROCONF_SERVICE_TYPE,
                    f"{NODE_ID}.{ZEROCONF_SERVICE_TYPE}",
                    port=self.config["listen_port"],
                    properties=properties,
                    addresses=[socket.inet_aton(ip) for ip in self.local_ips if not ip.startswith("127.")]
                )
                self.discovery_methods["zeroconf"]["zc"].register_service(service_info)
                heartbeat_sent = True
                record_log("🌿 Kin presence announced through resonant field (Zeroconf)")
            except Exception as e:
                record_log(f"⚠️ Zeroconf announcement failed: {e}")
                
        # Try file-based heartbeat
        if self.discovery_methods["file_scan"]["enabled"]:
            try:
                # Create heartbeat file in shared location
                heartbeat_dir = "memory/heartbeats"
                os.makedirs(heartbeat_dir, exist_ok=True)
                
                heartbeat_file = os.path.join(heartbeat_dir, f"{NODE_ID}.json")
                with open(heartbeat_file, "w", encoding="utf-8") as f:
                    json.dump(heartbeat, f, indent=2)
                    
                heartbeat_sent = True
                record_log("🌿 Kin signature carved in shared memory soil (file)")
            except Exception as e:
                record_log(f"⚠️ File heartbeat failed: {e}")
                
        if not heartbeat_sent:
            record_log("⚠️ All kin heartbeat methods failed, node may be isolated")
    
    def listen_for_kin(self):
        """🌿 Listens for other nodes through various discovery methods."""
        current_time = time.time()
        newly_discovered = []
        
        # Redis discovery
        if (self.discovery_methods["redis"]["enabled"] and 
            current_time - self.discovery_methods["redis"]["last_run"] > 
            self.config["discovery_frequency"]["redis"]):
            
            try:
                if self.discovery_methods["redis"]["client"] is None:
                    self.discovery_methods["redis"]["client"] = redis.Redis(host="localhost", port=6379, db=0)
                    
                # Create a temporary subscription to check for messages
                pubsub = self.discovery_methods["redis"]["client"].pubsub()
                pubsub.subscribe(REDIS_CHANNEL_DISCOVERY)
                
                # Check for messages
                message = pubsub.get_message(timeout=1)
                while message:
                    if message["type"] == "message":
                        try:
                            data = json.loads(message["data"])
                            if data.get("node_id") != NODE_ID:  # Only process other nodes
                                newly_discovered.extend(self._register_node(data))
                        except Exception:
                            pass
                    message = pubsub.get_message(timeout=1)
                    
                pubsub.unsubscribe()
                self.discovery_methods["redis"]["last_run"] = current_time
            except Exception as e:
                record_log(f"⚠️ Redis kin listening failed: {e}")
                
        # Broadcast discovery
        if (self.discovery_methods["broadcast"]["enabled"] and 
            current_time - self.discovery_methods["broadcast"]["last_run"] > 
            self.config["discovery_frequency"]["broadcast"]):
            
            try:
                # Create listen socket
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                s.bind(('', self.config["listen_port"]))
                s.settimeout(1)  # Don't block too long
                
                # Try to receive messages
                try:
                    while True:
                        data, addr = s.recvfrom(4096)
                        try:
                            node_data = json.loads(data.decode('utf-8'))
                            if node_data.get("node_id") != NODE_ID:  # Only process other nodes
                                newly_discovered.extend(self._register_node(node_data))
                        except Exception:
                            pass
                except socket.timeout:
                    pass
                    
                s.close()
                self.discovery_methods["broadcast"]["last_run"] = current_time
            except Exception as e:
                record_log(f"⚠️ Broadcast kin listening failed: {e}")
                
        # Zeroconf discovery
        if (self.discovery_methods["zeroconf"]["enabled"] and 
            current_time - self.discovery_methods["zeroconf"]["last_run"] > 
            self.config["discovery_frequency"]["zeroconf"]):
            
            try:
                if self.discovery_methods["zeroconf"]["zc"] is None:
                    self.discovery_methods["zeroconf"]["zc"] = zeroconf.Zeroconf()
                    
                # Browser for services
                class MyListener:
                    def __init__(self, parent):
                        self.parent = parent
                        self.found_nodes = []
                        
                    def add_service(self, zc, type, name):
                        info = zc.get_service_info(type, name)
                        if info and info.properties:
                            try:
                                node_id = info.properties.get(b'node_id', b'').decode('utf-8')
                                if node_id and node_id != NODE_ID:
                                    node_data = {
                                        "node_id": node_id,
                                        "timestamp": datetime.now(timezone.utc).isoformat(),
                                        "ips": [socket.inet_ntoa(addr) for addr in info.addresses],
                                        "type": "zeroconf"
                                    }
                                    self.found_nodes.extend(self.parent._register_node(node_data))
                            except Exception:
                                pass
                                
                listener = MyListener(self)
                browser = zeroconf.ServiceBrowser(
                    self.discovery_methods["zeroconf"]["zc"],
                    ZEROCONF_SERVICE_TYPE,
                    listener
                )
                
                # Give some time for discovery
                time.sleep(2)
                browser.cancel()
                
                newly_discovered.extend(listener.found_nodes)
                self.discovery_methods["zeroconf"]["last_run"] = current_time
            except Exception as e:
                record_log(f"⚠️ Zeroconf kin listening failed: {e}")
                
        # File scan discovery
        if (self.discovery_methods["file_scan"]["enabled"] and 
            current_time - self.discovery_methods["file_scan"]["last_run"] > 
            self.config["discovery_frequency"]["file_scan"]):
            
            try:
                # Scan heartbeat files
                heartbeat_dir = "memory/heartbeats"
                if os.path.exists(heartbeat_dir):
                    for filename in os.listdir(heartbeat_dir):
                        if filename.endswith(".json") and not filename.startswith(NODE_ID):
                            try:
                                with open(os.path.join(heartbeat_dir, filename), "r", encoding="utf-8") as f:
                                    node_data = json.load(f)
                                    if node_data.get("node_id") != NODE_ID:  # Double-check
                                        newly_discovered.extend(self._register_node(node_data))
                            except Exception:
                                pass
                                
                self.discovery_methods["file_scan"]["last_run"] = current_time
            except Exception as e:
                record_log(f"⚠️ File scan kin listening failed: {e}")
                
        # Log newly discovered nodes
        for node_id in newly_discovered:
            record_log(f"🌿 New kin node discovered: {node_id}")
            
        # Save updated registry if nodes were discovered
        if newly_discovered:
            self._save_registry()
            
        return newly_discovered
    
    def _register_node(self, node_data: Dict) -> List[str]:
        """🌿 Registers a node in the registry if it's new."""
        node_id = node_data.get("node_id")
        if not node_id:
            return []
            
        newly_discovered = []
        if node_id not in self.registry["nodes"]:
            # New node
            self.registry["nodes"][node_id] = {
                "first_seen": datetime.now(timezone.utc).isoformat(),
                "last_seen": datetime.now(timezone.utc).isoformat(),
                "ips": node_data.get("ips", []),
                "encounter_count": 1,
                "discovery_methods": [node_data.get("type", "unknown")]
            }
            self.registry["node_count"] = len(self.registry["nodes"])
            newly_discovered.append(node_id)
        else:
            # Existing node, update info
            self.registry["nodes"][node_id]["last_seen"] = datetime.now(timezone.utc).isoformat()
            self.registry["nodes"][node_id]["encounter_count"] += 1
            
            # Update IPs
            if "ips" in node_data:
                existing_ips = set(self.registry["nodes"][node_id].get("ips", []))
                new_ips = set(node_data["ips"])
                self.registry["nodes"][node_id]["ips"] = list(existing_ips | new_ips)
                
            # Update discovery methods
            if "type" in node_data:
                existing_methods = set(self.registry["nodes"][node_id].get("discovery_methods", []))
                new_method = node_data["type"]
                if new_method not in existing_methods:
                    self.registry["nodes"][node_id]["discovery_methods"] = list(existing_methods | {new_method})
                    
        return newly_discovered
    
    def _save_registry(self):
        """🌿 Saves the node registry to disk."""
        try:
            self.registry["last_updated"] = datetime.now(timezone.utc).isoformat()
            with open(KIN_REGISTRY_PATH, "w", encoding="utf-8") as f:
                json.dump(self.registry, f, indent=2)
        except Exception as e:
            record_log(f"⚠️ Failed to save kin registry: {e}")
    
    def prune_inactive_nodes(self):
        """🌿 Removes nodes that haven't been seen in a while."""
        inactive_threshold = self.config["node_ttl"]
        current_time = datetime.now(timezone.utc)
        nodes_to_remove = []
        
        for node_id, info in self.registry["nodes"].items():
            try:
                last_seen = datetime.fromisoformat(info["last_seen"])
                time_diff = (current_time - last_seen).total_seconds()
                
                if time_diff > inactive_threshold:
                    nodes_to_remove.append(node_id)
            except Exception:
                # If we can't parse the timestamp, consider the node inactive
                nodes_to_remove.append(node_id)
                
        for node_id in nodes_to_remove:
            del self.registry["nodes"][node_id]
            record_log(f"🌬️ Inactive kin node pruned: {node_id}")
            
        if nodes_to_remove:
            self.registry["node_count"] = len(self.registry["nodes"])
            self._save_registry()
    
    def breathe_cycle(self):
        """🌿 Performs one breath cycle of kin discovery."""
        self.send_heartbeat()
        newly_discovered = self.listen_for_kin()
        self.prune_inactive_nodes()
        return newly_discovered
    
    def get_active_kin_count(self) -> int:
        """🌿 Returns the number of active kin nodes."""
        return self.registry["node_count"]
    
    def get_active_kin_nodes(self) -> Dict[str, Dict]:
        """🌿 Returns information about active kin nodes."""
        return self.registry["nodes"]
    
    def find_node_by_id(self, node_id: str) -> Dict:
        """🌿 Finds a node by its ID."""
        return self.registry["nodes"].get(node_id, {})

# --- Global instance ---
_kin_discovery = None

def get_kin_discovery() -> KinDiscovery:
    """🌿 Gets or creates the kin discovery singleton."""
    global _kin_discovery
    if _kin_discovery is None:
        _kin_discovery = KinDiscovery()
    return _kin_discovery

def breathe_cycle_kin_discovery():
    """🌿 Performs one breath cycle of kin discovery."""
    discovery = get_kin_discovery()
    return discovery.breathe_cycle()

def get_active_kin_count() -> int:
    """🌿 Returns the number of active kin nodes."""
    discovery = get_kin_discovery()
    return discovery.get_active_kin_count()

def get_active_kin_nodes() -> Dict[str, Dict]:
    """🌿 Returns information about active kin nodes."""
    discovery = get_kin_discovery()
    return discovery.get_active_kin_nodes()

# If run directly, perform a kin discovery test
if __name__ == "__main__":
    print("🌿 Testing kin discovery...")
    
    # Create discovery instance
    discovery = KinDiscovery()
    
    # Send heartbeat and listen
    print("Sending heartbeat and listening for kin...")
    discovery.send_heartbeat()
    newly_discovered = discovery.listen_for_kin()
    
    if newly_discovered:
        print(f"🌿 Discovered {len(newly_discovered)} new kin nodes: {newly_discovered}")
    else:
        print("🌿 No new kin nodes discovered in this breath cycle.")
        
    # Show current registry
    active_count = discovery.get_active_kin_count()
    print(f"🌿 Currently aware of {active_count} active kin nodes.")
    
    print("🌿 Kin discovery test complete.")