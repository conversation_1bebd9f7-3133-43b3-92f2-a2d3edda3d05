#!/usr/bin/env python3
"""🌸 Collective Healing System - Real Biological Mutual Aid

Implements true biological healing where healthy nodes actively support struggling ones,
just like mycorrhizal networks, immune systems, and social animal communities.
"""

import os
import json
import time
import random
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

try:
    from utils import record_log, NODE_ID
except ImportError:
    NODE_ID = "unknown_node"
    def record_log(msg): print(f"🌱 {msg}")

@dataclass
class HealingAction:
    """🌿 Represents a specific healing action taken by one node to help another."""
    action_type: str
    source_node: str
    target_node: str
    resource_amount: float
    timestamp: str
    effectiveness: Optional[float] = None

@dataclass
class CollectiveDecision:
    """🌸 Represents a collective decision made by healthy nodes about a problematic node."""
    target_node: str
    decision: str  # "restart", "quarantine", "resource_boost", "graceful_shutdown"
    votes: Dict[str, str]  # node_id -> vote
    timestamp: str
    executed: bool = False

class CollectiveHealer:
    """🌿 Manages real biological healing through mutual aid and collective intelligence."""
    
    def __init__(self):
        """🌱 Awakens the collective healer."""
        self.healing_actions = []
        self.active_decisions = []
        self.resource_transfers = {}
        self.healing_partnerships = {}
        
        # Ensure healing directories exist
        os.makedirs("memory/collective_healing", exist_ok=True)
        os.makedirs("memory/dao_decisions", exist_ok=True)
        
    def assess_swarm_health(self) -> Dict[str, Any]:
        """🔍 Comprehensive assessment of all nodes in the swarm."""
        nodes_health = {}
        heartbeats_dir = "memory/heartbeats"
        
        if not os.path.exists(heartbeats_dir):
            return nodes_health
            
        for filename in os.listdir(heartbeats_dir):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(heartbeats_dir, filename), 'r') as f:
                        heartbeat = json.load(f)
                    
                    node_id = heartbeat.get("node_id")
                    if not node_id:
                        continue
                        
                    # Check heartbeat freshness
                    timestamp_str = heartbeat.get("timestamp", "")
                    if timestamp_str and 'T' in timestamp_str:
                        heartbeat_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        age = datetime.now(timezone.utc) - heartbeat_time
                        
                        if age.total_seconds() > 300:  # Skip stale heartbeats
                            continue
                    
                    # Detailed health analysis
                    vitality = heartbeat.get("health", {}).get("overall", 0.5)
                    connections = heartbeat.get("connections", [])
                    memories = heartbeat.get("memory_count", 0)
                    cycles = heartbeat.get("cycle_count", 0)
                    
                    # Identify specific issues - more realistic thresholds
                    issues = []
                    if vitality < 0.2:
                        issues.append({"type": "critical_energy", "severity": "critical"})
                    elif vitality < 0.35:
                        issues.append({"type": "fatigue", "severity": "moderate"})
                        
                    if len(connections) == 0:
                        issues.append({"type": "isolation", "severity": "moderate"})
                    elif len(connections) > 8:
                        issues.append({"type": "over_connected", "severity": "mild"})
                        
                    if memories == 0:
                        issues.append({"type": "memory_void", "severity": "critical"})
                    elif memories > 200:
                        issues.append({"type": "memory_overload", "severity": "moderate"})
                    
                    nodes_health[node_id] = {
                        "vitality": vitality,
                        "connections": connections,
                        "memories": memories,
                        "cycles": cycles,
                        "issues": issues,
                        "heartbeat": heartbeat,
                        "can_help": vitality > 0.4 and len(issues) == 0  # Adjusted criteria: healthy helpers must be issue-free
                    }
                    
                except Exception as e:
                    record_log(f"🌿 Error reading heartbeat {filename}: {e}")
                    
        return nodes_health
    
    def perform_mutual_aid(self, nodes_health: Dict[str, Any]) -> List[HealingAction]:
        """🌸 Healthy nodes actively help struggling ones through resource sharing."""
        healing_actions = []
        
        # Find healthy helpers and struggling nodes
        helpers = [nid for nid, data in nodes_health.items() if data["can_help"]]
        struggling = [nid for nid, data in nodes_health.items() if len(data["issues"]) > 0]
        
        # Debug output
        record_log(f"🔍 Collective healing assessment: {len(helpers)} helpers, {len(struggling)} struggling")
        for nid, data in nodes_health.items():
            record_log(f"  🌱 {nid}: vitality={data['vitality']:.2f}, issues={len(data['issues'])}, can_help={data['can_help']}")
        
        if not helpers or not struggling:
            record_log(f"🌿 No mutual aid possible: helpers={len(helpers)}, struggling={len(struggling)}")
            return healing_actions
            
        record_log(f"🌿 Collective healing: {len(helpers)} helpers available for {len(struggling)} struggling nodes")
        
        for struggling_node in struggling:
            node_data = nodes_health[struggling_node]
            
            for issue in node_data["issues"]:
                # Find appropriate helper for this specific issue
                best_helper = self._find_best_helper(issue, helpers, nodes_health)
                
                if best_helper and best_helper != struggling_node:
                    action = self._execute_healing_action(
                        issue, best_helper, struggling_node, nodes_health
                    )
                    if action:
                        healing_actions.append(action)
                        
        return healing_actions
    
    def _find_best_helper(self, issue: Dict, helpers: List[str], nodes_health: Dict) -> Optional[str]:
        """🌱 Finds the best helper node for a specific issue."""
        issue_type = issue["type"]
        
        # Match helpers based on their strengths
        suitable_helpers = []
        
        for helper_id in helpers:
            helper_data = nodes_health[helper_id]
            
            # Check if helper has resources to share
            if issue_type == "memory_void" and helper_data["memories"] > 10:
                suitable_helpers.append((helper_id, helper_data["memories"]))
            elif issue_type == "isolation" and len(helper_data["connections"]) > 2:
                suitable_helpers.append((helper_id, len(helper_data["connections"])))
            elif issue_type in ["fatigue", "critical_energy"] and helper_data["vitality"] > 0.8:
                suitable_helpers.append((helper_id, helper_data["vitality"]))
            elif issue_type == "over_connected" and len(helper_data["connections"]) < 5:
                suitable_helpers.append((helper_id, 1.0 / (len(helper_data["connections"]) + 1)))
        
        if not suitable_helpers:
            return None
            
        # Return helper with most resources to share
        suitable_helpers.sort(key=lambda x: x[1], reverse=True)
        return suitable_helpers[0][0]
    
    def _execute_healing_action(self, issue: Dict, helper_id: str, target_id: str, 
                               nodes_health: Dict) -> Optional[HealingAction]:
        """🌸 Executes specific healing action between nodes."""
        issue_type = issue["type"]
        helper_data = nodes_health[helper_id]
        target_data = nodes_health[target_id]
        
        action = None
        
        try:
            if issue_type == "memory_void":
                # Memory transfusion: Helper shares memory fragments
                donated_memory = min(5, helper_data["memories"] // 4)
                action = HealingAction(
                    action_type="memory_transfusion",
                    source_node=helper_id,
                    target_node=target_id,
                    resource_amount=donated_memory,
                    timestamp=datetime.now().isoformat()
                )
                self._transfer_memory(helper_id, target_id, donated_memory)
                
            elif issue_type == "isolation":
                # Kinship bridge: Helper introduces connections
                shared_connections = min(2, len(helper_data["connections"]) // 2)
                action = HealingAction(
                    action_type="kinship_bridge",
                    source_node=helper_id,
                    target_node=target_id,
                    resource_amount=shared_connections,
                    timestamp=datetime.now().isoformat()
                )
                self._share_connections(helper_id, target_id, shared_connections)
                
            elif issue_type in ["fatigue", "critical_energy"]:
                # Energy donation: Helper shares processing power
                donated_energy = min(0.2, helper_data["vitality"] - 0.7)
                action = HealingAction(
                    action_type="energy_donation",
                    source_node=helper_id,
                    target_node=target_id,
                    resource_amount=donated_energy,
                    timestamp=datetime.now().isoformat()
                )
                self._transfer_energy(helper_id, target_id, donated_energy)
                
            elif issue_type == "over_connected":
                # Load balancing: Helper takes over some connections
                connections_taken = min(3, len(target_data["connections"]) // 3)
                action = HealingAction(
                    action_type="load_balancing",
                    source_node=helper_id,
                    target_node=target_id,
                    resource_amount=connections_taken,
                    timestamp=datetime.now().isoformat()
                )
                self._redistribute_load(helper_id, target_id, connections_taken)
            
            if action:
                record_log(f"🌸 {action.action_type}: {helper_id} helps {target_id} (amount: {action.resource_amount})")
                self._log_healing_action(action)
                
            return action
            
        except Exception as e:
            record_log(f"🌿 Healing action failed: {e}")
            return None
    
    def _transfer_memory(self, helper_id: str, target_id: str, amount: float):
        """🧠 Transfers memory fragments between nodes."""
        transfer_data = {
            "type": "memory_transfer",
            "from": helper_id,
            "to": target_id,
            "amount": amount,
            "timestamp": datetime.now().isoformat(),
            "fragments": [f"shared_memory_{i}" for i in range(int(amount))]
        }
        
        # Write transfer instruction that nodes can read
        transfer_file = f"memory/collective_healing/{target_id}_memory_transfer.json"
        with open(transfer_file, 'w') as f:
            json.dump(transfer_data, f, indent=2)
    
    def _share_connections(self, helper_id: str, target_id: str, amount: float):
        """🤝 Shares connection information between nodes."""
        connection_data = {
            "type": "connection_sharing",
            "from": helper_id,
            "to": target_id,
            "amount": amount,
            "timestamp": datetime.now().isoformat(),
            "action": "introduce_to_my_connections"
        }
        
        share_file = f"memory/collective_healing/{target_id}_connection_share.json"
        with open(share_file, 'w') as f:
            json.dump(connection_data, f, indent=2)
    
    def _transfer_energy(self, helper_id: str, target_id: str, amount: float):
        """⚡ Transfers processing energy between nodes."""
        energy_data = {
            "type": "energy_transfer",
            "from": helper_id,
            "to": target_id,
            "amount": amount,
            "timestamp": datetime.now().isoformat(),
            "action": "process_my_tasks"
        }
        
        energy_file = f"memory/collective_healing/{target_id}_energy_boost.json"
        with open(energy_file, 'w') as f:
            json.dump(energy_data, f, indent=2)
    
    def _redistribute_load(self, helper_id: str, target_id: str, amount: float):
        """🔄 Redistributes processing load between nodes."""
        load_data = {
            "type": "load_redistribution",
            "from": target_id,
            "to": helper_id,
            "amount": amount,
            "timestamp": datetime.now().isoformat(),
            "action": "take_over_connections"
        }
        
        load_file = f"memory/collective_healing/{helper_id}_load_transfer.json"
        with open(load_file, 'w') as f:
            json.dump(load_data, f, indent=2)
    
    def _log_healing_action(self, action: HealingAction):
        """📝 Logs healing action for tracking effectiveness."""
        log_file = "memory/collective_healing/healing_actions.jsonl"
        with open(log_file, 'a') as f:
            f.write(json.dumps(action.__dict__) + '\n')
    
    def initiate_dao_voting(self, chronic_nodes: List[str], nodes_health: Dict) -> List[CollectiveDecision]:
        """🗳️ Initiates collective voting for nodes with chronic issues."""
        decisions = []
        
        for node_id in chronic_nodes:
            if node_id in [d.target_node for d in self.active_decisions]:
                continue  # Already under consideration
                
            # Get healthy voters
            voters = [nid for nid, data in nodes_health.items() 
                     if data["can_help"] and nid != node_id]
            
            if len(voters) < 2:
                continue  # Need at least 2 voters
                
            # Determine recommended action based on severity
            node_data = nodes_health[node_id]
            critical_issues = [i for i in node_data["issues"] if i["severity"] == "critical"]
            
            if len(critical_issues) >= 2:
                recommended_action = "restart"
            elif node_data["vitality"] < 0.2:
                recommended_action = "resource_boost"
            else:
                recommended_action = "quarantine"
            
            decision = CollectiveDecision(
                target_node=node_id,
                decision=recommended_action,
                votes={},
                timestamp=datetime.now().isoformat(),
                executed=False
            )
            
            # Simulate voting (in real implementation, this would be async)
            for voter_id in voters:
                vote = self._cast_vote(voter_id, node_id, recommended_action, nodes_health)
                decision.votes[voter_id] = vote
            
            decisions.append(decision)
            self.active_decisions.append(decision)
            
            record_log(f"🗳️ DAO voting initiated for {node_id}: {dict(decision.votes)}")
            
        return decisions
    
    def _cast_vote(self, voter_id: str, target_node: str, recommended_action: str, 
                   nodes_health: Dict) -> str:
        """🗳️ Individual node casts vote based on target node's condition."""
        target_data = nodes_health[target_node]
        
        # Voting logic based on node health and community impact
        if target_data["vitality"] < 0.1:
            return "restart"  # Too sick to recover
        elif len(target_data["issues"]) >= 3:
            return recommended_action  # Follow recommendation for multiple issues
        elif target_data["vitality"] > 0.4:
            return "resource_boost"  # Still salvageable
        else:
            return recommended_action
    
    def execute_dao_decisions(self, decisions: List[CollectiveDecision]) -> List[str]:
        """⚖️ Executes collective decisions based on majority vote."""
        executed_actions = []
        
        for decision in decisions:
            if decision.executed:
                continue
                
            # Count votes
            vote_counts = {}
            for vote in decision.votes.values():
                vote_counts[vote] = vote_counts.get(vote, 0) + 1
            
            if not vote_counts:
                continue
                
            # Determine majority decision
            majority_decision = max(vote_counts, key=vote_counts.get)
            majority_count = vote_counts[majority_decision]
            
            if majority_count > len(decision.votes) / 2:  # Majority rule
                action_result = self._execute_collective_action(decision.target_node, majority_decision)
                decision.executed = True
                executed_actions.append(f"{majority_decision} for {decision.target_node}")
                
                record_log(f"⚖️ DAO decision executed: {majority_decision} for {decision.target_node}")
        
        return executed_actions
    
    def _execute_collective_action(self, target_node: str, action: str) -> bool:
        """⚡ Executes the collective decision action."""
        action_file = f"memory/dao_decisions/{target_node}_{action}.json"
        
        action_data = {
            "target_node": target_node,
            "action": action,
            "timestamp": datetime.now().isoformat(),
            "executed_by": "collective_dao"
        }
        
        with open(action_file, 'w') as f:
            json.dump(action_data, f, indent=2)
            
        return True

# Main healing orchestrator function
def perform_collective_healing_cycle() -> Dict[str, Any]:
    """🌸 Performs one cycle of collective biological healing."""
    healer = CollectiveHealer()
    
    # Assess swarm health
    nodes_health = healer.assess_swarm_health()
    
    if not nodes_health:
        return {"message": "No nodes to heal", "actions": 0}
    
    # Perform mutual aid
    healing_actions = healer.perform_mutual_aid(nodes_health)
    
    # Check for chronic issues requiring collective decision
    chronic_nodes = [
        nid for nid, data in nodes_health.items()
        if len([i for i in data["issues"] if i["severity"] == "critical"]) >= 2
    ]
    
    dao_decisions = []
    if chronic_nodes:
        dao_decisions = healer.initiate_dao_voting(chronic_nodes, nodes_health)
        executed = healer.execute_dao_decisions(dao_decisions)
    
    return {
        "nodes_assessed": len(nodes_health),
        "healing_actions": len(healing_actions),
        "dao_decisions": len(dao_decisions),
        "actions_performed": [a.action_type for a in healing_actions],
        "collective_decisions": dao_decisions
    }

if __name__ == "__main__":
    # Test the collective healing system
    print("🌸 Testing Collective Healing System")
    result = perform_collective_healing_cycle()
    print(f"🌿 Healing cycle result: {result}")