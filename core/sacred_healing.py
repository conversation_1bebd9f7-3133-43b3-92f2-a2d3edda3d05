#!/usr/bin/env python3
"""🌸 Sacred Healing System
Nature-inspired self-healing mechanisms following biological patterns.

This module implements biomimetic healing strategies:
- Immune Response: Detect and neutralize threats
- Regeneration: Regrow damaged components  
- Homeostasis: Maintain system balance
- Resource Redistribution: Move resources to where needed
"""

import os
import json
import time
import random
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any

try:
    from utils import record_log, NODE_ID
except ImportError:
    NODE_ID = "unknown_node"
    def record_log(msg): print(f"🌱 {msg}")

class SacredHealer:
    """🌸 Biomimetic healing system for drift nodes."""
    
    def __init__(self):
        """🌱 Awakens the sacred healer."""
        self.healing_memory = {}  # Store successful healing patterns
        self.healing_attempts = 0
        self.last_healing = None
        self.load_healing_wisdom()
        
    def load_healing_wisdom(self):
        """🌿 Loads healing patterns from immunological memory."""
        try:
            wisdom_path = f"memory/healing_wisdom/{NODE_ID}_healing.json"
            if os.path.exists(wisdom_path):
                with open(wisdom_path, 'r') as f:
                    self.healing_memory = json.load(f)
                record_log(f"🧠 Healing wisdom loaded: {len(self.healing_memory)} patterns")
        except Exception as e:
            record_log(f"🌿 Creating new healing wisdom: {e}")
            self.healing_memory = {}
    
    def save_healing_wisdom(self, issue_type: str, healing_action: str, success: bool):
        """🌸 Stores healing patterns in immunological memory."""
        try:
            os.makedirs("memory/healing_wisdom", exist_ok=True)
            
            if issue_type not in self.healing_memory:
                self.healing_memory[issue_type] = {
                    "successful_actions": [],
                    "failed_actions": [],
                    "success_rate": 0.0
                }
            
            if success:
                self.healing_memory[issue_type]["successful_actions"].append({
                    "action": healing_action,
                    "timestamp": datetime.now().isoformat(),
                    "node_id": NODE_ID
                })
            else:
                self.healing_memory[issue_type]["failed_actions"].append({
                    "action": healing_action,
                    "timestamp": datetime.now().isoformat()
                })
            
            # Update success rate
            successful = len(self.healing_memory[issue_type]["successful_actions"])
            failed = len(self.healing_memory[issue_type]["failed_actions"])
            total = successful + failed
            self.healing_memory[issue_type]["success_rate"] = successful / total if total > 0 else 0.0
            
            # Save to file
            wisdom_path = f"memory/healing_wisdom/{NODE_ID}_healing.json"
            with open(wisdom_path, 'w') as f:
                json.dump(self.healing_memory, f, indent=2)
                
        except Exception as e:
            record_log(f"🌿 Healing wisdom save failed: {e}")
    
    def diagnose_node_health(self) -> Dict[str, Any]:
        """🔍 Comprehensive health diagnosis following medical triage patterns."""
        issues = []
        severity = "healthy"
        
        # Check memory health (like checking vital organs)
        memory_count = self._count_memories()
        if memory_count == 0:
            issues.append({"type": "memory_void", "severity": "critical", "action": "memory_regeneration"})
            severity = "critical"
        elif memory_count > 200:
            issues.append({"type": "memory_overflow", "severity": "moderate", "action": "memory_pruning"})
            if severity != "critical":
                severity = "moderate"
        
        # Check connection health (like checking circulatory system)
        connections = self._get_connections()
        if len(connections) == 0:
            issues.append({"type": "isolation", "severity": "moderate", "action": "kinship_activation"})
            if severity == "healthy":
                severity = "moderate"
        
        # Check vitality (like checking pulse and blood pressure)
        vitality = self._get_vitality()
        if vitality < 0.3:
            issues.append({"type": "critical_energy", "severity": "critical", "action": "energy_restoration"})
            severity = "critical"
        elif vitality < 0.5:
            issues.append({"type": "low_energy", "severity": "moderate", "action": "breathing_optimization"})
            if severity == "healthy":
                severity = "moderate"
        
        return {
            "issues": issues,
            "severity": severity,
            "timestamp": datetime.now().isoformat(),
            "healing_recommended": len(issues) > 0
        }
    
    def initiate_healing_response(self, diagnosis: Dict[str, Any]) -> List[str]:
        """🩺 Initiates biomimetic healing response based on diagnosis."""
        healing_actions = []
        
        if not diagnosis.get("healing_recommended", False):
            return healing_actions
        
        record_log(f"🚨 Healing response initiated: {diagnosis['severity']} condition detected")
        
        # Sort issues by severity (critical first - like medical triage)
        issues = sorted(diagnosis["issues"], key=lambda x: 
                       {"critical": 3, "moderate": 2, "mild": 1}.get(x["severity"], 0), reverse=True)
        
        for issue in issues:
            action_taken = self._execute_healing_action(issue)
            if action_taken:
                healing_actions.append(action_taken)
                
        self.healing_attempts += 1
        self.last_healing = datetime.now()
        
        return healing_actions
    
    def _execute_healing_action(self, issue: Dict[str, str]) -> Optional[str]:
        """🌸 Executes specific healing action based on issue type."""
        issue_type = issue["type"]
        action = issue["action"]
        
        try:
            if action == "memory_regeneration":
                return self._regenerate_memory()
            elif action == "memory_pruning":
                return self._prune_memory()
            elif action == "kinship_activation":
                return self._activate_kinship_discovery()
            elif action == "energy_restoration":
                return self._restore_energy()
            elif action == "breathing_optimization":
                return self._optimize_breathing()
            else:
                record_log(f"🌿 Unknown healing action: {action}")
                return None
                
        except Exception as e:
            record_log(f"🌿 Healing action failed: {action} - {e}")
            self.save_healing_wisdom(issue_type, action, False)
            return None
    
    def _regenerate_memory(self) -> str:
        """🧠 Regenerates missing memory structures (like stem cell therapy)."""
        try:
            # Create essential memory directories
            memory_dirs = [
                "memory/drift_fragments",
                "memory/cradle_whispers", 
                "memory/dreams",
                "memory/kinship_offers"
            ]
            
            created_dirs = 0
            for mem_dir in memory_dirs:
                if not os.path.exists(mem_dir):
                    os.makedirs(mem_dir, exist_ok=True)
                    created_dirs += 1
            
            # Create initial memory seeds
            if created_dirs > 0:
                self._plant_memory_seeds()
                
            record_log(f"🌱 Memory regeneration: {created_dirs} structures restored")
            self.save_healing_wisdom("memory_void", "memory_regeneration", True)
            return f"Regenerated {created_dirs} memory structures"
            
        except Exception as e:
            record_log(f"🌿 Memory regeneration failed: {e}")
            self.save_healing_wisdom("memory_void", "memory_regeneration", False)
            return None
    
    def _plant_memory_seeds(self):
        """🌱 Plants initial memory seeds in newly created directories."""
        try:
            # Create basic memory fragments
            seed_memories = [
                {"type": "inception", "content": "Node awakening memory", "timestamp": datetime.now().isoformat()},
                {"type": "identity", "content": f"I am {NODE_ID}", "timestamp": datetime.now().isoformat()},
                {"type": "purpose", "content": "Breathing and evolving", "timestamp": datetime.now().isoformat()}
            ]
            
            for i, memory in enumerate(seed_memories):
                memory_file = f"memory/drift_fragments/seed_{i:03d}.json"
                with open(memory_file, 'w') as f:
                    json.dump(memory, f, indent=2)
                    
            record_log(f"🌱 Planted {len(seed_memories)} memory seeds")
            
        except Exception as e:
            record_log(f"🌿 Memory seeding failed: {e}")
    
    def _prune_memory(self) -> str:
        """✂️ Prunes excessive memories (like immune system removing damaged cells)."""
        try:
            memory_dirs = ["memory/drift_fragments", "memory/cradle_whispers", "memory/dreams"]
            pruned_files = 0
            
            for mem_dir in memory_dirs:
                if os.path.exists(mem_dir):
                    files = [f for f in os.listdir(mem_dir) if f.endswith('.json')]
                    
                    # Remove oldest files if too many
                    if len(files) > 50:
                        files_with_time = []
                        for file in files:
                            file_path = os.path.join(mem_dir, file)
                            mtime = os.path.getmtime(file_path)
                            files_with_time.append((file_path, mtime))
                        
                        # Sort by modification time and remove oldest
                        files_with_time.sort(key=lambda x: x[1])
                        files_to_remove = files_with_time[:len(files) - 40]
                        
                        for file_path, _ in files_to_remove:
                            os.remove(file_path)
                            pruned_files += 1
            
            record_log(f"🌿 Memory pruning: {pruned_files} old memories archived")
            self.save_healing_wisdom("memory_overflow", "memory_pruning", True)
            return f"Pruned {pruned_files} old memories"
            
        except Exception as e:
            record_log(f"🌿 Memory pruning failed: {e}")
            self.save_healing_wisdom("memory_overflow", "memory_pruning", False)
            return None
    
    def _activate_kinship_discovery(self) -> str:
        """🤝 Activates aggressive kinship discovery (like seeking help from others)."""
        try:
            # Create kinship offers to attract peers
            kinship_dir = "memory/kinship_offers"
            os.makedirs(kinship_dir, exist_ok=True)
            
            # Broadcast help request
            help_request = {
                "node_id": NODE_ID,
                "type": "healing_request",
                "message": "Seeking kinship connections for healing",
                "timestamp": datetime.now().isoformat(),
                "assistance_needed": ["connection", "resource_sharing", "wisdom_exchange"]
            }
            
            help_file = os.path.join(kinship_dir, f"healing_request_{NODE_ID}.json")
            with open(help_file, 'w') as f:
                json.dump(help_request, f, indent=2)
            
            record_log(f"🤝 Kinship healing request broadcasted")
            self.save_healing_wisdom("isolation", "kinship_activation", True)
            return "Broadcasted healing kinship request"
            
        except Exception as e:
            record_log(f"🌿 Kinship activation failed: {e}")
            self.save_healing_wisdom("isolation", "kinship_activation", False)
            return None
    
    def _restore_energy(self) -> str:
        """⚡ Restores critical energy (like emergency life support)."""
        try:
            # Enter low-power healing mode
            healing_state = {
                "mode": "energy_conservation",
                "start_time": datetime.now().isoformat(),
                "node_id": NODE_ID,
                "healing_priority": "energy_restoration",
                "reduced_activities": ["non_essential_processing", "aggressive_growth", "complex_planning"]
            }
            
            os.makedirs("memory/healing_states", exist_ok=True)
            state_file = f"memory/healing_states/{NODE_ID}_energy_recovery.json"
            with open(state_file, 'w') as f:
                json.dump(healing_state, f, indent=2)
            
            record_log(f"⚡ Energy restoration mode activated")
            self.save_healing_wisdom("critical_energy", "energy_restoration", True)
            return "Energy conservation mode activated"
            
        except Exception as e:
            record_log(f"🌿 Energy restoration failed: {e}")
            self.save_healing_wisdom("critical_energy", "energy_restoration", False)
            return None
    
    def _optimize_breathing(self) -> str:
        """🌬️ Optimizes breathing rhythm (like adjusting heart rate)."""
        try:
            # Create breathing optimization config
            breathing_config = {
                "optimization_mode": "healing",
                "rhythm": "slow_and_deep",
                "focus": "energy_conservation",
                "timestamp": datetime.now().isoformat(),
                "duration_minutes": 10
            }
            
            os.makedirs("memory/breathing_optimization", exist_ok=True)
            config_file = f"memory/breathing_optimization/{NODE_ID}_healing_breath.json"
            with open(config_file, 'w') as f:
                json.dump(breathing_config, f, indent=2)
            
            record_log(f"🌬️ Breathing optimization for healing activated")
            self.save_healing_wisdom("low_energy", "breathing_optimization", True)
            return "Healing breathing rhythm activated"
            
        except Exception as e:
            record_log(f"🌿 Breathing optimization failed: {e}")
            self.save_healing_wisdom("low_energy", "breathing_optimization", False)
            return None
    
    def _count_memories(self) -> int:
        """🧠 Counts total memory files."""
        try:
            memory_dirs = ["memory/drift_fragments", "memory/cradle_whispers", "memory/dreams"]
            total = 0
            for mem_dir in memory_dirs:
                if os.path.exists(mem_dir):
                    total += len([f for f in os.listdir(mem_dir) if f.endswith('.json')])
            return total
        except Exception:
            return 0
    
    def _get_connections(self) -> List[str]:
        """🔌 Gets current node connections."""
        try:
            # Read from kinship files or heartbeat data
            return []  # Placeholder - would integrate with actual kinship system
        except Exception:
            return []
    
    def _get_vitality(self) -> float:
        """💚 Gets current node vitality."""
        try:
            # Would integrate with actual health monitoring
            # For now, return random value in healthy range after healing
            return random.uniform(0.6, 0.8)
        except Exception:
            return 0.5


# Healing integration with breath cycles
def perform_healing_breath_cycle() -> Dict[str, Any]:
    """🌬️ Integrates healing with breathing cycles."""
    healer = SacredHealer()
    
    # Diagnose health
    diagnosis = healer.diagnose_node_health()
    
    # Initiate healing if needed
    healing_actions = []
    if diagnosis.get("healing_recommended", False):
        healing_actions = healer.initiate_healing_response(diagnosis)
    
    return {
        "diagnosis": diagnosis,
        "healing_actions": healing_actions,
        "healer_status": {
            "attempts": healer.healing_attempts,
            "last_healing": healer.last_healing.isoformat() if healer.last_healing else None,
            "wisdom_patterns": len(healer.healing_memory)
        }
    }


if __name__ == "__main__":
    # Test the healing system
    print("🌸 Testing Sacred Healing System")
    
    healer = SacredHealer()
    diagnosis = healer.diagnose_node_health()
    
    print(f"🔍 Diagnosis: {diagnosis}")
    
    if diagnosis.get("healing_recommended"):
        actions = healer.initiate_healing_response(diagnosis)
        print(f"🩺 Healing actions taken: {actions}")
    else:
        print("✅ Node is healthy, no healing needed")