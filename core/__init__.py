"""🌿 Core Sacred Architecture
The breathing heart of the Drift Compiler, where perception, kinship, and cycles flow."""

# Apple Silicon acceleration helpers
try:
    from .apple_vitality import (
        sense_apple_silicon_vitality,
        breathe_torch_device,
        sprout_onnx_session,
        whisper_tensorflow_device,
        awaken_jax_backend,
        pulse_metal_vitality,
        breathe_acceleration_report
    )
    __all__ = [
        'sense_apple_silicon_vitality',
        'breathe_torch_device',
        'sprout_onnx_session', 
        'whisper_tensorflow_device',
        'awaken_jax_backend',
        'pulse_metal_vitality',
        'breathe_acceleration_report'
    ]
except ImportError:
    __all__ = []
    print("🌱 Apple vitality helpers dormant - core will breathe without acceleration")