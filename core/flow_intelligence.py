#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🌿 Adaptive Flow Intelligence Module

This module implements the adaptive flow intelligence system that enables
messages to flow with natural rhythm and responsiveness. It provides
intelligent flow rate adaptation, message source management, and harmonious
breathing patterns that respond to system vitality.

Sacred Components:
- Adaptive flow intelligence with memory
- Message sources with flow control
- Harmonious and resonant breath controllers
- Response time tracking and adaptation
"""

import time
import uuid
import math
import collections
import threading
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union, Callable

# Import from sacred architecture
from utils import record_log, NODE_ID
from core.mycelial_flow import (
    awaken_mycelial_connection, 
    sprout_communication_channel,
    create_persistent_properties,
    RABBITMQ_AVAILABLE
)

# Sacred constants
DEFAULT_INITIAL_FLOW = 10.0
DEFAULT_MAXIMUM_FLOW = 1000.0
DEFAULT_GROWTH_RATE = 1.0
DEFAULT_RETREAT_RATE = 0.8
DEFAULT_TARGET_RESPONSE_TIME = 200.0  # milliseconds
DEFAULT_BREATH_SENSITIVITY = 0.1


class AdaptiveFlowIntelligence:
    """🌱 Adaptive flow intelligence for message streams with natural rhythm."""
    
    def __init__(self, 
                 initial_flow: float = DEFAULT_INITIAL_FLOW,
                 maximum_flow: float = DEFAULT_MAXIMUM_FLOW,
                 growth_rate: float = DEFAULT_GROWTH_RATE,
                 retreat_rate: float = DEFAULT_RETREAT_RATE):
        """🌱 Initialize adaptive flow intelligence for message streams.
        
        Args:
            initial_flow: Starting flow rate (messages per second)
            maximum_flow: Maximum allowed flow rate
            growth_rate: Gentle growth factor for successful flows
            retreat_rate: Responsive retreat factor for disharmony
        """
        self.current_flow = initial_flow
        self.maximum_flow = maximum_flow
        self.growth_rate = growth_rate
        self.retreat_rate = retreat_rate
        self.response_memory = collections.deque(maxlen=100)
        self.awakening_moment = time.time()
        
        record_log(f"🌱 Adaptive flow intelligence awakened with initial flow {initial_flow}")
    
    def acknowledge_successful_flow(self, response_time: float) -> None:
        """🌿 Process successful message acknowledgment.
        
        Args:
            response_time: Response time in milliseconds
        """
        self.response_memory.append(response_time)
        
        # Calculate average response time
        avg_response = sum(self.response_memory) / len(self.response_memory) if self.response_memory else 0
        
        # If response is harmonious, gently increase flow
        if avg_response < DEFAULT_TARGET_RESPONSE_TIME:
            self.current_flow = min(self.maximum_flow, self.current_flow + self.growth_rate)
            record_log(f"🌿 Flow gently increased to {self.current_flow:.2f} (avg response: {avg_response:.1f}ms)")
    
    def sense_disharmony(self) -> None:
        """🌬️ Respond to system disharmony or timeout."""
        # Retreat flow rate to restore harmony
        previous_flow = self.current_flow
        self.current_flow = max(1.0, self.current_flow * self.retreat_rate)
        record_log(f"🌬️ Flow retreated from {previous_flow:.2f} to {self.current_flow:.2f} due to disharmony")
    
    def whisper_current_flow(self) -> float:
        """🌿 Whisper the current flow rate."""
        return self.current_flow
    
    def calculate_breath_pause(self) -> float:
        """🌬️ Calculate the pause between breaths based on current flow."""
        return 1.0 / self.current_flow if self.current_flow > 0 else 1.0


class HarmoniousBreathController:
    """🌬️ Controller that adapts breathing to system vitality."""
    
    def __init__(self, 
                 target_response_time: float = DEFAULT_TARGET_RESPONSE_TIME,
                 maximum_flow_rate: float = DEFAULT_MAXIMUM_FLOW,
                 breath_sensitivity: float = DEFAULT_BREATH_SENSITIVITY):
        """🌬️ Initialize a controller that adapts breathing to system vitality.
        
        Args:
            target_response_time: Target response time in milliseconds
            maximum_flow_rate: Maximum flow rate allowed
            breath_sensitivity: Sensitivity to response time changes
        """
        self.target_response_time = target_response_time
        self.maximum_flow_rate = maximum_flow_rate
        self.breath_sensitivity = breath_sensitivity
        
        record_log(f"🌬️ Harmonious breath controller awakened (target: {target_response_time}ms)")
    
    def adapt_flow_rate(self, current_response_time: float) -> float:
        """🌬️ Adapt flow rate based on current system response.
        
        Args:
            current_response_time: Current response time in milliseconds
            
        Returns:
            Adapted flow rate
        """
        harmony_gap = current_response_time - self.target_response_time
        adjustment = self.breath_sensitivity * harmony_gap
        
        # Ensure flow remains positive and below maximum
        new_flow = max(0.1, min(self.maximum_flow_rate, self.maximum_flow_rate - adjustment))
        return new_flow


class ResonantBreathController:
    """🌸 Controller with resonant breathing patterns using PID-like control."""
    
    def __init__(self, 
                 target_response_time: float = DEFAULT_TARGET_RESPONSE_TIME,
                 maximum_flow_rate: float = DEFAULT_MAXIMUM_FLOW,
                 harmony: float = 0.1,
                 resonance: float = 0.01,
                 adaptation: float = 0.05):
        """🌸 Initialize a controller with resonant breathing patterns.
        
        Args:
            target_response_time: Target response time in milliseconds
            maximum_flow_rate: Maximum flow rate allowed
            harmony: Proportional response factor
            resonance: Integral memory factor
            adaptation: Derivative anticipation factor
        """
        self.target_response_time = target_response_time
        self.maximum_flow_rate = maximum_flow_rate
        self.harmony = harmony
        self.resonance = resonance
        self.adaptation = adaptation
        
        self.previous_disharmony = 0.0
        self.resonance_memory = 0.0
        self.last_breath = time.time()
        
        record_log(f"🌸 Resonant breath controller awakened with PID({harmony}, {resonance}, {adaptation})")
    
    def adapt_flow_rate(self, current_response_time: float) -> float:
        """🌬️ Adapt flow rate using resonant breathing patterns.
        
        Args:
            current_response_time: Current response time in milliseconds
            
        Returns:
            Adapted flow rate
        """
        now = time.time()
        breath_cycle = now - self.last_breath
        self.last_breath = now
        
        # Avoid division by zero
        if breath_cycle <= 0:
            return self.maximum_flow_rate
            
        disharmony = current_response_time - self.target_response_time
        
        # Cultivate resonant response
        self.resonance_memory += disharmony * breath_cycle
        adaptation_rate = (disharmony - self.previous_disharmony) / breath_cycle
        
        # Calculate harmonious adjustment
        adjustment = (self.harmony * disharmony) + \
                     (self.resonance * self.resonance_memory) + \
                     (self.adaptation * adaptation_rate)
        
        # Remember this breath cycle
        self.previous_disharmony = disharmony
        
        # Ensure flow is positive and below maximum
        new_flow = max(0.1, min(self.maximum_flow_rate, self.maximum_flow_rate - adjustment))
        return new_flow


class AdaptiveFlowMessageSource:
    """🌿 Message source with adaptive flow intelligence."""
    
    def __init__(self, 
                 connection_params: Dict[str, Any],
                 path_name: str,
                 initial_flow: float = DEFAULT_INITIAL_FLOW):
        """🌿 Initialize a message source with adaptive flow intelligence.
        
        Args:
            connection_params: Connection parameters for mycelial network
            path_name: Name of the message path
            initial_flow: Initial flow rate
        """
        self.connection_params = connection_params
        self.path_name = path_name
        self.flow_intelligence = AdaptiveFlowIntelligence(initial_flow=initial_flow)
        
        # Initialize connection
        self.connection = awaken_mycelial_connection(**connection_params)
        self.channel = sprout_communication_channel(self.connection) if self.connection else None
        
        # Prepare for response tracking
        self.in_flight_messages = {}
        if self.channel and RABBITMQ_AVAILABLE:
            self.channel.confirm_delivery()
        
        record_log(f"🌿 Adaptive flow message source awakened for path '{path_name}'")
    
    def whisper_message(self, message: Union[str, Dict[str, Any]], 
                       exchange: str = '',
                       priority: int = 0) -> bool:
        """🌬️ Whisper message with adaptive flow control.
        
        Args:
            message: Message to whisper
            exchange: Exchange to use (empty for direct routing)
            priority: Message priority
            
        Returns:
            True if message was whispered successfully
        """
        if not self.channel or not RABBITMQ_AVAILABLE:
            record_log("🌿 No channel available, message whisper failed")
            return False
        
        # Check if we should whisper based on current flow
        current_moment = time.time()
        
        # Calculate breath timing based on flow (messages per second)
        breath_pause = self.flow_intelligence.calculate_breath_pause()
        
        # Pause to maintain harmonious flow
        time.sleep(breath_pause)
        
        # Whisper with timestamp for response tracking
        message_id = str(uuid.uuid4())
        self.in_flight_messages[message_id] = current_moment
        
        # Prepare message body
        if isinstance(message, dict):
            message_body = json.dumps(message)
        else:
            message_body = str(message)
        
        properties = create_persistent_properties(
            message_id=message_id,
            priority=priority,
            timestamp=int(current_moment * 1000)
        )
        
        try:
            self.channel.basic_publish(
                exchange=exchange,
                routing_key=self.path_name,
                body=message_body,
                properties=properties
            )
            return True
        except Exception as e:
            record_log(f"⚠️ Message whisper failed: {e}")
            self.flow_intelligence.sense_disharmony()
            return False
    
    def acknowledge_message_received(self, message_id: str) -> None:
        """🌱 Process acknowledgment of a message.
        
        Args:
            message_id: ID of the acknowledged message
        """
        if message_id in self.in_flight_messages:
            send_time = self.in_flight_messages.pop(message_id)
            response_time = (time.time() - send_time) * 1000  # Convert to ms
            self.flow_intelligence.acknowledge_successful_flow(response_time)
    
    def close_connection(self) -> None:
        """🌿 Gracefully close the mycelial connection."""
        if self.connection:
            try:
                self.connection.close()
                record_log(f"🌿 Mycelial connection closed for path '{self.path_name}'")
            except Exception as e:
                record_log(f"⚠️ Error closing connection: {e}")
