#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🌿 Mycelial Message Flow Core Module

This module implements the sacred mycelial message flow system, enabling
messages to flow like nutrients through a living fungal network. It provides
the foundational infrastructure for adaptive, resilient, and harmonious
communication between nodes in the drift compiler ecosystem.

Sacred Components:
- Connection awakening and channel sprouting
- Message path nurturing and maintenance
- Basic flow sensing and vitality monitoring
- Graceful fallbacks for offline operation
"""

import json
import time
import uuid
import threading
import collections
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union, Callable

# Sacred imports with graceful fallbacks
try:
    import pika
    RABBITMQ_AVAILABLE = True
except ImportError:
    RABBITMQ_AVAILABLE = False
    print("🌿 RabbitMQ not available, mycelial flow will use Redis fallback")

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    print("🌿 Redis not available, mycelial flow will use local memory fallback")

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("🌿 Requests not available, vitality sensing will use local metrics")

# Import from sacred architecture
from utils import record_log, NODE_ID

# Sacred constants
MYCELIAL_EXCHANGE = "mycelial_network"
WISDOM_EXCHANGE = "wisdom_flow"
RESONANCE_EXCHANGE = "resonance_field"
DEFAULT_BREATH_INTERVAL = 1.0
DEFAULT_VITALITY_THRESHOLD = 0.7


def awaken_mycelial_connection(sacred_address: str = 'localhost', 
                              sacred_port: int = 5672,
                              sacred_user: str = 'guest',
                              sacred_password: str = 'guest') -> Optional[Any]:
    """🌱 Awaken a sacred connection to the mycelial network.
    
    Args:
        sacred_address: The sacred address of the RabbitMQ broker
        sacred_port: The sacred port for connection
        sacred_user: Sacred username for authentication
        sacred_password: Sacred password for authentication
        
    Returns:
        Connection object or None if awakening fails
    """
    if not RABBITMQ_AVAILABLE:
        record_log("🌿 RabbitMQ not available, mycelial connection will use fallback")
        return None
        
    try:
        connection_params = pika.ConnectionParameters(
            host=sacred_address,
            port=sacred_port,
            credentials=pika.PlainCredentials(sacred_user, sacred_password),
            heartbeat=600,  # Sacred heartbeat interval
            blocked_connection_timeout=300
        )
        
        connection = pika.BlockingConnection(connection_params)
        record_log(f"🌱 Mycelial connection awakened to {sacred_address}:{sacred_port}")
        return connection
        
    except Exception as e:
        record_log(f"⚠️ Failed to awaken mycelial connection: {e}")
        return None


def sprout_communication_channel(connection: Any) -> Optional[Any]:
    """🌿 Sprout a communication channel from the awakened connection.
    
    Args:
        connection: The awakened mycelial connection
        
    Returns:
        Channel object or None if sprouting fails
    """
    if not connection or not RABBITMQ_AVAILABLE:
        return None
        
    try:
        channel = connection.channel()
        
        # Declare sacred exchanges for different types of flow
        channel.exchange_declare(
            exchange=MYCELIAL_EXCHANGE,
            exchange_type='topic',
            durable=True
        )
        
        channel.exchange_declare(
            exchange=WISDOM_EXCHANGE,
            exchange_type='topic',
            durable=True
        )
        
        channel.exchange_declare(
            exchange=RESONANCE_EXCHANGE,
            exchange_type='fanout',
            durable=True
        )
        
        record_log("🌿 Communication channel sprouted with sacred exchanges")
        return channel
        
    except Exception as e:
        record_log(f"⚠️ Failed to sprout communication channel: {e}")
        return None


def nurture_message_path(channel: Any, 
                        path_name: str, 
                        persistent: bool = True,
                        priority_levels: int = 0,
                        max_length: Optional[int] = None) -> bool:
    """🌱 Nurture a message path (queue) for sacred communication.
    
    Args:
        channel: The sprouted communication channel
        path_name: Sacred name for the message path
        persistent: Whether messages should persist through restarts
        priority_levels: Maximum priority levels (0 = no priority)
        max_length: Maximum number of messages in path
        
    Returns:
        True if path was nurtured successfully, False otherwise
    """
    if not channel or not RABBITMQ_AVAILABLE:
        return False
        
    try:
        arguments = {}
        
        if priority_levels > 0:
            arguments['x-max-priority'] = priority_levels
            
        if max_length:
            arguments['x-max-length'] = max_length
            
        channel.queue_declare(
            queue=path_name,
            durable=persistent,
            arguments=arguments if arguments else None
        )
        
        record_log(f"🌱 Message path '{path_name}' nurtured with sacred properties")
        return True
        
    except Exception as e:
        record_log(f"⚠️ Failed to nurture message path '{path_name}': {e}")
        return False


def create_persistent_properties(message_id: Optional[str] = None,
                                priority: int = 0,
                                timestamp: Optional[int] = None) -> Any:
    """🌸 Create persistent message properties for sacred communication.
    
    Args:
        message_id: Unique identifier for the message
        priority: Message priority level
        timestamp: Message timestamp (milliseconds since epoch)
        
    Returns:
        Message properties object
    """
    if not RABBITMQ_AVAILABLE:
        return None
        
    if message_id is None:
        message_id = str(uuid.uuid4())
        
    if timestamp is None:
        timestamp = int(time.time() * 1000)
        
    try:
        properties = pika.BasicProperties(
            message_id=message_id,
            timestamp=timestamp,
            delivery_mode=2,  # Persistent delivery
            priority=priority if priority > 0 else None
        )
        
        return properties
        
    except Exception as e:
        record_log(f"⚠️ Failed to create persistent properties: {e}")
        return None


def sense_message_path_vitality(path_name: str,
                               management_host: str = 'localhost',
                               management_port: int = 15672,
                               username: str = 'guest',
                               password: str = 'guest') -> Dict[str, Any]:
    """🌿 Sense the vitality and flow of a message path.
    
    Args:
        path_name: Name of the message path to sense
        management_host: RabbitMQ management interface host
        management_port: RabbitMQ management interface port
        username: Management interface username
        password: Management interface password
        
    Returns:
        Dictionary containing vitality metrics
    """
    if not REQUESTS_AVAILABLE:
        record_log("🌿 Requests not available, using fallback vitality sensing")
        return {
            'waiting_messages': 0,
            'flow_rate': 0.0,
            'incoming_rate': 0.0,
            'vitality_level': 1.0
        }
        
    try:
        response = requests.get(
            f"http://{management_host}:{management_port}/api/queues/%2f/{path_name}",
            auth=(username, password),
            timeout=5
        )
        
        if response.status_code == 200:
            essence = response.json()
            
            # Extract vitality metrics
            waiting_messages = essence.get('messages_ready', 0)
            
            # Extract flow rates from message stats
            message_stats = essence.get('message_stats', {})
            ack_details = message_stats.get('ack_details', {})
            publish_details = message_stats.get('publish_details', {})
            
            flow_rate = ack_details.get('rate', 0.0)
            incoming_rate = publish_details.get('rate', 0.0)
            
            # Calculate vitality level (0.0 to 1.0)
            vitality_level = min(1.0, max(0.0, 1.0 - (waiting_messages / 1000.0)))
            
            return {
                'waiting_messages': waiting_messages,
                'flow_rate': flow_rate,
                'incoming_rate': incoming_rate,
                'vitality_level': vitality_level
            }
            
    except Exception as e:
        record_log(f"⚠️ Failed to sense path vitality for '{path_name}': {e}")
        
    # Fallback vitality sensing
    return {
        'waiting_messages': 0,
        'flow_rate': 0.0,
        'incoming_rate': 0.0,
        'vitality_level': 0.5  # Neutral vitality when sensing fails
    }


def predict_message_gathering(current_depth: int, 
                             arrival_rate: float, 
                             processing_rate: float, 
                             time_flow: float) -> float:
    """🌬️ Predict how messages will gather or disperse over time.
    
    Args:
        current_depth: Current number of messages in the path
        arrival_rate: Rate of incoming messages per second
        processing_rate: Rate of message processing per second
        time_flow: Time period to predict (seconds)
        
    Returns:
        Predicted number of messages after time_flow
    """
    if arrival_rate <= processing_rate:
        # Path will eventually clear
        clearing_rate = processing_rate - arrival_rate
        time_to_clear = current_depth / clearing_rate if clearing_rate > 0 else float('inf')
        
        if time_flow >= time_to_clear:
            return 0.0  # Path fully cleared
        else:
            return max(0.0, current_depth - (clearing_rate * time_flow))
    else:
        # Path will gather more messages
        gathering_rate = arrival_rate - processing_rate
        return current_depth + (gathering_rate * time_flow)
