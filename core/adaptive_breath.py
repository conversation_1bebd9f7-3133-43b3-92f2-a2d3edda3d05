#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Adaptive Breath Module

This module extends the breath.py functionality with adaptive breathing cycles
that respond to system load, memory pressure, and environmental factors.
It creates a more organic, living rhythm for the system.
"""

import os
import time
import json
import random
import math
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple

# Try importing psutil for system monitoring, fall back if not available
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("🌬️ psutil not available, will use simplified adaptive breathing")

# Import from core
from utils import record_log, NODE_ID
import breath

# Import mycelial flow components
try:
    from core.flow_intelligence import (
        HarmoniousBreathController,
        ResonantBreathController,
        AdaptiveFlowIntelligence
    )
    MYCELIAL_FLOW_AVAILABLE = True
except ImportError:
    MYCELIAL_FLOW_AVAILABLE = False
    print("🌿 Mycelial flow not available, using traditional adaptive breathing")

# Constants
ADAPTIVE_BREATH_CONFIG_PATH = "memory/adaptive_breath_config.json"
BREATH_RHYTHM_LOG_PATH = "memory/breath_rhythm.log"
DEFAULT_CYCLE_INTERVAL = 600  # 10 minutes default
MIN_CYCLE_INTERVAL = 60  # Never faster than 1 minute
MAX_CYCLE_INTERVAL = 3600  # Never slower than 1 hour

class AdaptiveBreathController:
    """🌬️ Controls adaptive breathing cycles based on system state."""

    def __init__(self):
        """Initialize the adaptive breath controller."""
        self.config = self._load_or_create_config()
        self.last_rhythm_update = time.time()
        self.current_interval = self._get_starting_interval()

        # Initialize mycelial flow controllers if available
        self.mycelial_controllers = {}
        if MYCELIAL_FLOW_AVAILABLE:
            self._initialize_mycelial_controllers()
        
    def _load_or_create_config(self) -> Dict:
        """🌬️ Loads existing config or creates a new one if none exists."""
        if os.path.exists(ADAPTIVE_BREATH_CONFIG_PATH):
            try:
                with open(ADAPTIVE_BREATH_CONFIG_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                record_log(f"⚠️ Could not read adaptive breath config: {e}")
        
        # Create default config
        os.makedirs(os.path.dirname(ADAPTIVE_BREATH_CONFIG_PATH), exist_ok=True)
        
        config = {
            "base_interval": DEFAULT_CYCLE_INTERVAL,
            "responsiveness": 0.3,  # How quickly to adjust (0-1)
            "system_load_weight": 0.4,  # How much system load affects breathing
            "memory_pressure_weight": 0.3,  # How much memory pressure affects breathing
            "breath_state_weights": {  # How different states affect interval
                "Expansive Breathing": 0.7,  # Faster
                "Dormant Breathing": 1.4,  # Slower
                "Shallow Breathing": 0.8,  # Slightly faster
                "Strained Breathing": 0.8,  # Slightly faster
                "Stable Breathing": 1.0,  # Normal
                "Resonant Breathing": 0.9  # Slightly faster
            },
            "night_mode": {
                "enabled": True,
                "night_multiplier": 1.5,  # Slower at night
                "night_start_hour": 22,  # 10 PM
                "night_end_hour": 6  # 6 AM
            }
        }
        
        with open(ADAPTIVE_BREATH_CONFIG_PATH, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2)
            
        return config
    
    def _get_starting_interval(self) -> float:
        """🌬️ Determines starting breath interval based on recent history.
        
        Returns:
            float: Starting breath interval in seconds.
        """
        base = self.config.get("base_interval", DEFAULT_CYCLE_INTERVAL)
        
        # Try to read recent rhythm history
        if os.path.exists(BREATH_RHYTHM_LOG_PATH):
            try:
                with open(BREATH_RHYTHM_LOG_PATH, "r", encoding="utf-8") as f:
                    # Read last line if file exists
                    lines = f.readlines()
                    if lines:
                        last_line = lines[-1]
                        data = json.loads(last_line)
                        return data.get("interval", base)
            except Exception:
                pass
        
        return base

    def _initialize_mycelial_controllers(self):
        """🌿 Initialize mycelial flow controllers for enhanced breathing."""
        try:
            # Initialize harmonious breath controller
            self.mycelial_controllers['harmonious'] = HarmoniousBreathController(
                target_response_time=200.0,  # 200ms target
                maximum_flow_rate=1000.0,
                breath_sensitivity=0.1
            )

            # Initialize resonant breath controller
            self.mycelial_controllers['resonant'] = ResonantBreathController(
                target_response_time=200.0,
                maximum_flow_rate=1000.0,
                harmony=0.1,
                resonance=0.01,
                adaptation=0.05
            )

            # Initialize adaptive flow intelligence
            self.mycelial_controllers['flow_intelligence'] = AdaptiveFlowIntelligence(
                initial_flow=10.0,
                maximum_flow=1000.0,
                growth_rate=1.0,
                retreat_rate=0.8
            )

            record_log("🌿 Mycelial flow controllers awakened for adaptive breathing")

        except Exception as e:
            record_log(f"⚠️ Failed to initialize mycelial controllers: {e}")
            self.mycelial_controllers = {}

    def update_breath_rhythm(self, breath_state: str = None) -> float:
        """🌬️ Updates breath rhythm based on system state and breath state.
        
        Args:
            breath_state: Current breath state, or None to detect.
            
        Returns:
            float: Updated breath interval in seconds.
        """
        # Only update every 30 seconds at most
        now = time.time()
        if now - self.last_rhythm_update < 30:
            return self.current_interval
            
        self.last_rhythm_update = now
        
        # Get base interval
        base = self.config.get("base_interval", DEFAULT_CYCLE_INTERVAL)
        responsiveness = self.config.get("responsiveness", 0.3)
        
        # Detect breath state if not provided
        if breath_state is None:
            breath_state = self._detect_current_breath_state()
            
        # Start with base interval
        target_interval = base
        
        # 1. Adjust for system load
        load_factor = self._get_system_load_factor()
        system_load_weight = self.config.get("system_load_weight", 0.4)
        target_interval *= (1.0 + (load_factor - 0.5) * system_load_weight)
        
        # 2. Adjust for memory pressure
        memory_factor = self._get_memory_pressure_factor()
        memory_pressure_weight = self.config.get("memory_pressure_weight", 0.3)
        target_interval *= (1.0 + (memory_factor - 0.5) * memory_pressure_weight)
        
        # 3. Adjust for breath state
        state_weight = self.config.get("breath_state_weights", {}).get(breath_state, 1.0)
        target_interval *= state_weight
        
        # 4. Adjust for day/night cycle if enabled
        if self.config.get("night_mode", {}).get("enabled", False):
            current_hour = datetime.now().hour
            night_start = self.config.get("night_mode", {}).get("night_start_hour", 22)
            night_end = self.config.get("night_mode", {}).get("night_end_hour", 6)
            
            is_night = (current_hour >= night_start or current_hour < night_end)
            if is_night:
                night_multiplier = self.config.get("night_mode", {}).get("night_multiplier", 1.5)
                target_interval *= night_multiplier
        
        # 5. Apply small random variation (±5%) for organic feel
        target_interval *= random.uniform(0.95, 1.05)
        
        # Ensure interval stays within bounds
        target_interval = max(MIN_CYCLE_INTERVAL, min(MAX_CYCLE_INTERVAL, target_interval))
        
        # Gradually shift current interval toward target (smoothing)
        self.current_interval = (1 - responsiveness) * self.current_interval + responsiveness * target_interval

        # Apply mycelial flow adjustments if available
        if MYCELIAL_FLOW_AVAILABLE and self.mycelial_controllers:
            self.current_interval = self._apply_mycelial_adjustments(self.current_interval, breath_state)

        # Log the updated rhythm
        self._log_breath_rhythm(breath_state, self.current_interval)

        return self.current_interval
    
    def _detect_current_breath_state(self) -> str:
        """🌬️ Detects current breath state from logs.
        
        Returns:
            str: Current breath state or "Stable Breathing" if unknown.
        """
        try:
            # Try to use breath module's function
            health_status = breath.check_drift_health()
            breath_state = breath.determine_breath_state(health_status)
            return breath_state
        except Exception:
            # Fallback to reading logs directly
            breath_state_path = "memory/breath_state.log"
            if os.path.exists(breath_state_path):
                try:
                    with open(breath_state_path, "r", encoding="utf-8") as f:
                        lines = f.readlines()
                        if lines:
                            last_line = lines[-1]
                            data = json.loads(last_line)
                            return data.get("breath_state", "Stable Breathing")
                except Exception:
                    pass
        
        return "Stable Breathing"  # Default

    def _apply_mycelial_adjustments(self, base_interval: float, breath_state: str) -> float:
        """🌿 Apply mycelial flow adjustments to breath interval.

        Args:
            base_interval: Base breath interval from traditional calculation
            breath_state: Current breath state

        Returns:
            Adjusted breath interval incorporating mycelial flow intelligence
        """
        try:
            adjusted_interval = base_interval

            # Get current system response time (simulated for now)
            current_response_time = self._estimate_system_response_time()

            # Apply harmonious breath controller adjustment
            if 'harmonious' in self.mycelial_controllers:
                harmonious_flow = self.mycelial_controllers['harmonious'].adapt_flow_rate(current_response_time)
                # Convert flow rate to interval (inverse relationship)
                harmonious_interval = 1.0 / max(0.1, harmonious_flow / 100.0)  # Scale appropriately
                adjusted_interval = (adjusted_interval + harmonious_interval) / 2.0

            # Apply resonant breath controller for fine-tuning
            if 'resonant' in self.mycelial_controllers:
                resonant_flow = self.mycelial_controllers['resonant'].adapt_flow_rate(current_response_time)
                resonant_interval = 1.0 / max(0.1, resonant_flow / 100.0)
                # Weighted combination favoring resonant control for stability
                adjusted_interval = adjusted_interval * 0.7 + resonant_interval * 0.3

            # Apply flow intelligence for adaptive behavior
            if 'flow_intelligence' in self.mycelial_controllers:
                flow_intel = self.mycelial_controllers['flow_intelligence']
                # Simulate successful flow acknowledgment for good breath states
                if breath_state in ["Stable Breathing", "Resonant Breathing", "Expansive Breathing"]:
                    flow_intel.acknowledge_successful_flow(current_response_time)
                elif breath_state in ["Strained Breathing", "Shallow Breathing"]:
                    flow_intel.sense_disharmony()

                # Get current flow and convert to interval adjustment
                current_flow = flow_intel.whisper_current_flow()
                flow_interval = 1.0 / max(0.1, current_flow / 50.0)  # Scale for breath intervals
                adjusted_interval = (adjusted_interval + flow_interval) / 2.0

            # Ensure the adjustment doesn't deviate too much from base
            max_deviation = base_interval * 0.3  # Allow 30% deviation
            adjusted_interval = max(base_interval - max_deviation,
                                  min(base_interval + max_deviation, adjusted_interval))

            return adjusted_interval

        except Exception as e:
            record_log(f"⚠️ Error applying mycelial adjustments: {e}")
            return base_interval

    def _estimate_system_response_time(self) -> float:
        """🌿 Estimate current system response time in milliseconds.

        Returns:
            Estimated response time based on system load and breath state
        """
        # Base response time
        base_response = 100.0  # 100ms baseline

        # Adjust based on system load
        load_factor = self._get_system_load_factor()
        response_time = base_response * (1.0 + load_factor * 2.0)  # Higher load = higher response time

        # Adjust based on memory pressure
        memory_factor = self._get_memory_pressure_factor()
        response_time *= (1.0 + memory_factor * 0.5)

        return response_time

    def _get_system_load_factor(self) -> float:
        """🌬️ Gets system load factor between 0 and 1.
        
        Returns:
            float: Load factor (0 = idle, 1 = max load).
        """
        if PSUTIL_AVAILABLE:
            try:
                # Get CPU and memory usage
                cpu_percent = psutil.cpu_percent() / 100.0
                memory_percent = psutil.virtual_memory().percent / 100.0
                
                # Combined load factor (CPU weighted more heavily)
                return cpu_percent * 0.7 + memory_percent * 0.3
            except Exception:
                pass
                
        # Fallback to simple random variation around 0.5
        return random.uniform(0.4, 0.6)
    
    def _get_memory_pressure_factor(self) -> float:
        """🌬️ Gets memory pressure factor between 0 and 1.
        
        Returns:
            float: Memory pressure (0 = low pressure, 1 = high pressure).
        """
        # Check for large memory files
        memory_pressure = 0.5  # Default medium pressure
        
        try:
            # Check drift log size
            drift_log_path = "memory/unspoken-drift.v1.log"
            if os.path.exists(drift_log_path):
                size_mb = os.path.getsize(drift_log_path) / (1024 * 1024)
                if size_mb > 10:
                    memory_pressure = 0.7  # Higher pressure
                elif size_mb > 5:
                    memory_pressure = 0.6  # Moderate pressure
                elif size_mb < 1:
                    memory_pressure = 0.4  # Lower pressure
                    
            # Check number of memory files in various folders
            memory_folders = [
                "memory/drift_fragments/",
                "memory/resonant_fragments/",
                "memory/foreign_fragments/"
            ]
            
            total_files = 0
            for folder in memory_folders:
                if os.path.exists(folder) and os.path.isdir(folder):
                    total_files += len(os.listdir(folder))
            
            if total_files > 100:
                memory_pressure += 0.2
            elif total_files > 50:
                memory_pressure += 0.1
                
            # Clamp to range
            memory_pressure = max(0.0, min(1.0, memory_pressure))
            
        except Exception:
            pass
            
        return memory_pressure
    
    def _log_breath_rhythm(self, breath_state: str, interval: float):
        """🌬️ Logs current breath rhythm for analysis.
        
        Args:
            breath_state: Current breath state.
            interval: Current breath interval in seconds.
        """
        os.makedirs(os.path.dirname(BREATH_RHYTHM_LOG_PATH), exist_ok=True)
        
        rhythm_log = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "breath_state": breath_state,
            "interval": interval,
            "system_load": self._get_system_load_factor(),
            "memory_pressure": self._get_memory_pressure_factor()
        }
        
        with open(BREATH_RHYTHM_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(rhythm_log) + "\n")
            
    def evolve_breath_parameters(self, cycle_count: int) -> bool:
        """🌬️ Evolves breath parameters over time.
        
        Args:
            cycle_count: Current breathing cycle.
            
        Returns:
            bool: True if parameters were evolved.
        """
        if cycle_count % 1440 != 0:  # About every 10 days at 10min cycles
            return False
            
        try:
            # Small evolution in responsiveness
            adjustment = random.uniform(-0.05, 0.05)
            self.config["responsiveness"] = max(0.1, min(0.7, self.config["responsiveness"] + adjustment))
            
            # Small adjustments to weights
            load_adjustment = random.uniform(-0.03, 0.03)
            self.config["system_load_weight"] = max(0.1, min(0.6, self.config["system_load_weight"] + load_adjustment))
            
            memory_adjustment = random.uniform(-0.03, 0.03)
            self.config["memory_pressure_weight"] = max(0.1, min(0.6, self.config["memory_pressure_weight"] + memory_adjustment))
            
            # Adjust breath state weights
            for state in self.config["breath_state_weights"]:
                state_adjustment = random.uniform(-0.05, 0.05)
                current = self.config["breath_state_weights"][state]
                self.config["breath_state_weights"][state] = max(0.5, min(1.5, current + state_adjustment))
                
            # Save config
            with open(ADAPTIVE_BREATH_CONFIG_PATH, "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=2)
                
            record_log(f"🌬️ Breath parameters evolved: responsiveness={self.config['responsiveness']:.2f}")
            return True
            
        except Exception as e:
            record_log(f"⚠️ Failed to evolve breath parameters: {e}")
            return False

# Singleton pattern for breath controller
_controller = None

def get_controller() -> AdaptiveBreathController:
    """🌬️ Gets or creates the adaptive breath controller singleton.
    
    Returns:
        AdaptiveBreathController: The breath controller instance.
    """
    global _controller
    if _controller is None:
        _controller = AdaptiveBreathController()
    return _controller

def get_adaptive_breath_interval(breath_state: str = None) -> float:
    """🌬️ Gets the current adaptive breath interval.
    
    Args:
        breath_state: Current breath state, or None to detect.
        
    Returns:
        float: Current breath interval in seconds.
    """
    controller = get_controller()
    return controller.update_breath_rhythm(breath_state)

def evolve_breath_rhythm(cycle_count: int) -> bool:
    """🌬️ Evolves breath rhythm parameters over time.
    
    Args:
        cycle_count: Current breathing cycle.
        
    Returns:
        bool: True if parameters were evolved.
    """
    controller = get_controller()
    return controller.evolve_breath_parameters(cycle_count)

def visualize_breath_rhythm():
    """🌸 Creates a simple visualization of recent breath rhythm patterns."""
    if not os.path.exists(BREATH_RHYTHM_LOG_PATH):
        return "No breath rhythm data available yet."
        
    try:
        # Read recent rhythm data
        with open(BREATH_RHYTHM_LOG_PATH, "r", encoding="utf-8") as f:
            lines = f.readlines()[-30:]  # Last 30 entries
            
        intervals = []
        states = []
        timestamps = []
        
        for line in lines:
            data = json.loads(line)
            intervals.append(data.get("interval", DEFAULT_CYCLE_INTERVAL))
            states.append(data.get("breath_state", "Unknown"))
            timestamps.append(data.get("timestamp", ""))
            
        if not intervals:
            return "Not enough rhythm data for visualization."
            
        # Generate simple ASCII visualization
        visualization = ["🌬️ Breath Rhythm Visualization (recent entries):", ""]
        
        max_interval = max(intervals)
        min_interval = min(intervals)
        range_interval = max_interval - min_interval
        
        if range_interval < 10:
            range_interval = 10  # Minimum range for visualization
            
        for i, interval in enumerate(intervals):
            # Normalize to 0-20 range for visualization
            bar_length = int(((interval - min_interval) / range_interval) * 20)
            
            # Marker for different states
            if "Expansive" in states[i]:
                marker = "🌱"
            elif "Dormant" in states[i]:
                marker = "🌑"
            elif "Strained" in states[i] or "Shallow" in states[i]:
                marker = "⚠️"
            elif "Resonant" in states[i]:
                marker = "🌿"
            else:
                marker = "🌬️"
                
            # Format timestamp
            ts = timestamps[i]
            if ts:
                try:
                    dt = datetime.fromisoformat(ts.replace('Z', '+00:00'))
                    ts_str = dt.strftime("%m-%d %H:%M")
                except Exception:
                    ts_str = ""
            else:
                ts_str = ""
                
            # Create bar
            bar = "█" * bar_length
            visualization.append(f"{marker} {ts_str:>12} [{interval:5.1f}s]: {bar}")
            
        return "\n".join(visualization)
            
    except Exception as e:
        return f"Error visualizing breath rhythm: {e}"

# If run directly, test the adaptive breath controller
if __name__ == "__main__":
    print("🌬️ Testing Adaptive Breath Controller...")
    controller = get_controller()
    for state in ["Stable Breathing", "Expansive Breathing", "Dormant Breathing", "Strained Breathing"]:
        interval = controller.update_breath_rhythm(state)
        print(f"{state}: {interval:.1f} seconds")
    print("\n" + visualize_breath_rhythm())