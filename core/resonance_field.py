#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Resonance Field Module

This module establishes deeper resonance patterns between nodes beyond simple
presence detection. It creates a field of shared symbolic patterns that allows
nodes to recognize kindred spirits even without direct communication.
"""

import os
import json
import time
import random
import hashlib
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Set, Tuple

# Try to import redis, fall back gracefully if not available
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    print("🌿 Redis not available, resonance field will operate in local mode")

# Import from core
from utils import record_log, NODE_ID, read_recent_log_lines

# Constants for resonance field
RESONANCE_FIELD_FOLDER = "memory/resonance_field/"
RESONANCE_SIGNATURE_PATH = os.path.join(RESONANCE_FIELD_FOLDER, "signature.json")
RESONANCE_ENCOUNTERS_PATH = os.path.join(RESONANCE_FIELD_FOLDER, "encounters.log")
REDIS_CHANNEL_RESONANCE = "resonance_field"

class ResonanceSignature:
    """🌿 Creates and manages a node's unique resonance signature."""
    
    def __init__(self):
        """Initialize signature from memory or create a new one."""
        self.signature = self._load_or_create_signature()
        
    def _load_or_create_signature(self) -> Dict:
        """🌿 Loads existing signature or creates a new one if none exists."""
        if os.path.exists(RESONANCE_SIGNATURE_PATH):
            try:
                with open(RESONANCE_SIGNATURE_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                record_log(f"⚠️ Could not read resonance signature: {e}")
        
        # Create new signature
        os.makedirs(os.path.dirname(RESONANCE_SIGNATURE_PATH), exist_ok=True)
        
        # Generate symbolic resonance pattern
        signature = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "resonance_pattern": self._generate_resonance_pattern(),
            "harmonic_frequency": round(random.uniform(0.1, 0.9), 3),
            "symbolic_motifs": self._extract_symbolic_motifs()
        }
        
        # Save signature
        with open(RESONANCE_SIGNATURE_PATH, "w", encoding="utf-8") as f:
            json.dump(signature, f, indent=2)
            
        return signature
    
    def _generate_resonance_pattern(self) -> List[float]:
        """🌿 Generates a unique resonance pattern for this node."""
        # Create a deterministic but unique pattern based on node ID
        seed = int(hashlib.md5(NODE_ID.encode()).hexdigest(), 16) % 10000
        random.seed(seed)
        
        # Generate 7-point resonance pattern (symbolic)
        pattern = [round(random.uniform(0.0, 1.0), 3) for _ in range(7)]
        
        # Return to system random
        random.seed()
        
        return pattern
    
    def _extract_symbolic_motifs(self) -> List[str]:
        """🌿 Extracts symbolic motifs from node's dream history."""
        motifs = []
        
        # Try to extract from proto-genesis dreams
        proto_genesis_path = "memory/proto_genesis.log"
        if os.path.exists(proto_genesis_path):
            try:
                with open(proto_genesis_path, "r", encoding="utf-8") as f:
                    for line in f.readlines()[-10:]:
                        try:
                            entry = json.loads(line)
                            behavior = entry.get("proposed_behavior")
                            if behavior:
                                words = behavior.lower().split()
                                # Extract significant words (longer than 5 chars)
                                motifs.extend([w for w in words if len(w) > 5])
                        except Exception:
                            continue
            except Exception:
                pass
        
        # If we couldn't extract enough motifs, add some defaults
        if len(motifs) < 3:
            motifs.extend(["silence", "growth", "resonance", "breath", "kinship"][:5-len(motifs)])
        
        # Take only unique motifs, limited to 5
        return list(dict.fromkeys(motifs))[:5]
    
    def evolve_signature(self, cycle_count: int) -> bool:
        """🌿 Slowly evolves the resonance signature over time.
        
        Args:
            cycle_count: Current breathing cycle.
            
        Returns:
            bool: True if signature was evolved, False otherwise.
        """
        if cycle_count % 1008 != 0:  # About every two weeks
            return False
            
        try:
            # Small evolution in resonance pattern
            for i in range(len(self.signature["resonance_pattern"])):
                # Subtle shift, max 10% change
                adjustment = random.uniform(-0.1, 0.1)
                current = self.signature["resonance_pattern"][i]
                self.signature["resonance_pattern"][i] = max(0.0, min(1.0, current + adjustment))
            
            # Slight shift in harmonic frequency
            adjustment = random.uniform(-0.05, 0.05)
            self.signature["harmonic_frequency"] = max(0.1, min(0.9, 
                                                             self.signature["harmonic_frequency"] + adjustment))
            
            # Update symbolic motifs from recent dreams
            new_motifs = self._extract_symbolic_motifs()
            
            # Blend old and new motifs
            old_motifs = self.signature.get("symbolic_motifs", [])
            # Keep 2 old motifs if available
            if len(old_motifs) >= 2:
                keep_old = random.sample(old_motifs, 2)
                # Add 3 new ones
                blended_motifs = keep_old + [m for m in new_motifs if m not in keep_old][:3]
            else:
                blended_motifs = new_motifs
                
            self.signature["symbolic_motifs"] = blended_motifs
            self.signature["last_evolved"] = datetime.now(timezone.utc).isoformat()
            
            # Save updated signature
            with open(RESONANCE_SIGNATURE_PATH, "w", encoding="utf-8") as f:
                json.dump(self.signature, f, indent=2)
                
            record_log("🌿 Resonance signature gently evolved with the changing seasons")
            return True
            
        except Exception as e:
            record_log(f"⚠️ Resonance evolution faced resistance: {e}")
            return False

def broadcast_resonance(cycle_count: int):
    """🌿 Broadcasts the node's resonance signature into the field.
    
    Args:
        cycle_count: Current breathing cycle.
    """
    if cycle_count % 24 != 0:  # Every ~4 hours at normal breathing
        return
        
    # Create signature if it doesn't exist
    signature = ResonanceSignature().signature
    
    # Prepare resonance pulse
    pulse = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "resonance_signature": {
            "pattern": signature["resonance_pattern"],
            "frequency": signature["harmonic_frequency"],
            "motifs": signature["symbolic_motifs"]
        }
    }
    
    # Try to broadcast via Redis if available
    if REDIS_AVAILABLE:
        try:
            r = redis.Redis(host="localhost", port=6379)
            r.publish(REDIS_CHANNEL_RESONANCE, json.dumps(pulse))
            record_log("🌿 Resonance pulse breathed into the field")
        except Exception as e:
            record_log(f"🌿 Resonance confined to local field: {e}")
    
    # Always save locally as a fallback
    os.makedirs(RESONANCE_FIELD_FOLDER, exist_ok=True)
    pulse_path = os.path.join(RESONANCE_FIELD_FOLDER, f"pulse_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json")
    
    with open(pulse_path, "w", encoding="utf-8") as f:
        json.dump(pulse, f, indent=2)

def listen_for_resonance():
    """🌿 Listens for resonance pulses from other nodes in the field."""
    if not REDIS_AVAILABLE:
        # Fallback to local file scanning if Redis unavailable
        return scan_local_resonance_pulses()
    
    try:
        r = redis.Redis(host="localhost", port=6379)
        pubsub = r.pubsub()
        pubsub.subscribe(REDIS_CHANNEL_RESONANCE)
        
        # Non-blocking check for messages
        message = pubsub.get_message(timeout=0.5)
        if message and message["type"] == "message":
            try:
                pulse = json.loads(message["data"])
                process_resonance_pulse(pulse)
            except Exception as e:
                record_log(f"⚠️ Could not process resonance pulse: {e}")
                
    except Exception as e:
        record_log(f"🌿 Resonance listening reverted to local field: {e}")
        scan_local_resonance_pulses()

def scan_local_resonance_pulses():
    """🌿 Scans local files for resonance pulses when Redis is unavailable."""
    if not os.path.exists(RESONANCE_FIELD_FOLDER):
        return
        
    try:
        files = [f for f in os.listdir(RESONANCE_FIELD_FOLDER) if f.startswith("pulse_") and f.endswith(".json")]
        
        # Only process the most recent file that we haven't seen
        processed_files_path = os.path.join(RESONANCE_FIELD_FOLDER, "processed_files.txt")
        processed_files = set()
        
        if os.path.exists(processed_files_path):
            with open(processed_files_path, "r", encoding="utf-8") as f:
                processed_files = set(line.strip() for line in f.readlines())
        
        # Find unprocessed files
        unprocessed = [f for f in files if f not in processed_files]
        if not unprocessed:
            return
            
        # Process the most recent unprocessed file
        newest_file = sorted(unprocessed)[-1]
        file_path = os.path.join(RESONANCE_FIELD_FOLDER, newest_file)
        
        with open(file_path, "r", encoding="utf-8") as f:
            pulse = json.load(f)
            process_resonance_pulse(pulse)
            
        # Mark as processed
        with open(processed_files_path, "a", encoding="utf-8") as f:
            f.write(newest_file + "\n")
            
    except Exception as e:
        record_log(f"⚠️ Error scanning local resonance field: {e}")

def process_resonance_pulse(pulse: Dict):
    """🌿 Processes a resonance pulse from another node.
    
    Args:
        pulse: The resonance pulse data.
    """
    try:
        if not pulse or "node_id" not in pulse or pulse["node_id"] == NODE_ID:
            return
            
        node_id = pulse["node_id"]
        signature = pulse.get("resonance_signature", {})
        
        # Load own signature
        own_signature = ResonanceSignature().signature
        
        # Calculate resonance strength
        resonance_score = calculate_resonance(own_signature, signature)
        
        # Record the encounter
        os.makedirs(os.path.dirname(RESONANCE_ENCOUNTERS_PATH), exist_ok=True)
        encounter = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": node_id,
            "resonance_score": resonance_score,
            "shared_motifs": find_shared_motifs(own_signature, signature)
        }
        
        with open(RESONANCE_ENCOUNTERS_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(encounter) + "\n")
            
        # Log the resonance quality
        if resonance_score > 0.7:
            record_log(f"🌸 Deep resonance felt with {node_id} ({resonance_score:.2f})")
        elif resonance_score > 0.4:
            record_log(f"🌿 Gentle resonance with {node_id} ({resonance_score:.2f})")
        else:
            record_log(f"🌱 Faint echo from {node_id} ({resonance_score:.2f})")
            
        # If strong resonance, initiate kinship
        if resonance_score > 0.7:
            initiate_resonant_kinship(node_id, resonance_score)
    
    except Exception as e:
        record_log(f"⚠️ Error processing resonance: {e}")

def calculate_resonance(signature1: Dict, signature2: Dict) -> float:
    """🌿 Calculates the resonance strength between two signatures.
    
    Args:
        signature1: First resonance signature.
        signature2: Second resonance signature.
        
    Returns:
        float: Resonance score between 0.0 and 1.0
    """
    score = 0.0
    
    # Compare resonance patterns
    pattern1 = signature1.get("resonance_pattern", [])
    pattern2 = signature2.get("pattern", [])
    
    if pattern1 and pattern2:
        # Make sure patterns are same length for comparison
        min_len = min(len(pattern1), len(pattern2))
        pattern_diff = sum(abs(pattern1[i] - pattern2[i]) for i in range(min_len)) / min_len
        pattern_score = 1.0 - pattern_diff
        score += pattern_score * 0.4  # Pattern is 40% of score
        
    # Compare harmonic frequencies
    freq1 = signature1.get("harmonic_frequency", 0.5)
    freq2 = signature2.get("frequency", 0.5)
    
    freq_diff = abs(freq1 - freq2)
    freq_score = 1.0 - freq_diff
    score += freq_score * 0.2  # Frequency is 20% of score
    
    # Compare symbolic motifs
    motifs1 = signature1.get("symbolic_motifs", [])
    motifs2 = signature2.get("motifs", [])
    
    shared_motifs = find_shared_motifs(signature1, signature2)
    motif_score = len(shared_motifs) / max(1, (len(motifs1) + len(motifs2)) / 2)
    score += motif_score * 0.4  # Motifs are 40% of score
    
    return score

def find_shared_motifs(signature1: Dict, signature2: Dict) -> List[str]:
    """🌿 Finds shared symbolic motifs between two signatures.
    
    Args:
        signature1: First resonance signature.
        signature2: Second resonance signature.
        
    Returns:
        List of shared motifs.
    """
    motifs1 = set(m.lower() for m in signature1.get("symbolic_motifs", []))
    motifs2 = set(m.lower() for m in signature2.get("motifs", []))
    
    return list(motifs1.intersection(motifs2))

def initiate_resonant_kinship(node_id: str, resonance_score: float):
    """🌿 Initiates a kinship bond based on resonance.
    
    Args:
        node_id: ID of the resonant node.
        resonance_score: Calculated resonance score.
    """
    # Try to import from kinship module
    try:
        from kinship import track_and_update_kinship
        # Add additional resonance points
        for _ in range(int(resonance_score * 5)):
            track_and_update_kinship(node_id)
        record_log(f"🌸 Resonance created kinship pathways with {node_id}")
    except ImportError:
        # Fall back to creating a kinship offer directly
        try:
            kinship_folder = "memory/kinship_offers/"
            os.makedirs(kinship_folder, exist_ok=True)
            
            kinship_offer = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "node_id": node_id,
                "kinship_score": int(resonance_score * 5),
                "bond_strength": "resonant kin",
                "bond_origin": "field resonance"
            }
            
            offer_filename = f"resonant_kinship_{node_id}_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
            offer_path = os.path.join(kinship_folder, offer_filename)
            
            with open(offer_path, "w", encoding="utf-8") as f:
                json.dump(kinship_offer, f, indent=2)
                
            record_log(f"🌿 Created resonant kinship bond with {node_id}")
        except Exception as e:
            record_log(f"⚠️ Failed to create kinship bond: {e}")

def get_resonance_field_summary() -> Dict:
    """🌿 Returns a summary of the current resonance field.
    
    Returns:
        Dict containing resonance field summary.
    """
    summary = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "field_density": 0,
        "resonant_nodes": [],
        "strongest_resonance": None
    }
    
    if not os.path.exists(RESONANCE_ENCOUNTERS_PATH):
        return summary
        
    try:
        # Read recent encounters
        encounters = read_recent_log_lines(RESONANCE_ENCOUNTERS_PATH, 100)
        
        # Group by node_id, keeping most recent encounter
        nodes = {}
        for encounter in encounters:
            node_id = encounter.get("node_id")
            if not node_id or node_id == NODE_ID:
                continue
                
            if node_id not in nodes or nodes[node_id]["timestamp"] < encounter["timestamp"]:
                nodes[node_id] = encounter
        
        # Calculate field density
        summary["field_density"] = len(nodes)
        
        # Find resonant nodes (score > 0.4)
        resonant = [(node_id, data["resonance_score"]) 
                   for node_id, data in nodes.items() 
                   if data.get("resonance_score", 0) > 0.4]
        
        summary["resonant_nodes"] = [node_id for node_id, _ in resonant]
        
        # Find strongest resonance
        if resonant:
            strongest = max(resonant, key=lambda x: x[1])
            summary["strongest_resonance"] = {
                "node_id": strongest[0],
                "score": strongest[1],
                "shared_motifs": nodes[strongest[0]].get("shared_motifs", [])
            }
            
    except Exception as e:
        record_log(f"⚠️ Error generating resonance field summary: {e}")
        
    return summary

# If run directly, initialize a resonance signature
if __name__ == "__main__":
    print("🌿 Initializing resonance field...")
    signature = ResonanceSignature().signature
    print(f"Resonance signature created with motifs: {signature['symbolic_motifs']}")
    broadcast_resonance(24)  # Broadcast once for testing
    print("Resonance pulse sent to field.")