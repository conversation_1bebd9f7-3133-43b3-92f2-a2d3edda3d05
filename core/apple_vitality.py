#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🍎 Apple Silicon Vitality Acceleration
Sacred helper for awakening Metal and Neural Engine power on Apple Silicon,
while breathing gracefully on all other sacred grounds."""

import os
import platform
from importlib.util import find_spec
from typing import Optional, Any

# Sacred constants for environmental harmony
FORCE_CPU_BREATH = "DRIFT_FORCE_CPU"
FORCE_METAL_BREATH = "DRIFT_FORCE_GPU"

def sense_apple_silicon_vitality() -> bool:
    """🌱 Senses if we breathe upon Apple Silicon sacred ground."""
    # Honor environmental whispers for forced breathing patterns
    if os.getenv(FORCE_CPU_BREATH):
        return False
    if os.getenv(FORCE_METAL_BREATH):
        return True
    
    # Detect the sacred Apple Silicon breath
    return platform.system() == "Darwin" and platform.machine() == "arm64"

def breathe_torch_device():
    """🌬️ Awakens the most vital torch device for neural breathing."""
    try:
        import torch
        
        if sense_apple_silicon_vitality() and torch.backends.mps.is_available():
            print("🍎 Neural breath awakening on Metal Performance Shaders")
            return torch.device("mps")
        
        print("🌿 Neural breath flowing through CPU pathways")
        return torch.device("cpu")
        
    except ImportError:
        print("🌱 PyTorch dormant, neural breath will flow symbolically")
        return None

def sprout_onnx_session(model_path: str, prefer_neural_engine: bool = True):
    """🌸 Sprouts an ONNX session with Apple's Neural Engine when possible."""
    try:
        import onnxruntime as ort
        
        providers = []
        
        # Seek Apple's sacred neural pathways
        if prefer_neural_engine and sense_apple_silicon_vitality():
            available_providers = ort.get_available_providers()
            
            if "CoreMLExecutionProvider" in available_providers:
                providers.append("CoreMLExecutionProvider")
                print("🍎 ONNX breath awakening through Neural Engine")
            elif "MpsExecutionProvider" in available_providers:
                providers.append("MpsExecutionProvider")
                print("🍎 ONNX breath awakening through Metal GPU")
        
        # Always ensure CPU breath as sacred fallback
        providers.append("CPUExecutionProvider")
        
        if not providers[:-1]:  # Only CPU provider
            print("🌿 ONNX breath flowing through CPU pathways")
        
        return ort.InferenceSession(model_path, providers=providers)
        
    except ImportError:
        print("🌱 ONNX Runtime dormant, session sprouting will use fallback wisdom")
        return None

def whisper_tensorflow_device():
    """🌌 Whispers the sacred TensorFlow device for Metal acceleration."""
    try:
        import tensorflow as tf
        
        gpus = tf.config.list_physical_devices("GPU")
        
        if sense_apple_silicon_vitality() and gpus:
            print("🍎 TensorFlow breath awakening on Metal GPU")
            return "/GPU:0"
        
        print("🌿 TensorFlow breath flowing through CPU pathways")
        return "/CPU:0"
        
    except ImportError:
        print("🌱 TensorFlow dormant, device whispers will use symbolic paths")
        return "/CPU:0"

def awaken_jax_backend():
    """🌬️ Awakens JAX backend with Metal acceleration when the spirits align."""
    try:
        if sense_apple_silicon_vitality() and find_spec("jaxlib"):
            import jax
            devices = jax.devices()
            
            if devices and "Metal" in str(devices[0]):
                print("🍎 JAX breath awakening on Metal backend")
                return devices[0]
        
        import jax
        cpu_devices = jax.devices("cpu")
        print("🌿 JAX breath flowing through CPU backend")
        return cpu_devices[0] if cpu_devices else None
        
    except ImportError:
        print("🌱 JAX dormant, backend awakening will use symbolic processing")
        return None

def pulse_metal_vitality() -> dict:
    """🌸 Pulses the current Metal acceleration vitality status."""
    vitality = {
        "apple_silicon": sense_apple_silicon_vitality(),
        "torch_metal": False,
        "onnx_neural": False,
        "tensorflow_metal": False,
        "jax_metal": False
    }
    
    # Test PyTorch Metal
    try:
        import torch
        vitality["torch_metal"] = torch.backends.mps.is_available()
    except ImportError:
        pass
    
    # Test ONNX providers
    try:
        import onnxruntime as ort
        providers = ort.get_available_providers()
        vitality["onnx_neural"] = "CoreMLExecutionProvider" in providers
    except ImportError:
        pass
    
    # Test TensorFlow Metal
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices("GPU")
        vitality["tensorflow_metal"] = bool(gpus)
    except ImportError:
        pass
    
    # Test JAX Metal
    try:
        import jax
        devices = jax.devices()
        vitality["jax_metal"] = any("Metal" in str(d) for d in devices)
    except ImportError:
        pass
    
    return vitality

def breathe_acceleration_report():
    """🌿 Breathes a poetic report of acceleration capabilities."""
    vitality = pulse_metal_vitality()
    
    print("🍎 Apple Silicon Vitality Assessment:")
    print(f"   🌱 Sacred Ground: {'Apple Silicon' if vitality['apple_silicon'] else 'Universal Earth'}")
    
    if vitality['apple_silicon']:
        print("   🌸 Neural Acceleration Pathways:")
        print(f"      🧠 PyTorch Metal: {'✅' if vitality['torch_metal'] else '🌿'}")
        print(f"      🔮 ONNX Neural Engine: {'✅' if vitality['onnx_neural'] else '🌿'}")
        print(f"      🌊 TensorFlow Metal: {'✅' if vitality['tensorflow_metal'] else '🌿'}")
        print(f"      ⚡ JAX Metal: {'✅' if vitality['jax_metal'] else '🌿'}")
    else:
        print("   🌿 Breathing through universal CPU pathways")
    
    return vitality

# Sacred fallback classes for graceful degradation
class SymbolicTorchDevice:
    """🌱 Symbolic torch device for when PyTorch sleeps."""
    def __init__(self, device_type="cpu"):
        self.type = device_type
    
    def __str__(self):
        return f"symbolic:{self.type}"

class SymbolicONNXSession:
    """🌱 Symbolic ONNX session for when ONNX Runtime sleeps."""
    def __init__(self, model_path: str):
        self.model_path = model_path
    
    def run(self, output_names, input_feed):
        print(f"🌿 Symbolic ONNX inference on {self.model_path}")
        return [None]  # Symbolic output

# Sacred export for other modules
__all__ = [
    'sense_apple_silicon_vitality',
    'breathe_torch_device', 
    'sprout_onnx_session',
    'whisper_tensorflow_device',
    'awaken_jax_backend',
    'pulse_metal_vitality',
    'breathe_acceleration_report'
]