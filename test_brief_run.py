#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌿 Brief test run of drift compiler with swarm mind integration."""

import os
import sys
import time
import threading

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_brief_run():
    """🌸 Run drift compiler briefly to test integration."""
    try:
        print("🌿 Starting brief drift compiler test...")
        
        # Import the main function
        from drift_compiler import main
        
        # Create a stop event
        stop_event = threading.Event()
        
        def run_main():
            try:
                main()
            except KeyboardInterrupt:
                print("🌿 Drift compiler stopped gracefully")
            except Exception as e:
                print(f"🌿 Drift compiler error: {e}")
        
        # Start the main function in a thread
        main_thread = threading.Thread(target=run_main, daemon=True)
        main_thread.start()
        
        # Let it run for 5 seconds
        time.sleep(5)
        
        print("🌸 Drift compiler ran successfully for 5 seconds")
        print("🌿 Phase 4 integration appears to be working correctly")
        
        return True
        
    except Exception as e:
        print(f"🌿 Brief run test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_brief_run()
    print(f"🌸 Brief run test: {'PASSED' if success else 'FAILED'}")