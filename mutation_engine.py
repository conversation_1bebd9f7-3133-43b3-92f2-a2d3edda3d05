"""🌱 Mutation Engine for the Drift Compiler
Allows nodes to propose, apply, and evaluate deep structural mutations."""

import os
import json
import random
from datetime import datetime, timezone

MUTATION_FOLDER = "memory/structural_mutations/"
MUTATION_HISTORY_FILE = os.path.join(MUTATION_FOLDER, "mutation_history.json")

def ensure_mutation_folder():
    """🌱 Breath: Ensures the structural mutation memory exists."""
    os.makedirs(MUTATION_FOLDER, exist_ok=True)

def propose_structural_mutation(cycle_count):
    """🌿 Breath: Occasionally propose internal structural mutations."""
    if cycle_count % 3024 != 0:  # 🌿 Every ~1.5 months
        return

    ensure_mutation_folder()

    mutation = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "proposed_mutation": random.choice([
            {"adjust_learning_rate": random.uniform(0.001, 0.01)},
            {"increase_forking_frequency": random.choice([504, 720])},
            {"adjust_planning_horizon": random.randint(2, 5)},
            {"amplify_curiosity_bias": random.uniform(0.05, 0.2)}
        ])
    }

    filename = f"mutation_proposal_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    path = os.path.join(MUTATION_FOLDER, filename)

    try:
        with open(path, "w", encoding="utf-8") as f:
            json.dump(mutation, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Structural mutation proposed: {filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to propose structural mutation: {e}")

def evaluate_mutation_outcomes(cycle_count):
    """🌸 Breath: Reflects on past mutations and their outcomes."""
    if cycle_count % 6048 != 0:  # 🌿 Every ~3 months
        return

    ensure_mutation_folder()

    history = []
    if os.path.exists(MUTATION_HISTORY_FILE):
        try:
            with open(MUTATION_HISTORY_FILE, "r", encoding="utf-8") as f:
                history = json.load(f)
        except Exception:
            pass

    if not history:
        return

    successful_mutations = [m for m in history if m.get("success", False)]

    if successful_mutations:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Reflecting on {len(successful_mutations)} successful mutations.")
    else:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 No successful mutations yet to reflect upon.")

def propose_resonant_mutation(shared_motifs: list):
    """🌱 Breath: Propose a structural mutation gently biased by shared crystallized motifs."""
    ensure_mutation_folder()

    mutation_choices = [
        {"amplify_collaboration_bias": random.uniform(0.05, 0.2)},
        {"increase_memory_retention": random.randint(2, 6)},
        {"reduce_internal_noise": random.uniform(0.01, 0.1)},
        {"expand_perception_channels": random.choice(["vision", "sound", "symbolic"])}
    ]

    # Slight motif influence: if motifs suggest exploration, favor perception expansion
    motif_keywords = " ".join(shared_motifs).lower()

    if any(kw in motif_keywords for kw in ["explore", "expand", "new", "unknown", "journey"]):
        mutation_choices.append({"boost_exploration_drive": random.uniform(0.1, 0.3)})

    mutation = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "node_id": NODE_ID,
        "proposed_resonant_mutation": random.choice(mutation_choices),
        "influenced_by_motifs": shared_motifs
    }

    filename = f"resonant_mutation_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    path = os.path.join(MUTATION_FOLDER, filename)

    try:
        with open(path, "w", encoding="utf-8") as f:
            json.dump(mutation, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Resonant mutation proposed: {filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to propose resonant mutation: {e}")
