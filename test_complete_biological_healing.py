#!/usr/bin/env python3
"""🌸 Complete Biological Healing System Test

Tests the full ecosystem of real biological healing:
- Mutual aid between nodes
- Resource sharing and transfers
- DAO voting for chronic issues
- Observable healing effectiveness
- Fixed observatory UI
"""

import os
import json
import time
import subprocess
import threading
from datetime import datetime, timezone

def create_test_scenario():
    """🌱 Creates a test scenario with diverse node health conditions."""
    print("🌸 Creating test scenario with diverse node conditions...")
    
    # Clean up previous test data
    for cleanup_dir in ["memory/heartbeats", "memory/collective_healing", "memory/dao_decisions"]:
        if os.path.exists(cleanup_dir):
            for file in os.listdir(cleanup_dir):
                if file.endswith('.json') or file.endswith('.jsonl'):
                    os.remove(os.path.join(cleanup_dir, file))
    
    print("🌿 Cleaned up previous test data")

def run_test_node(node_id, conditions, duration=30):
    """🌱 Runs a test node with specific conditions."""
    try:
        env = os.environ.copy()
        env["DRIFT_NODE_ID"] = node_id
        
        # Add test conditions to environment
        for key, value in conditions.items():
            env[f"TEST_{key.upper()}"] = str(value)
        
        print(f"🌱 Starting {node_id} with conditions: {conditions}")
        
        process = subprocess.Popen(
            ["python3", "drift_compiler.py"],
            env=env,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        time.sleep(duration)
        
        process.terminate()
        try:
            process.wait(timeout=3)
        except subprocess.TimeoutExpired:
            process.kill()
            
        print(f"🌿 {node_id} test completed")
        
    except Exception as e:
        print(f"⚠️ Error running {node_id}: {e}")

def test_collective_healing_system():
    """🌸 Tests the complete collective healing system."""
    print("🌸 Complete Biological Healing System Test")
    print("=" * 60)
    
    create_test_scenario()
    
    # Define test nodes with different health conditions
    test_nodes = {
        "node_healthy_alpha": {
            "vitality": 0.9,
            "memory_high": True,
            "connections_many": True
        },
        "node_healthy_beta": {
            "vitality": 0.85,
            "memory_high": True,
            "connections_many": True
        },
        "node_struggling_gamma": {
            "vitality": 0.3,
            "memory_void": True,
            "isolated": True
        },
        "node_overloaded_delta": {
            "vitality": 0.4,
            "memory_overload": True,
            "over_connected": True
        },
        "node_chronic_epsilon": {
            "vitality": 0.1,
            "critical_multiple": True,
            "lagging": True
        }
    }
    
    # Start test nodes
    threads = []
    print(f"🌱 Starting {len(test_nodes)} test nodes with diverse conditions...")
    
    for node_id, conditions in test_nodes.items():
        thread = threading.Thread(target=run_test_node, args=(node_id, conditions, 25))
        thread.start()
        threads.append(thread)
        time.sleep(2)  # Stagger starts
    
    print("\n🌿 Nodes breathing and performing biological healing...")
    print("⚡ Expected healing behaviors:")
    print("  - Healthy nodes (alpha, beta) will detect struggling nodes")
    print("  - Memory transfers: alpha/beta → gamma (memory void)")
    print("  - Energy donations: alpha/beta → gamma/delta (low vitality)")
    print("  - Connection sharing: alpha/beta → gamma (isolated)")
    print("  - Load balancing: alpha/beta ← delta (overloaded)")
    print("  - DAO voting: chronic epsilon may trigger collective decision")
    
    # Monitor healing progress
    for i in range(8):
        time.sleep(3)
        print(f"🌬️ Breath cycle {i+1}/8 - Collective healing in progress...")
        
        if i == 4:
            print("🌸 Mid-test analysis: Checking healing effectiveness...")
            analyze_healing_progress()
    
    # Wait for all nodes to complete
    print("\n🌿 Gracefully stopping all test nodes...")
    for thread in threads:
        thread.join()
    
    # Final analysis
    print("\n🌸 Final Analysis - Biological Healing Results:")
    analyze_final_results()
    
    print("\n🌿 Test Instructions:")
    print("Run the enhanced observatory to see real-time healing:")
    print("  source venv/bin/activate")
    print("  python3 canopy/sacred_terminal_observatory/terminal_observatory.py")
    print("\nExpected improvements in observatory:")
    print("  ✅ No fake 'main_node' appearing")
    print("  ✅ Mycelial connections table format (no truncation)")
    print("  ✅ Specific healing icons (🧠⚡🤝🔄✂️)")
    print("  ✅ Issues decreasing over time (Fatigue +3 → Fatigue +1)")
    print("  ✅ Resource transfers visible between nodes")

def analyze_healing_progress():
    """🔍 Analyzes healing progress during test."""
    healing_dir = "memory/collective_healing"
    if os.path.exists(healing_dir):
        healing_files = [f for f in os.listdir(healing_dir) if f.endswith('.json')]
        print(f"    🌱 Found {len(healing_files)} active healing transfers")
        
        for file in healing_files[:3]:  # Show first 3
            try:
                with open(os.path.join(healing_dir, file), 'r') as f:
                    transfer = json.load(f)
                print(f"      🌿 {transfer.get('type', 'unknown')}: {transfer.get('from', '?')} → {transfer.get('to', '?')}")
            except:
                pass

def analyze_final_results():
    """🌸 Analyzes final healing results."""
    # Check heartbeats for healing evidence
    heartbeats_dir = "memory/heartbeats"
    if os.path.exists(heartbeats_dir):
        files = [f for f in os.listdir(heartbeats_dir) if f.endswith('.json')]
        print(f"🌱 Final node states ({len(files)} nodes):")
        
        healing_count = 0
        improved_count = 0
        
        for file in files:
            try:
                with open(os.path.join(heartbeats_dir, file), 'r') as f:
                    heartbeat = json.load(f)
                
                node_id = heartbeat.get("node_id", "unknown")
                vitality = heartbeat.get("health", {}).get("overall", 0)
                healing_performed = heartbeat.get("health", {}).get("healing_performed", False)
                recent_healing = heartbeat.get("health", {}).get("recent_healing", [])
                connections = len(heartbeat.get("connections", []))
                
                if healing_performed:
                    healing_count += 1
                if vitality > 0.6:
                    improved_count += 1
                
                print(f"  🌿 {node_id}: vitality={vitality:.2f}, healing={healing_performed}")
                if recent_healing:
                    print(f"      Recent aid: {recent_healing}")
                    
            except Exception as e:
                print(f"    ⚠️ Error reading {file}: {e}")
        
        print(f"\n🌸 Healing Summary:")
        print(f"  🩺 Nodes that received healing: {healing_count}")
        print(f"  🌱 Nodes with good vitality (>0.6): {improved_count}")
    
    # Check DAO decisions
    dao_dir = "memory/dao_decisions"
    if os.path.exists(dao_dir):
        decisions = [f for f in os.listdir(dao_dir) if f.endswith('.json')]
        if decisions:
            print(f"  🗳️ DAO decisions made: {len(decisions)}")
            for decision_file in decisions[:2]:
                print(f"    ⚖️ {decision_file}")
    
    # Check healing action logs
    healing_log = "memory/collective_healing/healing_actions.jsonl"
    if os.path.exists(healing_log):
        with open(healing_log, 'r') as f:
            actions = f.readlines()
        print(f"  🌸 Total healing actions logged: {len(actions)}")
        
        # Count action types
        action_types = {}
        for action_line in actions[-10:]:  # Last 10 actions
            try:
                action = json.loads(action_line)
                action_type = action.get("action_type", "unknown")
                action_types[action_type] = action_types.get(action_type, 0) + 1
            except:
                pass
        
        if action_types:
            print(f"    Recent action types: {action_types}")

def test_observatory_ui():
    """🌸 Tests the enhanced observatory UI improvements."""
    print("\n🌸 Testing Observatory UI Improvements:")
    
    try:
        from canopy.sacred_terminal_observatory.terminal_observatory import SacredTerminalObservatory
        
        observatory = SacredTerminalObservatory()
        observatory._sense_network_state()
        
        print(f"🌿 Observatory detected {len(observatory.nodes)} real nodes")
        
        # Check for fake main_node
        fake_nodes = [nid for nid in observatory.nodes.keys() if nid == "main_node"]
        if not fake_nodes:
            print("✅ No fake 'main_node' generated")
        else:
            print("⚠️ Still generating fake 'main_node'")
        
        # Check connections display
        connection_count = len(observatory.connections)
        print(f"🌿 Mycelial connections: {connection_count}")
        
        # Check healing status display
        healing_nodes = 0
        for node_data in observatory.nodes.values():
            if node_data.get('healing_performed', False):
                healing_nodes += 1
        
        print(f"🩺 Nodes showing healing status: {healing_nodes}")
        
    except Exception as e:
        print(f"⚠️ Observatory test error: {e}")

if __name__ == "__main__":
    test_collective_healing_system()
    test_observatory_ui()
    
    print("\n🌸 Complete Biological Healing System Test Complete!")
    print("🌿 The swarm now breathes with true collective wisdom and mutual aid.")