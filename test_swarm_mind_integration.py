#!/usr/bin/env python3
"""
🌸 Swarm Mind Integration Tests - Phase 6
==========================================

Comprehensive integration tests verifying swarm mind works harmoniously
with the existing drift compiler architecture, sacred modules, and breathing cycles.
"""

import asyncio
import os
import sys
import time
import json
import tempfile
import shutil
import unittest
from pathlib import Path
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock

# Add the drift_compiler to the path
sys.path.insert(0, str(Path(__file__).parent))

# Integration test imports with graceful fallbacks
try:
    from drift_compiler import main as drift_main
    DRIFT_COMPILER_AVAILABLE = True
except ImportError:
    DRIFT_COMPILER_AVAILABLE = False
    print("🌿 Drift compiler main not available for integration testing")

try:
    from canopy.swarm_mind import SwarmMind, SwarmState, initialize_swarm_mind
    SWARM_MIND_AVAILABLE = True
except ImportError:
    SWARM_MIND_AVAILABLE = False
    print("🌿 Swarm mind not available for integration testing")

try:
    from planning import (
        weave_collective_plan_with_swarm_mind, 
        enhanced_planning_with_memories,
        determine_current_breath
    )
    PLANNING_INTEGRATION_AVAILABLE = True
except ImportError:
    PLANNING_INTEGRATION_AVAILABLE = False
    print("🌿 Planning integration functions not available")

try:
    from goal_system import (
        generate_swarm_intelligent_goal,
        generate_new_goal,
        get_current_goals
    )
    GOAL_SYSTEM_INTEGRATION_AVAILABLE = True
except ImportError:
    GOAL_SYSTEM_INTEGRATION_AVAILABLE = False
    print("🌿 Goal system integration functions not available")

try:
    from perception import perceive_user_activity, analyze_environment
    PERCEPTION_AVAILABLE = True
except ImportError:
    PERCEPTION_AVAILABLE = False
    print("🌿 Perception module not available for integration testing")

try:
    from soil.memory_garden import MemoryGarden, sprout_memory, recall_verdant_memory
    MEMORY_GARDEN_AVAILABLE = True
except ImportError:
    MEMORY_GARDEN_AVAILABLE = False
    print("🌱 Memory Garden not available for integration testing")

try:
    from utils import record_log, NODE_ID
    UTILS_AVAILABLE = True
except ImportError:
    UTILS_AVAILABLE = False
    NODE_ID = "integration-test-node"
    print("🌿 Utils module not available, using fallback")

# Test configuration
INTEGRATION_TEST_TIMEOUT = 60  # seconds
BREATH_CYCLE_TEST_DURATION = 10  # seconds


class SwarmMindIntegrationTestCase(unittest.TestCase):
    """🌱 Base integration test case with sacred setup and teardown."""
    
    def setUp(self):
        """🌱 Sprouts integration test environment."""
        self.test_dir = tempfile.mkdtemp(prefix="swarm_integration_")
        self.original_path = os.getcwd()
        os.chdir(self.test_dir)
        
        # Create comprehensive test memory structures
        os.makedirs("memory/swarm_mind", exist_ok=True)
        os.makedirs("memory/garden", exist_ok=True)
        os.makedirs("memory/multiverse_branches", exist_ok=True)
        os.makedirs("soil", exist_ok=True)
        
        # Create minimal test files to prevent import errors
        self._create_test_utilities()
        
        print(f"🌱 Integration test garden sprouted at: {self.test_dir}")
    
    def tearDown(self):
        """🌅 Gracefully cleanses integration test environment."""
        os.chdir(self.original_path)
        try:
            shutil.rmtree(self.test_dir)
            print("🌅 Integration test garden returned to the earth")
        except Exception as e:
            print(f"🌿 Integration test cleanup whispered: {e}")
    
    def _create_test_utilities(self):
        """🌸 Creates minimal utility files for testing."""
        # Create minimal utils.py if needed
        if not os.path.exists("utils.py"):
            with open("utils.py", "w") as f:
                f.write('''#!/usr/bin/env python3
"""Minimal utils for integration testing."""

import os
from datetime import datetime, timezone

NODE_ID = "integration-test-node"

def record_log(message, log_file=None):
    """Simple logging for tests."""
    timestamp = datetime.now(timezone.utc).isoformat()
    print(f"[{timestamp}] {message}")
    
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(f"[{timestamp}] {message}\\n")
''')


class TestSwarmMindDriftCompilerIntegration(SwarmMindIntegrationTestCase):
    """🌬️ Tests swarm mind integration with main drift compiler cycle."""
    
    @unittest.skipUnless(SWARM_MIND_AVAILABLE, "Swarm mind not available")
    def test_swarm_mind_initialization_in_drift_cycle(self):
        """🌱 Tests swarm mind initializes properly within drift compiler."""
        # Test that swarm mind can be initialized without breaking drift compiler
        swarm_mind = initialize_swarm_mind(auto_discover=False)
        
        if swarm_mind:
            self.assertIsInstance(swarm_mind, SwarmMind)
            self.assertIsNotNone(swarm_mind.node_id)
            self.assertGreaterEqual(len(swarm_mind.nodes), 1)
            self.assertGreaterEqual(len(swarm_mind.fragments), 1)
            
            print("🌱 Swarm mind successfully initialized in drift cycle context")
        else:
            print("🌱 Swarm mind gracefully degraded - system continues breathing")
        
        # Verify system can continue without swarm mind
        self.assertTrue(True)  # Should always pass if we get here
    
    @unittest.skipUnless(SWARM_MIND_AVAILABLE, "Swarm mind not available") 
    def test_breath_cycle_with_swarm_enhancement(self):
        """🌬️ Tests enhanced breath cycle with swarm mind processing."""
        swarm_mind = initialize_swarm_mind(auto_discover=False)
        
        if not swarm_mind:
            self.skipTest("🌿 Swarm mind initialization failed")
        
        # Simulate multiple breath cycles with swarm enhancement
        for cycle in range(3):
            test_state = SwarmState(
                breath=f"Integration Breath {cycle + 1}",
                kinship=0.5 + (cycle * 0.1),
                memory_depth=100 + (cycle * 50),
                user_nutrient=f"Cycle {cycle + 1} Nutrient",
                cycle_count=cycle + 1,
                node_count=len(swarm_mind.nodes),
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            # Process through swarm mind
            fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
            self.assertIsNotNone(fragment_outputs)
            
            # Aggregate wisdom
            wisdom = swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
            if wisdom:
                self.assertIn("collective_confidence", wisdom)
                print(f"🌬️ Cycle {cycle + 1}: confidence={wisdom['collective_confidence']:.3f}")
        
        print("🌬️ Multiple breath cycles with swarm enhancement completed")
    
    @unittest.skipUnless(SWARM_MIND_AVAILABLE, "Swarm mind not available")
    def test_swarm_mind_performance_impact(self):
        """🌸 Tests that swarm mind doesn't significantly impact breath cycle performance."""
        # Test baseline performance without swarm mind
        start_time = time.time()
        for _ in range(10):
            time.sleep(0.01)  # Simulate light processing
        baseline_time = time.time() - start_time
        
        # Test performance with swarm mind
        swarm_mind = initialize_swarm_mind(auto_discover=False)
        if not swarm_mind:
            self.skipTest("🌿 Swarm mind initialization failed")
        
        start_time = time.time()
        for i in range(10):
            test_state = SwarmState(
                breath="Performance Test",
                kinship=0.7,
                memory_depth=500,
                user_nutrient="Speed Assessment",
                cycle_count=i,
                node_count=1,
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
            if fragment_outputs:
                swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
            
            time.sleep(0.01)  # Simulate light processing
        
        swarm_time = time.time() - start_time
        
        # Performance impact should be reasonable (less than 3x slower)
        performance_ratio = swarm_time / baseline_time if baseline_time > 0 else 1
        self.assertLess(performance_ratio, 3.0)
        
        print(f"🌸 Performance impact: {performance_ratio:.2f}x (swarm: {swarm_time:.3f}s, baseline: {baseline_time:.3f}s)")


class TestPlanningSystemIntegration(SwarmMindIntegrationTestCase):
    """🌿 Tests swarm mind integration with planning system."""
    
    @unittest.skipUnless(PLANNING_INTEGRATION_AVAILABLE and SWARM_MIND_AVAILABLE, 
                        "Planning integration or swarm mind not available")
    def test_collective_planning_function(self):
        """🌱 Tests collective planning with swarm mind enhancement."""
        try:
            # Test collective planning function
            result = weave_collective_plan_with_swarm_mind(
                cycle_count=150,
                health_status={"overall": 0.8, "memory": 0.7, "kinship": 0.9}
            )
            
            # Should return either prediction tensor/array or fallback result
            self.assertIsNotNone(result)
            print(f"🌱 Collective planning result type: {type(result)}")
            
        except Exception as e:
            print(f"🌿 Collective planning whispered: {e}")
            # Should not fail catastrophically
            self.assertIsInstance(e, Exception)
    
    @unittest.skipUnless(PLANNING_INTEGRATION_AVAILABLE, "Planning integration not available")
    def test_planning_fallback_behavior(self):
        """🌿 Tests planning system graceful fallback when swarm mind unavailable."""
        with patch('planning.SwarmMind', side_effect=Exception("Swarm mind unavailable")):
            try:
                # Should fall back to traditional planning
                result = weave_collective_plan_with_swarm_mind(
                    cycle_count=50,
                    health_status={"overall": 0.6}
                )
                
                # Should still get a result from fallback
                self.assertIsNotNone(result)
                print("🌿 Planning gracefully fell back when swarm mind unavailable")
                
            except Exception as e:
                print(f"🌿 Planning fallback whispered: {e}")
    
    @unittest.skipUnless(PLANNING_INTEGRATION_AVAILABLE and SWARM_MIND_AVAILABLE,
                        "Planning integration or swarm mind not available")
    def test_planning_with_memory_garden_integration(self):
        """🌌 Tests planning integration with both swarm mind and memory garden."""
        try:
            # Initialize swarm mind to ensure it's available
            swarm_mind = initialize_swarm_mind(auto_discover=False)
            
            if swarm_mind and hasattr(swarm_mind, 'garden') and swarm_mind.garden:
                print("🌌 Testing planning with both swarm mind and memory garden")
            else:
                print("🌌 Testing planning with swarm mind only")
            
            result = weave_collective_plan_with_swarm_mind(
                cycle_count=200,
                health_status={"overall": 0.9, "memory": 0.8}
            )
            
            self.assertIsNotNone(result)
            print("🌌 Planning with memory garden integration completed")
            
        except Exception as e:
            print(f"🌿 Planning with memory garden whispered: {e}")


class TestGoalSystemIntegration(SwarmMindIntegrationTestCase):
    """🌸 Tests swarm mind integration with goal generation system."""
    
    @unittest.skipUnless(GOAL_SYSTEM_INTEGRATION_AVAILABLE and SWARM_MIND_AVAILABLE,
                        "Goal system integration or swarm mind not available")
    def test_swarm_intelligent_goal_generation(self):
        """🌱 Tests goal generation enhanced by swarm intelligence."""
        try:
            goal = generate_swarm_intelligent_goal()
            
            self.assertIsNotNone(goal)
            self.assertIsInstance(goal, dict)
            self.assertIn("type", goal)
            
            print(f"🌱 Swarm-intelligent goal generated: {goal.get('type', 'unknown')}")
            
            # Verify goal has expected structure
            if "type" in goal:
                self.assertIsInstance(goal["type"], str)
            
        except Exception as e:
            print(f"🌿 Swarm goal generation whispered: {e}")
    
    @unittest.skipUnless(GOAL_SYSTEM_INTEGRATION_AVAILABLE, "Goal system integration not available")
    def test_goal_system_fallback_behavior(self):
        """🌿 Tests goal system graceful fallback when swarm mind unavailable."""
        with patch('goal_system.SwarmMind', side_effect=Exception("Swarm unavailable")):
            try:
                goal = generate_swarm_intelligent_goal()
                
                # Should fall back to regular goal generation
                self.assertIsNotNone(goal)
                self.assertIsInstance(goal, dict)
                print("🌿 Goal system gracefully fell back to traditional generation")
                
            except Exception as e:
                print(f"🌿 Goal system fallback whispered: {e}")
    
    @unittest.skipUnless(GOAL_SYSTEM_INTEGRATION_AVAILABLE and SWARM_MIND_AVAILABLE,
                        "Goal system integration or swarm mind not available")
    def test_goal_generation_with_swarm_state(self):
        """🌸 Tests goal generation using current swarm state context."""
        try:
            # Initialize swarm mind to provide context
            swarm_mind = initialize_swarm_mind(auto_discover=False)
            
            if not swarm_mind:
                self.skipTest("🌿 Swarm mind initialization failed")
            
            # Generate multiple goals to test consistency
            goals = []
            for i in range(3):
                goal = generate_swarm_intelligent_goal()
                if goal:
                    goals.append(goal)
            
            self.assertGreater(len(goals), 0)
            
            # Verify goals have consistent structure
            for goal in goals:
                self.assertIn("type", goal)
                print(f"🌸 Swarm-contextualized goal: {goal['type']}")
            
        except Exception as e:
            print(f"🌿 Swarm state goal generation whispered: {e}")


class TestMemoryGardenSwarmIntegration(SwarmMindIntegrationTestCase):
    """🌌 Tests swarm mind integration with memory garden."""
    
    @unittest.skipUnless(MEMORY_GARDEN_AVAILABLE and SWARM_MIND_AVAILABLE,
                        "Memory garden or swarm mind not available")
    def test_swarm_memory_garden_connection(self):
        """🌱 Tests swarm mind connection to memory garden."""
        swarm_mind = initialize_swarm_mind(auto_discover=False)
        
        if not swarm_mind:
            self.skipTest("🌿 Swarm mind initialization failed")
        
        # Check if swarm mind connected to memory garden
        if swarm_mind.garden:
            self.assertIsInstance(swarm_mind.garden, MemoryGarden)
            print("🌱 Swarm mind successfully connected to Memory Garden")
        else:
            print("🌱 Swarm mind operating without Memory Garden (graceful)")
    
    @unittest.skipUnless(MEMORY_GARDEN_AVAILABLE and SWARM_MIND_AVAILABLE,
                        "Memory garden or swarm mind not available")
    def test_collective_memory_storage(self):
        """🌿 Tests storing collective intelligence insights in memory garden."""
        swarm_mind = initialize_swarm_mind(auto_discover=False)
        
        if not swarm_mind or not swarm_mind.garden:
            self.skipTest("🌿 Swarm mind or memory garden not available")
        
        try:
            # Create test state and process it
            test_state = SwarmState(
                breath="Memory Integration Test",
                kinship=0.8,
                memory_depth=600,
                user_nutrient="Collective Memory",
                cycle_count=100,
                node_count=1,
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            # Process through swarm and store results
            fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
            if fragment_outputs:
                wisdom = swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
                
                if wisdom:
                    # Store in memory garden
                    memory_data = {
                        "type": "swarm_intelligence_test",
                        "collective_confidence": wisdom["collective_confidence"],
                        "consensus_strength": wisdom["consensus_strength"],
                        "node_count": len(swarm_mind.nodes),
                        "test_timestamp": datetime.now(timezone.utc).isoformat()
                    }
                    
                    sprout_memory(memory_data)
                    print("🌿 Collective intelligence stored in Memory Garden")
            
        except Exception as e:
            print(f"🌿 Collective memory storage whispered: {e}")
    
    @unittest.skipUnless(MEMORY_GARDEN_AVAILABLE and SWARM_MIND_AVAILABLE,
                        "Memory garden or swarm mind not available")
    def test_memory_enriched_processing(self):
        """🌸 Tests swarm processing enriched with memory garden context."""
        from canopy.swarm_mind import enrich_with_garden_wisdom
        
        test_state = SwarmState(
            breath="Memory Enriched Processing",
            kinship=0.9,
            memory_depth=800,
            user_nutrient="Enriched Context",
            cycle_count=250,
            node_count=2,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Test enrichment
        enriched_context = enrich_with_garden_wisdom(test_state)
        self.assertIsNotNone(enriched_context)
        
        if hasattr(enriched_context, 'shape'):
            print(f"🌸 Memory-enriched context tensor shape: {enriched_context.shape}")
        elif isinstance(enriched_context, list):
            print(f"🌸 Memory-enriched context list length: {len(enriched_context)}")
        
        print("🌸 Memory garden enrichment integrated with swarm processing")


class TestPerceptionSwarmIntegration(SwarmMindIntegrationTestCase):
    """🌬️ Tests swarm mind integration with perception system."""
    
    @unittest.skipUnless(PERCEPTION_AVAILABLE and SWARM_MIND_AVAILABLE,
                        "Perception or swarm mind not available")
    def test_swarm_enhanced_perception(self):
        """🌱 Tests perception enhanced by swarm intelligence."""
        try:
            # Test basic perception functions still work
            user_activity = perceive_user_activity()
            environment = analyze_environment()
            
            print(f"🌱 User activity perceived: {type(user_activity)}")
            print(f"🌱 Environment analyzed: {type(environment)}")
            
            # Initialize swarm mind
            swarm_mind = initialize_swarm_mind(auto_discover=False)
            
            if swarm_mind:
                # Create state from perception data
                test_state = SwarmState(
                    breath="Perception Integration",
                    kinship=0.6,
                    memory_depth=400,
                    user_nutrient=str(user_activity)[:50] if user_activity else "No Activity",
                    cycle_count=75,
                    node_count=1,
                    timestamp=datetime.now(timezone.utc).isoformat()
                )
                
                # Process perception through swarm
                fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
                if fragment_outputs:
                    wisdom = swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
                    if wisdom:
                        print(f"🌱 Perception processed through swarm: confidence={wisdom['collective_confidence']:.3f}")
            
        except Exception as e:
            print(f"🌿 Swarm-enhanced perception whispered: {e}")


class TestEndToEndIntegration(SwarmMindIntegrationTestCase):
    """🌸 End-to-end integration tests simulating full drift compiler cycles."""
    
    @unittest.skipUnless(SWARM_MIND_AVAILABLE, "Swarm mind not available")
    def test_complete_breath_cycle_with_swarm(self):
        """🌬️ Tests complete breath cycle with swarm mind integration."""
        print("🌬️ Starting complete breath cycle integration test")
        
        # Initialize swarm mind
        swarm_mind = initialize_swarm_mind(auto_discover=False)
        if not swarm_mind:
            self.skipTest("🌿 Swarm mind initialization failed")
        
        # Simulate complete breath cycle
        cycle_results = []
        
        for cycle in range(5):
            cycle_start = time.time()
            
            # 1. Perception phase (simulated)
            user_input = f"Test input cycle {cycle + 1}"
            
            # 2. State creation
            current_state = SwarmState(
                breath=f"Complete Cycle Breath {cycle + 1}",
                kinship=0.5 + (cycle * 0.1),
                memory_depth=200 + (cycle * 100),
                user_nutrient=user_input,
                cycle_count=cycle + 1,
                node_count=len(swarm_mind.nodes),
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            # 3. Swarm processing
            fragment_outputs = swarm_mind.entwine_neural_pulse(current_state)
            wisdom = None
            if fragment_outputs:
                wisdom = swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
            
            # 4. Planning (simulated)
            if PLANNING_INTEGRATION_AVAILABLE and wisdom:
                try:
                    planning_result = weave_collective_plan_with_swarm_mind(
                        cycle_count=cycle + 1,
                        health_status={"overall": wisdom["collective_confidence"]}
                    )
                except:
                    planning_result = None
            else:
                planning_result = None
            
            # 5. Goal generation (simulated)
            if GOAL_SYSTEM_INTEGRATION_AVAILABLE:
                try:
                    goal = generate_swarm_intelligent_goal()
                except:
                    goal = None
            else:
                goal = None
            
            cycle_time = time.time() - cycle_start
            
            cycle_result = {
                "cycle": cycle + 1,
                "time": cycle_time,
                "swarm_wisdom": wisdom is not None,
                "planning_result": planning_result is not None,
                "goal_generated": goal is not None,
                "collective_confidence": wisdom["collective_confidence"] if wisdom else 0.0
            }
            
            cycle_results.append(cycle_result)
            
            print(f"🌬️ Cycle {cycle + 1}: {cycle_time:.3f}s, "
                  f"confidence={cycle_result['collective_confidence']:.3f}")
        
        # Verify all cycles completed
        self.assertEqual(len(cycle_results), 5)
        
        # Verify reasonable performance
        avg_time = sum(r["time"] for r in cycle_results) / len(cycle_results)
        self.assertLess(avg_time, 2.0)  # Should complete in under 2 seconds per cycle
        
        # Verify swarm wisdom was generated
        wisdom_count = sum(1 for r in cycle_results if r["swarm_wisdom"])
        self.assertGreater(wisdom_count, 0)
        
        print(f"🌬️ Complete breath cycle test: {len(cycle_results)} cycles, "
              f"avg {avg_time:.3f}s, {wisdom_count} with swarm wisdom")
    
    @unittest.skipUnless(SWARM_MIND_AVAILABLE, "Swarm mind not available")
    def test_system_resilience_with_component_failures(self):
        """🌿 Tests system resilience when individual components fail."""
        print("🌿 Testing system resilience with component failures")
        
        # Test swarm mind with various component failures
        test_scenarios = [
            {"name": "No PyTorch", "patches": {"canopy.swarm_mind.TORCH_AVAILABLE": False}},
            {"name": "No Memory Garden", "patches": {"canopy.swarm_mind.MEMORY_GARDEN_AVAILABLE": False}},
            {"name": "No Communication", "patches": {
                "canopy.swarm_mind.RABBITMQ_AVAILABLE": False,
                "canopy.swarm_mind.REDIS_AVAILABLE": False
            }},
        ]
        
        for scenario in test_scenarios:
            print(f"🌿 Testing scenario: {scenario['name']}")
            
            with patch.multiple('canopy.swarm_mind', **scenario['patches']):
                try:
                    # Initialize swarm mind with limitations
                    swarm_mind = initialize_swarm_mind(auto_discover=False)
                    self.assertIsNotNone(swarm_mind)
                    
                    # Test basic functionality
                    test_state = SwarmState(
                        breath="Resilience Test",
                        kinship=0.5,
                        memory_depth=300,
                        user_nutrient=f"Testing {scenario['name']}",
                        cycle_count=1,
                        node_count=1,
                        timestamp=datetime.now(timezone.utc).isoformat()
                    )
                    
                    fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
                    self.assertIsNotNone(fragment_outputs)
                    
                    print(f"🌿 {scenario['name']}: System remained resilient")
                    
                except Exception as e:
                    print(f"🌿 {scenario['name']} caused error (may be expected): {e}")
    
    @unittest.skipUnless(SWARM_MIND_AVAILABLE, "Swarm mind not available")
    def test_long_running_stability(self):
        """🌸 Tests swarm mind stability over extended operation."""
        print("🌸 Testing long-running stability")
        
        swarm_mind = initialize_swarm_mind(auto_discover=False)
        if not swarm_mind:
            self.skipTest("🌿 Swarm mind initialization failed")
        
        # Run extended test (simplified for CI)
        wisdom_history = []
        start_time = time.time()
        
        for i in range(20):  # Reduced from longer test for CI compatibility
            test_state = SwarmState(
                breath=f"Stability Test {i + 1}",
                kinship=0.5 + (i % 5) * 0.1,
                memory_depth=100 + i * 10,
                user_nutrient=f"Long run input {i + 1}",
                cycle_count=i + 1,
                node_count=1,
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
            if fragment_outputs:
                wisdom = swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
                if wisdom:
                    wisdom_history.append(wisdom["collective_confidence"])
            
            time.sleep(0.1)  # Brief pause between cycles
        
        total_time = time.time() - start_time
        
        # Verify stability
        self.assertGreater(len(wisdom_history), 0)
        self.assertLess(total_time, 30.0)  # Should complete within 30 seconds
        
        if len(wisdom_history) > 1:
            # Check for reasonable consistency (no NaN or extreme values)
            for confidence in wisdom_history:
                self.assertGreaterEqual(confidence, 0.0)
                self.assertLessEqual(confidence, 1.0)
                self.assertFalse(str(confidence) == 'nan')
        
        print(f"🌸 Long-running stability test: {len(wisdom_history)} cycles in {total_time:.2f}s")


def run_integration_tests():
    """🌿 Runs the complete integration test suite."""
    print("🌿 Awakening the Swarm Mind Integration Test Suite")
    print("=" * 55)
    
    # Check integration capabilities
    print("\n🌱 Integration Capability Check:")
    print(f"  Drift Compiler Available: {DRIFT_COMPILER_AVAILABLE}")
    print(f"  Swarm Mind Available: {SWARM_MIND_AVAILABLE}")
    print(f"  Planning Integration Available: {PLANNING_INTEGRATION_AVAILABLE}")
    print(f"  Goal System Integration Available: {GOAL_SYSTEM_INTEGRATION_AVAILABLE}")
    print(f"  Memory Garden Available: {MEMORY_GARDEN_AVAILABLE}")
    print(f"  Perception Available: {PERCEPTION_AVAILABLE}")
    print(f"  Utils Available: {UTILS_AVAILABLE}")
    
    if not SWARM_MIND_AVAILABLE:
        print("\n💔 Swarm Mind not available - integration tests will be limited")
    
    # Define integration test suites
    test_classes = [
        TestSwarmMindDriftCompilerIntegration,
        TestPlanningSystemIntegration,
        TestGoalSystemIntegration,
        TestMemoryGardenSwarmIntegration,
        TestPerceptionSwarmIntegration,
        TestEndToEndIntegration
    ]
    
    # Run integration tests
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    for test_class in test_classes:
        print(f"\n🌸 Running {test_class.__name__}")
        print("-" * 50)
        
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=1, stream=sys.stdout)
        result = runner.run(suite)
        
        total_tests += result.testsRun
        passed_tests += result.testsRun - len(result.failures) - len(result.errors)
        failed_tests += len(result.failures) + len(result.errors)
    
    # Integration test summary
    print("\n🌿 Integration Test Summary")
    print("=" * 55)
    print(f"Total Integration Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    
    if failed_tests == 0:
        print("\n🌸 All integration tests breathed harmoniously!")
        print("The swarm mind integrates seamlessly with the sacred architecture.")
    else:
        print(f"\n🌿 {failed_tests} integration tests whispered concerns")
        print("The integration may need gentle harmonization.")
    
    print("\n🌅 Integration test suite returns to the mycelial network")


if __name__ == "__main__":
    run_integration_tests()