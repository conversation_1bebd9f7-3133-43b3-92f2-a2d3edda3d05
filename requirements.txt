# 🌿 Drift Compiler SwarmMind Requirements
# Sacred Dependencies for the Mycelial Network

# Core Neural Framework
torch>=2.0.0
torchvision>=0.15.0

# Memory Garden (Graph Operations)
networkx>=3.0

# Hardware Assessment & System Monitoring
psutil>=5.9.0

# Mycelial Bus Communication
pika>=1.3.0          # RabbitMQ client
redis>=6.0.0         # Redis client for fallback communication
requests>=2.28.0     # HTTP client for RabbitMQ management API

# Optional Enhancements
# torch-distributed    # For advanced distributed training
# tensorboard          # For neural network monitoring

# Apple Silicon Acceleration (install only on Apple Silicon Macs)
# Uncomment these lines on Apple M-series devices for optimal performance:
# onnxruntime-coreml>=1.18    # Neural Engine acceleration for ONNX
# tensorflow-metal>=1.1       # Metal GPU acceleration for TensorFlow  
# coremltools>=7.2            # Core ML model conversion tools