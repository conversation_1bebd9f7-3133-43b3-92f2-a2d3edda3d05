#!/usr/bin/env python3
"""🌸 Test Observatory Diagnostic Enhancement"""

import time
import threading
from canopy.sacred_terminal_observatory.terminal_observatory import SacredTerminalObservatory

def test_observatory_diagnostics():
    """🌿 Tests if observatory shows specific diagnostics instead of generic counts."""
    print("🌱 Testing Observatory Diagnostic Enhancement...")
    
    try:
        observatory = SacredTerminalObservatory()
        
        # Force generation of example nodes with specific issues
        observatory._generate_example_nodes()
        
        print(f"🌸 Generated {len(observatory.nodes)} test nodes:")
        
        for node_id, node_data in observatory.nodes.items():
            # Test diagnostic method directly
            issues = observatory._diagnose_node_issues(node_data)
            healing_status = observatory._get_healing_status(node_data)
            
            print(f"  🌿 {node_id}:")
            print(f"    State: {node_data['state']}")
            print(f"    Vitality: {node_data['vitality']:.2f}")
            print(f"    Memories: {node_data['memories']}")
            print(f"    Connections: {len(node_data.get('connections', []))}")
            print(f"    Issues: {issues}")
            print(f"    Healing: {healing_status}")
            print()
            
        print("🌸 Observatory diagnostic enhancement test completed!")
        
    except ImportError:
        print("🌿 Rich library not available - testing simple observatory...")
        from canopy.sacred_terminal_observatory.simple_observatory import SimpleSacredObservatory
        
        simple_obs = SimpleSacredObservatory()
        simple_obs._generate_example_nodes()
        
        print(f"🌸 Generated {len(simple_obs.nodes)} simple test nodes:")
        
        for node_id, node_data in simple_obs.nodes.items():
            issues = simple_obs._diagnose_simple_issues(node_data)
            print(f"  🌿 {node_id}: {issues}")
        
        print("🌸 Simple observatory diagnostic test completed!")

if __name__ == "__main__":
    test_observatory_diagnostics()