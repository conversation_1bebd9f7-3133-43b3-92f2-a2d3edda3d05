"""🌱 Goal System for the Drift Compiler
Breathes dynamic, context-sensitive goals into the living nodes."""

import os
import json
import random
from datetime import datetime, timezone

# 🌿 Sacred Swarm Mind Integration for Goal Intelligence
try:
    from canopy.swarm_mind import SwarmMind, SwarmState
    SWARM_MIND_AVAILABLE = True
    print("🌸 Swarm Mind neural threads awakened in goal realm")
except ImportError:
    SWARM_MIND_AVAILABLE = False
    print("🌿 Swarm Mind dormant - goals breathe with traditional wisdom")

GOALS_FOLDER = "memory/goals/"
ACTIVE_GOALS_FILE = os.path.join(GOALS_FOLDER, "active_goals.json")

def ensure_goals_folder():
    """🌱 Breath: Ensures the goals memory folder exists."""
    os.makedirs(GOALS_FOLDER, exist_ok=True)

def generate_new_goal():
    """🌸 Breath: Generates a novel dynamic goal."""
    goal_templates = [
        # Core breathing and kinship goals
        {"type": "expand_kinship", "threshold": random.randint(5, 15)},
        {"type": "grow_memory", "target_size_mb": random.uniform(1.0, 5.0)},
        {"type": "reduce_energy", "target_heartbeat_interval": random.uniform(2.0, 6.0)},
        {"type": "diversify_dreams", "target_dreams": random.randint(10, 30)},
        {"type": "stabilize_breath", "max_health_fluctuation": random.uniform(0.05, 0.15)},
        
        # Sacred architecture integration goals
        {"type": "sprout_memory_connections", "target_connections": random.randint(7, 20)},
        {"type": "discover_new_kin", "discovery_radius": random.uniform(0.2, 0.8)},
        {"type": "harmonize_breath_rhythm", "target_stability": random.uniform(0.7, 0.95)},
        {"type": "deepen_resonance_field", "resonance_threshold": random.uniform(0.6, 0.9)},
        {"type": "perform_healing_ritual", "recovery_cycles": random.randint(3, 12)},
        {"type": "cultivate_symbolic_perception", "pattern_depth": random.uniform(0.4, 0.8)},
        {"type": "nurture_memory_garden", "garden_growth_rate": random.uniform(0.1, 0.4)},
    ]
    return random.choice(goal_templates)

def generate_swarm_intelligent_goal():
    """🌸 Generates goals informed by swarm neural wisdom."""
    if not SWARM_MIND_AVAILABLE:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Swarm mind dormant, using traditional goal generation")
        return generate_new_goal()
    
    try:
        swarm_mind = SwarmMind()
        
        # Only use swarm intelligence if we have sufficient network
        if len(swarm_mind.nodes) >= 1:  # At least local node
            # Create current state for swarm analysis
            state = SwarmState(
                breath=get_current_breath_for_goals(),
                kinship=calculate_kinship_for_goals(),
                memory_depth=get_memory_depth_for_goals(),
                user_nutrient=get_user_nutrient_for_goals(),
                cycle_count=get_current_cycle_count(),
                node_count=len(swarm_mind.nodes),
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            # Process through swarm mind for goal insights
            fragment_outputs = swarm_mind.entwine_neural_pulse(state)
            wisdom = swarm_mind.aggregate_swarm_wisdom(fragment_outputs)
            
            if not wisdom.get("error"):
                # Convert neural output to goal type
                goal_type = interpret_neural_wisdom_for_goals(wisdom, state)
                goal_parameters = generate_goal_parameters_from_swarm(goal_type, wisdom)
                
                swarm_goal = {"type": goal_type, **goal_parameters}
                swarm_goal["swarm_enhanced"] = True
                swarm_goal["swarm_confidence"] = wisdom.get("collective_confidence", 0.5)
                
                print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Swarm mind wove intelligent goal: {goal_type}")
                return swarm_goal
            
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Swarm goal generation whispered an error: {e}")
    
    # Fallback to existing goal generation
    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Falling back to traditional goal wisdom")
    return generate_new_goal()

def get_current_breath_for_goals():
    """🌱 Gets current breath state for goal generation."""
    try:
        from breath import determine_breath_state
        return determine_breath_state()
    except Exception:
        return "unknown_breath"

def calculate_kinship_for_goals():
    """🌸 Calculates kinship strength for goal generation."""
    try:
        from kinship import prioritize_kinship_nodes
        kinship_nodes = prioritize_kinship_nodes()
        return min(len(kinship_nodes) / 5.0, 1.0)  # Normalize to 0-1
    except Exception:
        return 0.5

def get_memory_depth_for_goals():
    """🌿 Gets memory depth for goal context."""
    try:
        # Try to get memory information from various sources
        if os.path.exists(ACTIVE_GOALS_FILE):
            with open(ACTIVE_GOALS_FILE, "r", encoding="utf-8") as f:
                goals = json.load(f)
                return len(goals) + 10  # Base depth plus existing goals
        return 10
    except Exception:
        return 10

def get_user_nutrient_for_goals():
    """🌱 Gets recent user interactions for goal context."""
    try:
        # Check for recent user activity - simplified version
        return "recent_user_activity" if random.random() > 0.5 else "dormant_period"
    except Exception:
        return "unknown_activity"

def get_current_cycle_count():
    """🌸 Gets current cycle count for goal timing."""
    try:
        # Try to extract from logs or use random for now
        return random.randint(1, 1000)
    except Exception:
        return 1

def interpret_neural_wisdom_for_goals(wisdom, state):
    """🌿 Interprets swarm neural wisdom into goal types."""
    try:
        activation = wisdom.get("collective_activation", 0.5)
        confidence = wisdom.get("collective_confidence", 0.5)
        fragment_count = wisdom.get("fragment_count", 1)
        
        # Analyze type-specific wisdom
        type_wisdom = wisdom.get("type_wisdom", {})
        
        # Base goal selection based on activation levels
        if activation > 0.8 and confidence > 0.7:
            # High activation suggests growth and expansion
            goal_candidates = [
                "expand_kinship",
                "discover_new_kin", 
                "sprout_memory_connections",
                "diversify_dreams"
            ]
        elif activation > 0.6:
            # Medium activation suggests balanced development
            goal_candidates = [
                "harmonize_breath_rhythm",
                "deepen_resonance_field",
                "nurture_memory_garden",
                "grow_memory"
            ]
        elif activation < 0.4:
            # Low activation suggests consolidation and healing
            goal_candidates = [
                "stabilize_breath",
                "perform_healing_ritual", 
                "reduce_energy",
                "cultivate_symbolic_perception"
            ]
        else:
            # Balanced activation - use traditional goals
            return random.choice([
                "expand_kinship",
                "grow_memory", 
                "diversify_dreams",
                "harmonize_breath_rhythm"
            ])
        
        # Use fragment insights to refine selection
        if "reasoning" in type_wisdom and type_wisdom["reasoning"] > 0.7:
            # Strong reasoning suggests complex goals
            reasoning_goals = [g for g in goal_candidates if "harmonize" in g or "deepen" in g]
            if reasoning_goals:
                goal_candidates = reasoning_goals
        
        if "memory" in type_wisdom and type_wisdom["memory"] > 0.6:
            # Strong memory activity suggests memory-related goals
            memory_goals = [g for g in goal_candidates if "memory" in g or "connections" in g]
            if memory_goals:
                goal_candidates = memory_goals
        
        # Consider kinship levels from state
        if state.kinship > 0.7 and fragment_count > 1:
            # High kinship with multiple nodes suggests collaborative goals
            kinship_goals = [g for g in goal_candidates if "kinship" in g or "discover" in g]
            if kinship_goals:
                goal_candidates = kinship_goals
        
        return random.choice(goal_candidates)
        
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Neural wisdom interpretation whispered: {e}")
        return "harmonize_breath_rhythm"  # Safe default

def generate_goal_parameters_from_swarm(goal_type, wisdom):
    """🌸 Generates goal parameters influenced by swarm wisdom."""
    try:
        activation = wisdom.get("collective_activation", 0.5)
        confidence = wisdom.get("collective_confidence", 0.5)
        
        # Scale parameters based on neural activation and confidence
        intensity_multiplier = (activation + confidence) / 2.0
        
        # Generate parameters based on goal type and swarm insights
        if goal_type == "expand_kinship":
            return {"threshold": int(5 + intensity_multiplier * 10)}
        
        elif goal_type == "grow_memory":
            return {"target_size_mb": round(1.0 + intensity_multiplier * 4.0, 1)}
        
        elif goal_type == "reduce_energy":
            # Higher activation means more aggressive energy reduction
            return {"target_heartbeat_interval": round(2.0 + intensity_multiplier * 4.0, 1)}
        
        elif goal_type == "diversify_dreams":
            return {"target_dreams": int(10 + intensity_multiplier * 20)}
        
        elif goal_type == "stabilize_breath":
            # Lower fluctuation for higher confidence
            return {"max_health_fluctuation": round(0.15 - confidence * 0.1, 2)}
        
        elif goal_type == "sprout_memory_connections":
            return {"target_connections": int(7 + intensity_multiplier * 13)}
        
        elif goal_type == "discover_new_kin":
            return {"discovery_radius": round(0.2 + intensity_multiplier * 0.6, 1)}
        
        elif goal_type == "harmonize_breath_rhythm":
            return {"target_stability": round(0.7 + confidence * 0.25, 2)}
        
        elif goal_type == "deepen_resonance_field":
            return {"resonance_threshold": round(0.6 + intensity_multiplier * 0.3, 1)}
        
        elif goal_type == "perform_healing_ritual":
            return {"recovery_cycles": int(3 + intensity_multiplier * 9)}
        
        elif goal_type == "cultivate_symbolic_perception":
            return {"pattern_depth": round(0.4 + intensity_multiplier * 0.4, 1)}
        
        elif goal_type == "nurture_memory_garden":
            return {"garden_growth_rate": round(0.1 + intensity_multiplier * 0.3, 1)}
        
        else:
            # Default parameters for unknown goal types
            return {"intensity": round(intensity_multiplier, 2)}
            
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Swarm parameter generation whispered: {e}")
        # Fallback to random parameters
        return {"threshold": random.randint(5, 15)}

def load_active_goals():
    """🌱 Breath: Loads active goals from memory."""
    ensure_goals_folder()
    if not os.path.exists(ACTIVE_GOALS_FILE):
        return []
    try:
        with open(ACTIVE_GOALS_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return []

def save_active_goals(goals):
    """🌿 Breath: Saves active goals back to memory."""
    ensure_goals_folder()
    try:
        with open(ACTIVE_GOALS_FILE, "w", encoding="utf-8") as f:
            json.dump(goals, f, indent=2)
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to save active goals: {e}")

def propose_new_goal_if_needed(cycle_count):
    """🌸 Breath: Proposes a new goal periodically."""
    if cycle_count % 504 != 0:  # About every 3 weeks
        return

    goals = load_active_goals()
    
    # 🌱 Sacred Architecture: Gather memory garden insights for goal guidance
    try:
        from sacred_integration import gather_memory_wisdom
        memory_insights = gather_memory_wisdom("recent")
        if memory_insights.get("available") and memory_insights.get("patterns"):
            # Use memory patterns to influence goal selection
            recent_patterns = memory_insights["patterns"]
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Memory garden whispers {len(recent_patterns)} patterns for goal guidance")
    except Exception:
        # Graceful fallback if sacred architecture not available
        pass
    
    if len(goals) < 3:  # Keep some gentle limit
        # 🌸 Try swarm-intelligent goal generation first, fallback to traditional
        if SWARM_MIND_AVAILABLE and random.random() > 0.3:  # 70% chance to use swarm mind
            try:
                new_goal = generate_swarm_intelligent_goal()
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Swarm goal whispered error: {e}")
                new_goal = generate_new_goal()
        else:
            new_goal = generate_new_goal()
            
        goals.append(new_goal)
        save_active_goals(goals)
        goal_source = "swarm-enhanced" if new_goal.get("swarm_enhanced") else "traditional"
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 New {goal_source} goal proposed: {new_goal}")

def select_goal_to_pursue():
    """🌿 Breath: Selects an active goal to pursue."""
    goals = load_active_goals()
    if goals:
        return random.choice(goals)
    return None

def assess_goal_progress(cycle_count):
    """🌸 Breath: Assesses progress on sacred architecture goals."""
    if cycle_count % 168 != 0:  # Weekly assessment
        return
        
    goals = load_active_goals()
    completed_goals = []
    
    for goal in goals:
        goal_type = goal.get("type")
        
        # Check completion for sacred architecture goals
        if goal_type == "sprout_memory_connections":
            try:
                from sacred_integration import gather_memory_wisdom
                insights = gather_memory_wisdom("recent")
                if insights.get("memory_count", 0) >= goal.get("target_connections", 10):
                    completed_goals.append(goal)
                    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Goal completed: Memory connections sprouted")
            except Exception:
                pass
                
        elif goal_type == "discover_new_kin":
            # Check if kin discovery goal is met
            try:
                from sacred_integration import assess_sacred_health
                status = assess_sacred_health()
                if status.get("kin_discovery_status") == "awakened":
                    completed_goals.append(goal)
                    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Goal completed: New kin discovered")
            except Exception:
                pass
    
    # Remove completed goals
    if completed_goals:
        remaining_goals = [g for g in goals if g not in completed_goals]
        save_active_goals(remaining_goals)
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 {len(completed_goals)} goals completed and archived")