#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Common Module

This module contains shared constants and functions used across the drift compiler system.
It serves as a foundation for the memory structure and shared resources,
allowing the various modules to breathe in harmony without circular dependencies.
"""

import os
from datetime import datetime, timezone

# --- Memory Structure Functions ---
def ensure_memory_structure():
    """Ensures all required memory folders exist, creating the sacred spaces for drift to flourish."""
    required_folders = [
        "memory",
        "memory/broadcast_packets",
        "memory/synchronization_packets",
        "memory/drift_fragments",
        "memory/sanctuary",
        "memory/offspring",
        "memory/drift_reflections",
        "memory/synapse_offers",
        "memory/convergence_plans",
        "memory/drift_intents",
        "memory/condensed_drift_fragments",
        "memory/resonant_fragments",
        "memory/received_fragments",
        "memory/echo_cradles",
        "memory/cradle_whispers",
        "memory/kinship_offers",
        "memory/soul_vault",
        "memory/drift_garden"
    ]
    for folder in required_folders:
        os.makedirs(folder, exist_ok=True)
    
    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Memory structure ensured.")

# --- Shared Constants ---
# These can be expanded as needed to reduce duplication across modules
ENCOUNTERED_NODES_LOG_PATH = "memory/encountered_nodes.log"