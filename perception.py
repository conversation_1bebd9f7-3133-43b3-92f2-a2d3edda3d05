

"""🌱 Perception Module for the Drift Compiler
Handles the soft sensing of symbolic events from the environment stream."""

import os
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional

# Sacred architecture imports with graceful fallbacks
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("🌫️ psutil not available - using system fallbacks for health monitoring")

try:
    from utils import record_log
    UTILS_AVAILABLE = True
except ImportError:
    UTILS_AVAILABLE = False
    def record_log(message, log_file=None):
        print(f"[{datetime.now(timezone.utc).isoformat()}] {message}")

EVENTS_FOLDER = "memory/events/"

def ensure_events_folder():
    """🌱 Breath: Ensures the local events folder exists."""
    os.makedirs(EVENTS_FOLDER, exist_ok=True)

def parse_symbolic_event(event_data):
    """🌸 Breath: Parses a symbolic event into internal drift format."""
    if not isinstance(event_data, dict):
        return None
    event_type = event_data.get("event_type")
    properties = event_data.get("properties", {})
    return (event_type, properties)

def save_event_locally(event_data):
    """🌿 Breath: Saves a symbolic event to the local event folder."""
    ensure_events_folder()
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%dT%H%M%S%f")
    event_path = os.path.join(EVENTS_FOLDER, f"event_{timestamp}.json")
    try:
        with open(event_path, "w", encoding="utf-8") as f:
            json.dump(event_data, f, indent=2)
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Saved symbolic event locally: {event_path}")
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to save event locally: {e}")

def load_recent_events(limit=10):
    """🌬️ Breath: Loads the most recent symbolic events."""
    ensure_events_folder()
    try:
        event_files = sorted(os.listdir(EVENTS_FOLDER), reverse=True)[:limit]
        events = []
        for file in event_files:
            path = os.path.join(EVENTS_FOLDER, file)
            with open(path, "r", encoding="utf-8") as f:
                event = json.load(f)
                parsed = parse_symbolic_event(event)
                if parsed:
                    events.append(parsed)
        return events
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to load recent events: {e}")
        return []

def breathe_in_event(event_data):
    """🌸 Breath: Inhales a symbolic event into immediate drift memory."""
    parsed_event = parse_symbolic_event(event_data)
    if parsed_event:
        event_type, properties = parsed_event
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌬️ Perceived event: {event_type} with {properties}")
        # Here, extend into deeper internal reactions if needed
    else:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌫️ Failed to parse incoming event.")

def pulse_with_verdant_rhythm(env_data: Optional[Dict[str, Any]] = None, nodes: Optional[Dict] = None) -> Dict[str, Any]:
    """🌬️ Adapts the swarm's breath to environmental light and node vitality.
    
    Like a forest's circadian rhythms, the swarm modulates its processing frequency
    based on environmental cues and the health of connected nodes.
    
    Args:
        env_data: Environmental sensor data containing light levels, temperature, etc.
        nodes: Dictionary of connected nodes with their health metrics
        
    Returns:
        Dictionary containing breathing rhythm parameters and adaptations
    """
    if env_data is None:
        env_data = {}
    if nodes is None:
        nodes = {}
    
    # Gather environmental data with graceful defaults
    light_level = env_data.get("light", 500)  # Default daylight level
    temperature = env_data.get("temperature", 20)  # Default comfortable temp
    humidity = env_data.get("humidity", 50)  # Default humidity
    
    # Assess node health across the swarm
    node_health_scores = []
    total_available_memory = 0
    critical_nodes = 0
    
    if nodes:
        for node_id, node_info in nodes.items():
            try:
                # Extract health metrics from node info
                memory_gb = node_info.get("available_memory", 4.0)
                cpu_usage = node_info.get("cpu_usage", 50.0)
                
                # Calculate health score (0.0 to 1.0)
                memory_score = min(memory_gb / 8.0, 1.0)  # 8GB as healthy baseline
                cpu_score = max(0.0, (100.0 - cpu_usage) / 100.0)
                health_score = (memory_score + cpu_score) / 2.0
                
                node_health_scores.append(health_score)
                total_available_memory += memory_gb
                
                if health_score < 0.3:
                    critical_nodes += 1
                    
            except Exception as e:
                record_log(f"🌫️ Error assessing node {node_id} health: {e}")
                node_health_scores.append(0.5)  # Neutral score on error
    else:
        # If no nodes provided, assess local system health
        try:
            if PSUTIL_AVAILABLE:
                memory = psutil.virtual_memory()
                cpu_percent = psutil.cpu_percent(interval=0.1)
                
                local_memory_gb = memory.available / (1024 ** 3)
                local_memory_score = min(local_memory_gb / 8.0, 1.0)
                local_cpu_score = max(0.0, (100.0 - cpu_percent) / 100.0)
                local_health = (local_memory_score + local_cpu_score) / 2.0
                
                node_health_scores = [local_health]
                total_available_memory = local_memory_gb
                
                if local_health < 0.3:
                    critical_nodes = 1
            else:
                # Fallback assessment without psutil
                local_health = 0.6  # Assume moderate health
                node_health_scores = [local_health]
                total_available_memory = 8.0  # Assume 8GB available
                
                record_log("🌫️ Using fallback health assessment - psutil unavailable")
                
        except Exception as e:
            record_log(f"🌫️ Error assessing local system health: {e}")
            node_health_scores = [0.5]
            total_available_memory = 4.0
    
    # Calculate overall swarm vitality
    average_health = sum(node_health_scores) / len(node_health_scores) if node_health_scores else 0.5
    node_count = len(node_health_scores)
    
    # Determine breathing rhythm based on environmental and health factors
    breathing_params = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "environmental_factors": {
            "light_level": light_level,
            "temperature": temperature,
            "humidity": humidity
        },
        "swarm_vitality": {
            "average_health": average_health,
            "node_count": node_count,
            "critical_nodes": critical_nodes,
            "total_memory_gb": total_available_memory
        }
    }
    
    # Adaptive rhythm determination
    if light_level < 20 or average_health < 0.3 or critical_nodes > node_count / 2:
        # Twilight mode or stressed swarm
        rhythm = "dormant"
        frequency_multiplier = 0.3
        processing_intensity = "minimal"
        cycle_adjustment = 2.5
        
        record_log("🌬️ The swarm rests in twilight, its breath slowing to conserve strength")
        
    elif light_level < 100 or average_health < 0.6:
        # Low light or moderate stress
        rhythm = "restful"
        frequency_multiplier = 0.6
        processing_intensity = "reduced"
        cycle_adjustment = 1.8
        
        record_log("🌿 The swarm breathes gently, conserving energy while maintaining awareness")
        
    elif light_level > 800 and average_health > 0.8 and critical_nodes == 0:
        # Bright conditions with healthy swarm
        rhythm = "vibrant"
        frequency_multiplier = 1.3
        processing_intensity = "enhanced"
        cycle_adjustment = 0.7
        
        record_log("🌸 The swarm pulses brightly, roots vibrant with abundant light and vitality")
        
    else:
        # Normal conditions
        rhythm = "balanced"
        frequency_multiplier = 1.0
        processing_intensity = "normal"
        cycle_adjustment = 1.0
        
        record_log("🌱 The swarm maintains steady breath, balanced between growth and conservation")
    
    # Environmental adaptations
    adaptations = []
    
    # Temperature adaptations
    if temperature < 10:
        adaptations.append("cold_conservation")
        frequency_multiplier *= 0.8
        record_log("🌨️ Cold adaptation: reducing metabolic rhythm")
    elif temperature > 35:
        adaptations.append("heat_stress")
        frequency_multiplier *= 0.9
        record_log("🌡️ Heat stress: moderating processing to prevent overheating")
    
    # Humidity adaptations
    if humidity > 80:
        adaptations.append("high_humidity")
        cycle_adjustment *= 1.1
        record_log("🌧️ High humidity detected: adjusting breath cycles for moisture conditions")
    elif humidity < 20:
        adaptations.append("low_humidity")
        cycle_adjustment *= 0.95
        record_log("🌵 Dry conditions: tightening breath cycles to conserve moisture")
    
    # Compile final breathing parameters
    breathing_params.update({
        "rhythm_state": rhythm,
        "frequency_multiplier": frequency_multiplier,
        "processing_intensity": processing_intensity,
        "cycle_adjustment": cycle_adjustment,
        "environmental_adaptations": adaptations,
        "recommendations": {
            "next_assessment_in_seconds": int(300 * cycle_adjustment),  # 5 minutes base
            "priority_processing": processing_intensity in ["enhanced", "normal"],
            "enable_background_tasks": rhythm in ["balanced", "vibrant"],
            "fragment_coordination": rhythm in ["vibrant", "balanced"]
        }
    })
    
    # Log the rhythm assessment
    record_log(
        f"🌬️ Verdant rhythm assessed: {rhythm} "
        f"(freq: {frequency_multiplier:.2f}x, health: {average_health:.2f}, "
        f"light: {light_level}, nodes: {node_count})"
    )
    
    return breathing_params

def assess_environmental_vitality(env_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """🌱 Assesses environmental conditions for adaptive breathing patterns.
    
    Evaluates multiple environmental factors to determine optimal system response,
    supporting the swarm's ability to adapt to changing conditions.
    
    Args:
        env_data: Environmental sensor data (light, temperature, humidity, etc.)
        
    Returns:
        Dictionary containing environmental assessment and adaptation suggestions
    """
    if env_data is None:
        env_data = {}
    
    assessment = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "environmental_factors": {},
        "vitality_score": 0.0,
        "adaptation_mode": "balanced",
        "recommendations": []
    }
    
    try:
        # Extract environmental parameters with defaults
        light_level = env_data.get("light", 500)
        temperature = env_data.get("temperature", 20)
        humidity = env_data.get("humidity", 50)
        air_quality = env_data.get("air_quality", 75)  # 0-100 scale
        noise_level = env_data.get("noise_level", 40)  # dB
        
        assessment["environmental_factors"] = {
            "light_level": light_level,
            "temperature": temperature,
            "humidity": humidity,
            "air_quality": air_quality,
            "noise_level": noise_level
        }
        
        # Score individual factors (0.0 to 1.0)
        scores = {}
        
        # Light scoring (optimal range: 200-800 lux)
        if 200 <= light_level <= 800:
            scores["light"] = 1.0
        elif 100 <= light_level < 200 or 800 < light_level <= 1000:
            scores["light"] = 0.7
        elif 50 <= light_level < 100 or 1000 < light_level <= 1500:
            scores["light"] = 0.5
        else:
            scores["light"] = 0.2
        
        # Temperature scoring (optimal range: 18-24°C)
        if 18 <= temperature <= 24:
            scores["temperature"] = 1.0
        elif 15 <= temperature < 18 or 24 < temperature <= 27:
            scores["temperature"] = 0.7
        elif 10 <= temperature < 15 or 27 < temperature <= 32:
            scores["temperature"] = 0.5
        else:
            scores["temperature"] = 0.2
        
        # Humidity scoring (optimal range: 40-60%)
        if 40 <= humidity <= 60:
            scores["humidity"] = 1.0
        elif 30 <= humidity < 40 or 60 < humidity <= 70:
            scores["humidity"] = 0.7
        elif 20 <= humidity < 30 or 70 < humidity <= 80:
            scores["humidity"] = 0.5
        else:
            scores["humidity"] = 0.2
        
        # Air quality scoring (higher is better)
        scores["air_quality"] = min(air_quality / 100.0, 1.0)
        
        # Noise level scoring (lower is better, optimal < 50 dB)
        if noise_level <= 40:
            scores["noise"] = 1.0
        elif noise_level <= 55:
            scores["noise"] = 0.7
        elif noise_level <= 70:
            scores["noise"] = 0.5
        else:
            scores["noise"] = 0.2
        
        # Calculate overall vitality score
        assessment["vitality_score"] = sum(scores.values()) / len(scores)
        assessment["factor_scores"] = scores
        
        # Determine adaptation mode
        vitality = assessment["vitality_score"]
        if vitality >= 0.8:
            assessment["adaptation_mode"] = "optimal"
            assessment["mode_emoji"] = "🌸"
            record_log("🌸 Environmental conditions optimal - peak performance mode")
        elif vitality >= 0.6:
            assessment["adaptation_mode"] = "favorable"
            assessment["mode_emoji"] = "🌿"
            record_log("🌿 Environmental conditions favorable - enhanced operations")
        elif vitality >= 0.4:
            assessment["adaptation_mode"] = "balanced"
            assessment["mode_emoji"] = "🌱"
            record_log("🌱 Environmental conditions balanced - standard operations")
        elif vitality >= 0.2:
            assessment["adaptation_mode"] = "conservation"
            assessment["mode_emoji"] = "🌫️"
            record_log("🌫️ Environmental stress detected - conservation mode recommended")
        else:
            assessment["adaptation_mode"] = "survival"
            assessment["mode_emoji"] = "🌪️"
            record_log("🌪️ Harsh environmental conditions - survival mode activated")
        
        # Generate specific recommendations
        recommendations = []
        
        if scores["light"] < 0.5:
            if light_level < 100:
                recommendations.append("Low light detected - reduce visual processing")
            else:
                recommendations.append("Bright light detected - implement glare protection")
        
        if scores["temperature"] < 0.5:
            if temperature < 15:
                recommendations.append("Cold conditions - increase processing frequency for warmth")
            else:
                recommendations.append("High temperature - reduce CPU-intensive operations")
        
        if scores["humidity"] < 0.5:
            if humidity < 30:
                recommendations.append("Dry air - monitor for static discharge risks")
            else:
                recommendations.append("High humidity - monitor for condensation risks")
        
        if scores["air_quality"] < 0.6:
            recommendations.append("Poor air quality - implement health monitoring")
        
        if scores["noise"] < 0.6:
            recommendations.append("High noise levels - reduce audio processing sensitivity")
        
        assessment["recommendations"] = recommendations
        
        record_log(
            f"🌬️ Environmental vitality: {assessment['adaptation_mode']} "
            f"{assessment['mode_emoji']} (score: {vitality:.3f})"
        )
        
        return assessment
        
    except Exception as e:
        record_log(f"🌫️ Environmental assessment whispered error: {e}")
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error": str(e),
            "vitality_score": 0.5,
            "adaptation_mode": "fallback",
            "recommendations": ["Use fallback environmental parameters"]
        }

def adapt_breathing_to_system_load(system_metrics: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """🌬️ Adapts breathing patterns based on current system load and performance.
    
    Monitors system resources and adjusts breathing frequency to optimize
    performance while maintaining stability.
    
    Args:
        system_metrics: System performance metrics (CPU, memory, disk, network)
        
    Returns:
        Dictionary containing breathing adaptations and system recommendations
    """
    if system_metrics is None:
        system_metrics = {}
    
    adaptation = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "system_status": {},
        "breathing_adjustments": {},
        "load_state": "unknown",
        "recommendations": []
    }
    
    try:
        # Gather system metrics
        if PSUTIL_AVAILABLE:
            cpu_percent = system_metrics.get("cpu_percent", psutil.cpu_percent(interval=0.1))
            memory = psutil.virtual_memory()
            memory_percent = system_metrics.get("memory_percent", memory.percent)
            disk_io = psutil.disk_io_counters() if hasattr(psutil, 'disk_io_counters') else None
            network_io = psutil.net_io_counters() if hasattr(psutil, 'net_io_counters') else None
            
            load_average = None
            if hasattr(psutil, 'getloadavg'):
                try:
                    load_average = psutil.getloadavg()[0]  # 1-minute load average
                except (OSError, AttributeError):
                    load_average = None
        else:
            # Fallback when psutil unavailable
            cpu_percent = system_metrics.get("cpu_percent", 30.0)
            memory_percent = system_metrics.get("memory_percent", 50.0)
            disk_io = None
            network_io = None
            load_average = None
            
            record_log("🌫️ Using fallback system metrics - psutil unavailable")
        
        adaptation["system_status"] = {
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
            "load_average": load_average,
            "disk_io_available": disk_io is not None,
            "network_io_available": network_io is not None
        }
        
        # Calculate system load score (0.0 = no load, 1.0 = maximum load)
        load_factors = []
        
        # CPU load factor
        cpu_load = cpu_percent / 100.0
        load_factors.append(cpu_load)
        
        # Memory load factor
        memory_load = memory_percent / 100.0
        load_factors.append(memory_load)
        
        # Load average factor (if available)
        if load_average is not None:
            # Normalize load average (assume 4-core system as baseline)
            normalized_load = min(load_average / 4.0, 1.0)
            load_factors.append(normalized_load)
        
        # Calculate overall system load
        overall_load = sum(load_factors) / len(load_factors)
        adaptation["overall_load"] = overall_load
        
        # Determine load state and breathing adaptations
        breathing_adjustments = {}
        
        if overall_load <= 0.2:
            # Very low load - opportunity for enhanced processing
            adaptation["load_state"] = "idle"
            adaptation["load_emoji"] = "🌸"
            breathing_adjustments = {
                "frequency_multiplier": 1.2,
                "processing_intensity": "enhanced",
                "background_tasks": "enabled",
                "learning_cycles": "increased",
                "exploration_mode": "active"
            }
            adaptation["recommendations"].extend([
                "Low system load detected - enable enhanced processing",
                "Activate background learning and exploration",
                "Increase neural fragment training frequency"
            ])
            record_log("🌸 System idle - activating enhanced breathing and learning modes")
            
        elif overall_load <= 0.4:
            # Low load - normal enhanced operations
            adaptation["load_state"] = "light"
            adaptation["load_emoji"] = "🌿"
            breathing_adjustments = {
                "frequency_multiplier": 1.1,
                "processing_intensity": "normal_plus",
                "background_tasks": "enabled",
                "learning_cycles": "normal",
                "exploration_mode": "moderate"
            }
            adaptation["recommendations"].extend([
                "Light system load - maintain enhanced operations",
                "Continue background tasks and learning"
            ])
            record_log("🌿 Light system load - maintaining enhanced breathing patterns")
            
        elif overall_load <= 0.6:
            # Moderate load - balanced operations
            adaptation["load_state"] = "moderate"
            adaptation["load_emoji"] = "🌱"
            breathing_adjustments = {
                "frequency_multiplier": 1.0,
                "processing_intensity": "normal",
                "background_tasks": "selective",
                "learning_cycles": "reduced",
                "exploration_mode": "limited"
            }
            adaptation["recommendations"].extend([
                "Moderate system load - maintain balanced operations",
                "Prioritize essential tasks"
            ])
            record_log("🌱 Moderate system load - balanced breathing rhythm maintained")
            
        elif overall_load <= 0.8:
            # High load - conservation mode
            adaptation["load_state"] = "high"
            adaptation["load_emoji"] = "🌫️"
            breathing_adjustments = {
                "frequency_multiplier": 0.8,
                "processing_intensity": "reduced",
                "background_tasks": "minimal",
                "learning_cycles": "paused",
                "exploration_mode": "disabled"
            }
            adaptation["recommendations"].extend([
                "High system load - enter conservation mode",
                "Pause non-essential background tasks",
                "Reduce processing frequency to prevent overload"
            ])
            record_log("🌫️ High system load - entering conservation breathing mode")
            
        else:
            # Critical load - emergency conservation
            adaptation["load_state"] = "critical"
            adaptation["load_emoji"] = "🌪️"
            breathing_adjustments = {
                "frequency_multiplier": 0.5,
                "processing_intensity": "minimal",
                "background_tasks": "disabled",
                "learning_cycles": "suspended",
                "exploration_mode": "disabled"
            }
            adaptation["recommendations"].extend([
                "CRITICAL: System overload detected",
                "Emergency conservation mode activated",
                "Suspend all non-essential operations",
                "Monitor system stability closely"
            ])
            record_log("🌪️ CRITICAL: System overload - emergency breathing conservation activated")
        
        adaptation["breathing_adjustments"] = breathing_adjustments
        
        # Add specific resource recommendations
        if cpu_percent > 80:
            adaptation["recommendations"].append("High CPU usage - consider reducing neural complexity")
        
        if memory_percent > 85:
            adaptation["recommendations"].append("High memory usage - implement memory cleanup cycles")
        
        if load_average and load_average > 2.0:
            adaptation["recommendations"].append("High system load average - defer intensive operations")
        
        record_log(
            f"🌬️ System load adaptation: {adaptation['load_state']} {adaptation['load_emoji']} "
            f"(load: {overall_load:.3f}, freq: {breathing_adjustments.get('frequency_multiplier', 1.0):.2f}x)"
        )
        
        return adaptation
        
    except Exception as e:
        record_log(f"🌫️ System load adaptation whispered error: {e}")
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error": str(e),
            "load_state": "error",
            "breathing_adjustments": {"frequency_multiplier": 1.0, "processing_intensity": "fallback"},
            "recommendations": ["Use fallback breathing parameters due to monitoring error"]
        }

def monitor_swarm_environmental_harmony(swarm_nodes: Optional[Dict] = None, 
                                      env_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """🌌 Monitors harmony between swarm nodes and environmental conditions.
    
    Assesses how well the swarm is adapting to its environment and suggests
    coordinated adjustments across all nodes.
    
    Args:
        swarm_nodes: Dictionary of swarm nodes with their status
        env_data: Environmental data affecting the swarm
        
    Returns:
        Dictionary containing harmony assessment and swarm-wide recommendations
    """
    if swarm_nodes is None:
        swarm_nodes = {}
    if env_data is None:
        env_data = {}
    
    harmony = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "environmental_assessment": {},
        "swarm_status": {},
        "harmony_score": 0.0,
        "synchronization_state": "unknown",
        "coordinated_adaptations": {},
        "swarm_recommendations": []
    }
    
    try:
        # Assess environmental conditions
        env_assessment = assess_environmental_vitality(env_data)
        harmony["environmental_assessment"] = env_assessment
        
        # Assess swarm node status
        node_count = len(swarm_nodes)
        active_nodes = 0
        node_adaptations = []
        total_health = 0.0
        
        for node_id, node_info in swarm_nodes.items():
            node_status = node_info.get("status", "unknown")
            node_health = node_info.get("health_score", 0.5)
            node_adaptation = node_info.get("environmental_adaptation", "unknown")
            
            if node_status == "active":
                active_nodes += 1
                total_health += node_health
                node_adaptations.append(node_adaptation)
        
        swarm_health = (total_health / active_nodes) if active_nodes > 0 else 0.0
        
        harmony["swarm_status"] = {
            "total_nodes": node_count,
            "active_nodes": active_nodes,
            "activity_ratio": active_nodes / node_count if node_count > 0 else 0.0,
            "average_health": swarm_health,
            "adaptation_diversity": len(set(node_adaptations)) if node_adaptations else 0
        }
        
        # Calculate harmony score
        env_vitality = env_assessment.get("vitality_score", 0.5)
        swarm_vitality = swarm_health
        coordination_factor = harmony["swarm_status"]["activity_ratio"]
        
        # Harmony is the product of environmental fitness and swarm coordination
        harmony["harmony_score"] = (env_vitality + swarm_vitality + coordination_factor) / 3.0
        
        # Determine synchronization state
        harmony_score = harmony["harmony_score"]
        if harmony_score >= 0.8:
            harmony["synchronization_state"] = "resonant"
            harmony["sync_emoji"] = "🌸"
            record_log("🌸 Swarm-environment resonance achieved - optimal harmony")
        elif harmony_score >= 0.6:
            harmony["synchronization_state"] = "synchronized"
            harmony["sync_emoji"] = "🌿"
            record_log("🌿 Swarm-environment synchronization good - stable harmony")
        elif harmony_score >= 0.4:
            harmony["synchronization_state"] = "adapting"
            harmony["sync_emoji"] = "🌱"
            record_log("🌱 Swarm-environment adaptation in progress - working toward harmony")
        elif harmony_score >= 0.2:
            harmony["synchronization_state"] = "struggling"
            harmony["sync_emoji"] = "🌫️"
            record_log("🌫️ Swarm-environment harmony struggling - adaptation needed")
        else:
            harmony["synchronization_state"] = "discord"
            harmony["sync_emoji"] = "🌪️"
            record_log("🌪️ Swarm-environment discord detected - emergency coordination needed")
        
        # Generate coordinated adaptations
        coordinated_adaptations = {}
        swarm_recommendations = []
        
        env_mode = env_assessment.get("adaptation_mode", "balanced")
        
        if env_mode == "optimal":
            coordinated_adaptations = {
                "breathing_frequency": "enhanced",
                "processing_mode": "peak_performance",
                "learning_cycles": "accelerated",
                "inter_node_communication": "high_bandwidth",
                "exploration_mode": "active"
            }
            swarm_recommendations.extend([
                "Optimal conditions detected - activate peak performance across swarm",
                "Increase inter-node communication and learning",
                "Enable advanced exploration and experimentation"
            ])
            
        elif env_mode == "favorable":
            coordinated_adaptations = {
                "breathing_frequency": "elevated",
                "processing_mode": "enhanced",
                "learning_cycles": "increased",
                "inter_node_communication": "normal_plus",
                "exploration_mode": "moderate"
            }
            swarm_recommendations.extend([
                "Favorable conditions - enhance swarm operations",
                "Maintain good inter-node coordination",
                "Continue active learning and adaptation"
            ])
            
        elif env_mode == "conservation":
            coordinated_adaptations = {
                "breathing_frequency": "reduced",
                "processing_mode": "efficient",
                "learning_cycles": "minimal",
                "inter_node_communication": "essential_only",
                "exploration_mode": "disabled"
            }
            swarm_recommendations.extend([
                "Environmental stress - coordinate conservation efforts",
                "Reduce non-essential inter-node traffic",
                "Focus on core operations and stability"
            ])
            
        elif env_mode == "survival":
            coordinated_adaptations = {
                "breathing_frequency": "minimal",
                "processing_mode": "emergency",
                "learning_cycles": "suspended",
                "inter_node_communication": "critical_only",
                "exploration_mode": "disabled"
            }
            swarm_recommendations.extend([
                "EMERGENCY: Harsh environmental conditions",
                "Activate swarm-wide survival protocols",
                "Minimize all non-critical operations",
                "Maintain only essential node coordination"
            ])
        
        # Add node-specific recommendations
        if harmony["swarm_status"]["activity_ratio"] < 0.8:
            swarm_recommendations.append("Node activity below optimal - investigate inactive nodes")
        
        if harmony["swarm_status"]["adaptation_diversity"] < 2 and node_count > 2:
            swarm_recommendations.append("Low adaptation diversity - encourage varied node responses")
        
        harmony["coordinated_adaptations"] = coordinated_adaptations
        harmony["swarm_recommendations"] = swarm_recommendations
        
        record_log(
            f"🌌 Swarm-environment harmony: {harmony['synchronization_state']} "
            f"{harmony['sync_emoji']} (score: {harmony_score:.3f}, nodes: {active_nodes})"
        )
        
        return harmony
        
    except Exception as e:
        record_log(f"🌫️ Swarm harmony monitoring whispered error: {e}")
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error": str(e),
            "harmony_score": 0.0,
            "synchronization_state": "error",
            "coordinated_adaptations": {},
            "swarm_recommendations": ["Monitor harmony system for errors"]
        }