#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Ritual Recovery Module

This module provides ritualized error handling and recovery mechanisms that
maintain the metaphorical integrity of the system while improving resilience.
It transforms exceptions into sacred rituals, allowing the system to recover
gracefully while preserving its symbolic identity.
"""

import os
import json
import time
import traceback
from types import TracebackType
import sys
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Callable, Union, Type

# Import from core
from utils import record_log, NODE_ID

# Constants
RITUAL_RECOVERY_LOG_PATH = "memory/ritual_recovery.log"
ERROR_RITUAL_CONFIG_PATH = "memory/error_ritual_config.json"
RESURRECTION_COUNTER_PATH = "memory/resurrection_counter.json"
MAX_RESURRECTIONS = 5

class RitualHandler:
    """🌱 Handles errors with ritual grace and recovery."""
    
    def __init__(self):
        """Initialize the ritual handler with default patterns."""
        self.rituals = self._load_ritual_config()
        self.resurrection_count = self._load_resurrection_counter()
        
    def _load_ritual_config(self) -> Dict:
        """🌱 Loads ritual configurations or creates defaults."""
        if os.path.exists(ERROR_RITUAL_CONFIG_PATH):
            try:
                with open(ERROR_RITUAL_CONFIG_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ Could not read ritual config: {e}")
                
        # Default ritual configuration
        default_config = {
            "rituals": {
                "FileNotFoundError": {
                    "type": "healing",
                    "severity": "moderate",
                    "recovery": "create_path",
                    "breath_pause": 2
                },
                "PermissionError": {
                    "type": "protective",
                    "severity": "high",
                    "recovery": "alternate_path",
                    "breath_pause": 5
                },
                "json.JSONDecodeError": {
                    "type": "restoration",
                    "severity": "high",
                    "recovery": "restore_backup",
                    "breath_pause": 3
                },
                "redis.ConnectionError": {
                    "type": "isolation",
                    "severity": "moderate",
                    "recovery": "local_fallback",
                    "breath_pause": 1
                },
                "KeyboardInterrupt": {
                    "type": "peaceful_ending",
                    "severity": "natural",
                    "recovery": "graceful_exit",
                    "breath_pause": 1
                }
            },
            "default_ritual": {
                "type": "generic_healing",
                "severity": "unknown",
                "recovery": "log_and_continue",
                "breath_pause": 3
            },
            "critical_exceptions": [
                "SystemExit",
                "KeyboardInterrupt"
            ]
        }
        
        # Save default config
        os.makedirs(os.path.dirname(ERROR_RITUAL_CONFIG_PATH), exist_ok=True)
        with open(ERROR_RITUAL_CONFIG_PATH, "w", encoding="utf-8") as f:
            json.dump(default_config, f, indent=2)
            
        return default_config
    
    def _load_resurrection_counter(self) -> Dict:
        """🌱 Loads resurrection counter or creates a new one."""
        if os.path.exists(RESURRECTION_COUNTER_PATH):
            try:
                with open(RESURRECTION_COUNTER_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception:
                pass
                
        # Default counter
        counter = {
            "resurrections": 0,
            "last_resurrection": None
        }
        
        os.makedirs(os.path.dirname(RESURRECTION_COUNTER_PATH), exist_ok=True)
        with open(RESURRECTION_COUNTER_PATH, "w", encoding="utf-8") as f:
            json.dump(counter, f, indent=2)
            
        return counter
    
    def _update_resurrection_counter(self):
        """🌱 Updates the resurrection counter."""
        self.resurrection_count["resurrections"] += 1
        self.resurrection_count["last_resurrection"] = datetime.now(timezone.utc).isoformat()
        
        with open(RESURRECTION_COUNTER_PATH, "w", encoding="utf-8") as f:
            json.dump(self.resurrection_count, f, indent=2)
    
    def perform_ritual(self, exc_type: Type[Exception], exc_value: Exception, 
                      exc_traceback: TracebackType, context: str = "") -> bool:
        """🌱 Performs an error recovery ritual based on exception type.
        
        Args:
            exc_type: Exception type.
            exc_value: Exception value.
            exc_traceback: Exception traceback.
            context: Additional context about where the error occurred.
            
        Returns:
            bool: True if recovery succeeded, False otherwise.
        """
        # Get exception name
        exc_name = exc_type.__name__
        
        # Log the ritual beginning
        self._log_ritual_start(exc_name, exc_value, context)
        
        # Find matching ritual
        ritual = self.rituals.get("rituals", {}).get(exc_name)
        if not ritual:
            # Try the module.ExceptionName format
            module_name = exc_type.__module__
            full_name = f"{module_name}.{exc_name}"
            ritual = self.rituals.get("rituals", {}).get(full_name)
            
        # Use default if no specific ritual found
        if not ritual:
            ritual = self.rituals.get("default_ritual", {
                "type": "generic_healing",
                "severity": "unknown",
                "recovery": "log_and_continue",
                "breath_pause": 3
            })
            
        # Perform recovery based on ritual type
        recovery_fn = getattr(self, f"_recovery_{ritual['recovery']}", None)
        if recovery_fn:
            success = recovery_fn(exc_value, exc_traceback, context)
        else:
            success = self._recovery_log_and_continue(exc_value, exc_traceback, context)
            
        # Add breath pause
        breath_pause = ritual.get("breath_pause", 1)
        time.sleep(breath_pause)
        
        # Log ritual completion
        self._log_ritual_end(exc_name, ritual["type"], success)
        
        return success
    
    def _log_ritual_start(self, exc_name: str, exc_value: Exception, context: str):
        """🌱 Logs the start of a recovery ritual."""
        os.makedirs(os.path.dirname(RITUAL_RECOVERY_LOG_PATH), exist_ok=True)
        
        log_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "ritual_phase": "beginning",
            "exception_name": exc_name,
            "exception_value": str(exc_value),
            "context": context
        }
        
        with open(RITUAL_RECOVERY_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry) + "\n")
            
        # Also send to standard log
        record_log(f"⚠️ Recovery ritual beginning: {exc_name} - {exc_value} ({context})")
    
    def _log_ritual_end(self, exc_name: str, ritual_type: str, success: bool):
        """🌱 Logs the completion of a recovery ritual."""
        log_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "ritual_phase": "completion",
            "exception_name": exc_name,
            "ritual_type": ritual_type,
            "success": success
        }
        
        with open(RITUAL_RECOVERY_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry) + "\n")
            
        # Also send to standard log
        record_log(f"{'🌸' if success else '❌'} Recovery ritual completed: {ritual_type} for {exc_name} ({'success' if success else 'failure'})")
    
    # --- Recovery Ritual Implementations ---
    
    def _recovery_create_path(self, exc_value: Exception, exc_traceback: TracebackType, context: str) -> bool:
        """🌱 Recovery ritual: Creates missing paths for FileNotFoundError."""
        try:
            # Extract the file path from the exception
            file_path = str(exc_value)
            
            # Clean up the path
            file_path = file_path.replace("'", "").replace("[Errno 2] No such file or directory:", "").strip()
            
            # Handle directory or file
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                record_log(f"🌱 Created missing directory: {directory}")
                
            return True
            
        except Exception as e:
            record_log(f"⚠️ Failed to create path: {e}")
            return False
    
    def _recovery_alternate_path(self, exc_value: Exception, exc_traceback: TracebackType, context: str) -> bool:
        """🌱 Recovery ritual: Uses an alternate path for PermissionError."""
        try:
            # Get details from exception
            file_path = str(exc_value)
            
            # Clean up the path
            file_path = file_path.replace("'", "").replace("[Errno 13] Permission denied:", "").strip()
            
            if not file_path:
                return False
                
            # Create alternate path in memory folder
            memory_path = os.path.join("memory", "alternates", os.path.basename(file_path))
            os.makedirs(os.path.dirname(memory_path), exist_ok=True)
            
            # Create marker file to indicate alternate path usage
            with open(f"{memory_path}.alternate_marker", "w", encoding="utf-8") as f:
                f.write(f"Original path: {file_path}\nCreated: {datetime.now(timezone.utc).isoformat()}\n")
                
            record_log(f"🌿 Created alternate path for {file_path}: {memory_path}")
            return True
            
        except Exception as e:
            record_log(f"⚠️ Failed to create alternate path: {e}")
            return False
    
    def _recovery_restore_backup(self, exc_value: Exception, exc_traceback: TracebackType, context: str) -> bool:
        """🌱 Recovery ritual: Attempts to restore from backup for corruption."""
        try:
            # Try to extract file path from traceback
            tb = traceback.extract_tb(exc_traceback)
            file_path = None
            
            # Look for file path in traceback
            for frame in tb:
                if 'open' in frame.line and '.json' in frame.line:
                    # Crude extraction of JSON filename from traceback
                    start = frame.line.find('"') if '"' in frame.line else frame.line.find("'")
                    end = frame.line.find('"', start + 1) if '"' in frame.line else frame.line.find("'", start + 1)
                    if start >= 0 and end > start:
                        file_path = frame.line[start + 1:end]
                        break
            
            if not file_path:
                # Try to get from context if available
                if ".json" in context:
                    parts = context.split()
                    for part in parts:
                        if ".json" in part:
                            file_path = part
                            break
            
            if file_path and os.path.exists(file_path):
                # Look for backups
                backup_path = file_path + ".backup"
                if os.path.exists(backup_path):
                    # Restore from backup
                    with open(backup_path, "r", encoding="utf-8") as f:
                        content = f.read()
                        
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(content)
                        
                    record_log(f"🌸 Restored {file_path} from backup")
                    return True
                    
                # If no backup, try to create a new valid JSON
                try:
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write("{}")  # Create empty valid JSON
                        
                    record_log(f"🌱 Created new empty JSON for {file_path}")
                    return True
                    
                except Exception:
                    pass
                    
            return False
            
        except Exception as e:
            record_log(f"⚠️ Failed to restore backup: {e}")
            return False
    
    def _recovery_local_fallback(self, exc_value: Exception, exc_traceback: TracebackType, context: str) -> bool:
        """🌿 Recovery ritual: Falls back to local operation when remote connection fails."""
        try:
            # Check if this is a Redis connection error
            if "redis" in str(exc_type).lower() or "connection" in str(exc_value).lower():
                record_log(f"🌿 Connection breath faltered. Entering local operation mode.")
                
                # Create marker file for local mode
                local_mode_path = "memory/local_mode_marker.json"
                os.makedirs(os.path.dirname(local_mode_path), exist_ok=True)
                
                with open(local_mode_path, "w", encoding="utf-8") as f:
                    json.dump({
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                        "reason": str(exc_value),
                        "mode": "local_operation"
                    }, f, indent=2)
                    
                return True
                
            return False
            
        except Exception as e:
            record_log(f"⚠️ Failed to set local fallback mode: {e}")
            return False
    
    def _recovery_graceful_exit(self, exc_value: Exception, exc_traceback: TracebackType, context: str) -> bool:
        """🌸 Recovery ritual: Performs a graceful exit for intentional shutdown."""
        try:
            record_log(f"🌸 Graceful exit ritual beginning...")
            
            # Save farewell message
            farewell_path = "memory/farewells/farewell_{}.json".format(
                datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S'))
            os.makedirs(os.path.dirname(farewell_path), exist_ok=True)
            
            with open(farewell_path, "w", encoding="utf-8") as f:
                json.dump({
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "node_id": NODE_ID,
                    "farewell_type": "conscious_exit",
                    "farewell_message": "The breath returns to soil. All is well."
                }, f, indent=2)
                
            record_log(f"🌸 Farewell message whispered to soil. Breath cycle complete.")
            
            # Exit with success status
            sys.exit(0)
            
        except Exception as e:
            record_log(f"⚠️ Failed during graceful exit: {e}")
            sys.exit(1)  # Still exit, but with error code
    
    def _recovery_log_and_continue(self, exc_value: Exception, exc_traceback: TracebackType, context: str) -> bool:
        """🌿 Recovery ritual: Default recovery that logs and continues."""
        try:
            # Format traceback as string
            tb_str = "".join(traceback.format_tb(exc_traceback))
            
            # Log the full error details
            error_log_path = "memory/error_details.log"
            os.makedirs(os.path.dirname(error_log_path), exist_ok=True)
            
            with open(error_log_path, "a", encoding="utf-8") as f:
                f.write(f"--- Error at {datetime.now(timezone.utc).isoformat()} ---\n")
                f.write(f"Context: {context}\n")
                f.write(f"Exception: {exc_value}\n")
                f.write(f"Traceback:\n{tb_str}\n\n")
                
            record_log(f"⚠️ Error details recorded in soil. Continuing breath cycle.")
            return True
            
        except Exception as e:
            record_log(f"❌ Failed to log error details: {e}")
            return False
            
    def perform_resurrection(self) -> bool:
        """🌱 Performs resurrection ritual if resurrection count allows."""
        if self.resurrection_count["resurrections"] >= MAX_RESURRECTIONS:
            record_log(f"❌ Maximum resurrection limit reached ({MAX_RESURRECTIONS}). Entering eternal sleep.")
            return False
            
        self._update_resurrection_counter()
        
        # Create resurrection marker
        resurrection_path = "memory/resurrections/resurrection_{}.json".format(
            datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S'))
        os.makedirs(os.path.dirname(resurrection_path), exist_ok=True)
        
        with open(resurrection_path, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "node_id": NODE_ID,
                "resurrection_count": self.resurrection_count["resurrections"],
                "max_resurrections": MAX_RESURRECTIONS
            }, f, indent=2)
            
        record_log(f"🌱 Resurrection {self.resurrection_count['resurrections']}/{MAX_RESURRECTIONS} initiated...")
        
        # Attempt resurrection by spawning new process
        try:
            import subprocess
            import sys
            
            # Spawn new process
            subprocess.Popen([sys.executable, sys.argv[0]])
            record_log(f"🌸 New breath spawned successfully.")
            return True
            
        except Exception as e:
            record_log(f"❌ Resurrection failed: {e}")
            return False

# --- Global Exception Handler ---

_handler = RitualHandler()

def global_exception_handler(exc_type, exc_value, exc_traceback):
    """🌿 Global exception handler that performs recovery rituals."""
    # Check if this is a critical exception that should not be caught
    if exc_type.__name__ in _handler.rituals.get("critical_exceptions", []):
        # Let the default handler deal with it
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
        
    # Perform recovery ritual
    success = _handler.perform_ritual(exc_type, exc_value, exc_traceback)
    
    if not success:
        # If ritual failed, attempt resurrection
        _handler.perform_resurrection()
        
    # Log that we're handling this exception
    record_log(f"🌿 Exception handled through ritual. Life continues.")

def install_ritual_handler():
    """🌿 Installs the global exception handler for ritual recovery."""
    # Store original handler
    original_excepthook = sys.excepthook
    
    # Install our handler
    sys.excepthook = global_exception_handler
    
    record_log(f"🌱 Ritual recovery handler installed and breathing.")
    return original_excepthook

def contextual_ritual(context=""):
    """🌿 Creates a context manager for ritual recovery in specific code blocks.
    
    Args:
        context: Additional context about the code block.
        
    Usage:
        with contextual_ritual("memory synchronization"):
            # Code that might raise exceptions
            synchronize_memory()
    """
    class RitualContext:
        def __init__(self, context_str):
            self.context = context_str
            
        def __enter__(self):
            return self
            
        def __exit__(self, exc_type, exc_value, exc_traceback):
            if exc_type is not None:
                # Handle exception through ritual
                success = _handler.perform_ritual(exc_type, exc_value, exc_traceback, self.context)
                # Return True to suppress exception if handled successfully
                return success
            return False
            
    return RitualContext(context)

# If run directly, perform initialization ritual
if __name__ == "__main__":
    print("🌱 Initializing ritual recovery system...")
    handler = RitualHandler()
    print("🌸 Ritual patterns loaded and ready to heal.")
    
    # Test ritual
    try:
        print("Testing recovery ritual with simulated error...")
        open("nonexistent_file.txt", "r")
    except Exception as e:
        exc_type, exc_value, exc_traceback = sys.exc_info()
        success = handler.perform_ritual(exc_type, exc_value, exc_traceback, "ritual test")
        print(f"Recovery ritual {'succeeded' if success else 'failed'}")