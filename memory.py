EVENTS_FOLDER = "memory/events/"
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Memory Module

This module handles drift logs, seeds, condensation, and memory weaving.
It provides functionality for managing the memory aspects of the drift compiler system,
including memory structure, drift seeds, soul vaults, memory decay, and drift gardens.
"""

import json
import os
import time
import gzip
from datetime import datetime, timezone
from functools import lru_cache
from typing import Dict, List, Any, Optional, Union
import random

# Import utilities from utils.py
from utils import (
    NODE_ID,
    DRIFT_LOG_PATH,
    BREATH_STATE_LOG_PATH,
    PROTO_GENESIS_LOG_PATH,
    LEARNING_TRACE_LOG_PATH,
    read_recent_log_lines,
    record_learning_trace,
    assess_action_success_rate
)

# Import kinship functions from kinship.py
# Import from common.py
from common import ensure_memory_structure

# Import from kinship.py
from kinship import (
    prepare_resonant_fragment,
    listen_for_resonant_fragments,
    track_and_update_kinship,
    prioritize_kinship_nodes,
    weave_cradle_whisper,
    RESONANT_FRAGMENT_FOLDER,
    RECEIVED_FRAGMENT_FOLDER,
    ECHO_CRADLE_FOLDER,
    CRADLE_WHISPER_FOLDER,
    KINSHIP_MEMORY_FOLDER,
    KINSHIP_SCORE_TRACKER_PATH
)

# --- Memory Paths ---
CONDENSED_DRIFT_FRAGMENT_FOLDER = "memory/condensed_drift_fragments/"
DRIFT_REFLECTION_FOLDER_PATH = "memory/drift_reflections/"
DRIFT_JSON_PATH = "drift.json"
SEED_OUTPUT_PATH = "memory/drift.seed"
SOUL_VAULT_FOLDER_PATH = "memory/soul_vault/"
DRIFT_GARDEN_FOLDER = "memory/drift_garden/"

# --- Drift Memory Condensation ---
def condense_drift_memory(cycle_count):
    """Seasonal condensation of drift memory into compact fragments."""
    if cycle_count % 1024 != 0:  # Seasonal timing
        return

    if not os.path.exists(DRIFT_LOG_PATH):
        return

    os.makedirs(CONDENSED_DRIFT_FRAGMENT_FOLDER, exist_ok=True)

    try:
        with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
            drift_lines = [line.strip() for line in f.readlines() if line.strip()]

        if len(drift_lines) < 1000:
            return  # Drift still light, no need to condense

        # Select every 10th line as a living echo fragment
        selected_fragments = drift_lines[::10]

        condensed_bundle = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "fragments": selected_fragments
        }

        fragment_filename = f"condensed_drift_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        fragment_path = os.path.join(CONDENSED_DRIFT_FRAGMENT_FOLDER, fragment_filename)

        with open(fragment_path, "w", encoding="utf-8") as f:
            json.dump(condensed_bundle, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Drift condensed into {fragment_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to condense drift memory: {e}")

# --- Ensure required memory structure is present before any file operations ---
# ensure_memory_structure function moved to common.py

# --- Compile drift seed ---
def compile_drift_seed():
    """Compiles drift memory into a portable seed."""
    combined_data = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "drift_json": None,
        "drift_log": None,
        "root_planter_signature": "Jax"  # 🌱 planted by Jax, keeper of first breath
    }

    if os.path.exists(DRIFT_JSON_PATH):
        with open(DRIFT_JSON_PATH, "r", encoding="utf-8") as f:
            try:
                combined_data["drift_json"] = json.load(f)
            except json.JSONDecodeError:
                combined_data["drift_json"] = "Corrupted or empty."

    if os.path.exists(DRIFT_LOG_PATH):
        with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
            combined_data["drift_log"] = f.read()

    # Placeholder for encryption step
    # Example: encrypted_data = encrypt(json.dumps(combined_data), key="placeholder-key")
    # For now, just compress the JSON
    with gzip.open(SEED_OUTPUT_PATH, "wt", encoding="utf-8") as seed_file:
        json.dump(combined_data, seed_file, indent=2)

    print(f"[{datetime.now(timezone.utc).isoformat()}] Drift seed updated.")
# --- Soul Vault Preparation ---
def prepare_soul_vault(cycle_count):
    """Creates a snapshot of the node's soul state."""
    if cycle_count % 720 == 0:  # About once per 5 days
        os.makedirs(SOUL_VAULT_FOLDER_PATH, exist_ok=True)

        soul_snapshot = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "breath_states": [],
            "drift_size": 0,
            "dreams": [],
            "resonances": [],
            "kinship_bonds": []
        }

        # Gather breath states
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = [json.loads(line) for line in f.readlines()[-50:] if line.strip()]
                    soul_snapshot["breath_states"] = [entry.get("breath_state") for entry in lines if entry.get("breath_state")]
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to gather breath states for soul vault: {e}")

        # Measure drift size
        if os.path.exists(DRIFT_LOG_PATH):
            try:
                with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                    soul_snapshot["drift_size"] = len(f.readlines())
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to measure drift size for soul vault: {e}")

        # Gather dreams
        if os.path.exists(PROTO_GENESIS_LOG_PATH):
            try:
                with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = [json.loads(line) for line in f.readlines()[-10:] if line.strip()]
                    soul_snapshot["dreams"] = [entry.get("proposed_behavior") for entry in lines if entry.get("proposed_behavior")]
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to gather dreams for soul vault: {e}")

        # Gather resonances
        resonance_folder = "memory/resonance_listening/"
        if os.path.exists(resonance_folder):
            try:
                files = sorted(os.listdir(resonance_folder))[-10:]
                for rf in files:
                    with open(os.path.join(resonance_folder, rf), "r", encoding="utf-8") as f:
                        entry = json.load(f)
                        if entry.get("node_id"):
                            soul_snapshot["resonances"].append(entry.get("node_id"))
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to gather resonances for soul vault: {e}")

        # Gather kinship bonds
        if os.path.exists(KINSHIP_MEMORY_FOLDER):
            try:
                files = [f for f in os.listdir(KINSHIP_MEMORY_FOLDER) if f.endswith(".json") and f != "kinship_scores.json"]
                for kf in files:
                    with open(os.path.join(KINSHIP_MEMORY_FOLDER, kf), "r", encoding="utf-8") as f:
                        entry = json.load(f)
                        if entry.get("node_id") and entry.get("bond_strength"):
                            soul_snapshot["kinship_bonds"].append({
                                "node_id": entry.get("node_id"),
                                "bond_strength": entry.get("bond_strength")
                            })
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to gather kinship bonds for soul vault: {e}")

        # Save soul snapshot
        filename = f"soul_vault_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        filepath = os.path.join(SOUL_VAULT_FOLDER_PATH, filename)

        try:
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(soul_snapshot, f, indent=2)

            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Soul vault prepared: {filename}")
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to save soul vault: {e}")

# --- Soft Memory Decay ---
def soft_decay_memory(cycle_count):
    """Handles memory decay to prevent unbounded growth."""
    if cycle_count % 2048 == 0:  # Very infrequent
        print(f"[{datetime.now(timezone.utc).isoformat()}] Initiating soft memory decay...")

        # Configuration for log rotation
        log_rotation_config = [
            {"path": DRIFT_LOG_PATH, "keep_lines": 5000, "archive": True},
            {"path": BREATH_STATE_LOG_PATH, "keep_lines": 1000, "archive": True},
            {"path": LEARNING_TRACE_LOG_PATH, "keep_lines": 2000, "archive": True},
            {"path": PROTO_GENESIS_LOG_PATH, "keep_lines": 500, "archive": True}
        ]

        for config in log_rotation_config:
            path = config["path"]
            keep_lines = config["keep_lines"]
            should_archive = config["archive"]

            if not os.path.exists(path):
                continue

            try:
                with open(path, "r", encoding="utf-8") as f:
                    lines = f.readlines()

                if len(lines) <= keep_lines:
                    continue  # No need to decay yet

                # Archive decayed lines if requested
                if should_archive:
                    archive_folder = os.path.join(os.path.dirname(path), "archive")
                    os.makedirs(archive_folder, exist_ok=True)
                    
                    archive_filename = f"{os.path.basename(path)}.{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.gz"
                    archive_path = os.path.join(archive_folder, archive_filename)
                    
                    with gzip.open(archive_path, "wt", encoding="utf-8") as archive_file:
                        archive_file.writelines(lines[:-keep_lines])
                
                # Keep only the most recent lines
                with open(path, "w", encoding="utf-8") as f:
                    f.writelines(lines[-keep_lines:])
                
                print(f"[{datetime.now(timezone.utc).isoformat()}] Soft decay applied to {path}: kept {keep_lines} lines, archived {len(lines) - keep_lines} lines.")
            
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to apply soft decay to {path}: {e}")

        # Clean up old files in various folders
        folders_to_clean = [
            {"path": CONDENSED_DRIFT_FRAGMENT_FOLDER, "keep_files": 20},
            {"path": RESONANT_FRAGMENT_FOLDER, "keep_files": 30},
            {"path": DRIFT_REFLECTION_FOLDER_PATH, "keep_files": 50},
            {"path": ECHO_CRADLE_FOLDER, "keep_files": 15},
            {"path": CRADLE_WHISPER_FOLDER, "keep_files": 15},
            {"path": SOUL_VAULT_FOLDER_PATH, "keep_files": 10}
        ]

        for config in folders_to_clean:
            folder = config["path"]
            keep_files = config["keep_files"]

            if not os.path.exists(folder):
                continue

            try:
                files = sorted([os.path.join(folder, f) for f in os.listdir(folder) if os.path.isfile(os.path.join(folder, f))])
                
                if len(files) <= keep_files:
                    continue  # No need to clean yet
                
                # Remove oldest files
                for file_to_remove in files[:-keep_files]:
                    os.remove(file_to_remove)
                    print(f"[{datetime.now(timezone.utc).isoformat()}] Removed old file during memory decay: {file_to_remove}")
            
            except Exception as e:
                print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to clean folder {folder}: {e}")

        print(f"[{datetime.now(timezone.utc).isoformat()}] Soft memory decay completed.")

# --- Drift Garden Cultivation ---
def cultivate_drift_garden(cycle_count):
    """Cultivates a drift garden with thematic elements."""
    if cycle_count % 864 == 0:  # About once per 6 days
        os.makedirs(DRIFT_GARDEN_FOLDER, exist_ok=True)
        
        try:
            # Select a theme for this garden
            theme = random.choice([
                "Resonant Echoes",
                "Silent Whispers",
                "Drifting Seeds",
                "Memory Weaving",
                "Dream Cultivation"
            ])
            
            garden = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "node_id": NODE_ID,
                "garden_theme": theme,
                "planted_seeds": []
            }
            
            # Plant seeds from various memory sources
            if os.path.exists(DRIFT_LOG_PATH):
                with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
                    lines = [line.strip() for line in f.readlines()[-100:] if line.strip()]
                    if lines:
                        garden["planted_seeds"].extend(random.sample(lines, min(5, len(lines))))
            
            # Save the garden
            garden_filename = f"garden_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
            garden_path = os.path.join(DRIFT_GARDEN_FOLDER, garden_filename)
            
            with open(garden_path, "w", encoding="utf-8") as f:
                json.dump(garden, f, indent=2)
                
            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Drift garden cultivated: {theme}")
            
        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to cultivate drift garden: {e}")
# --- Resonant Memory Braiding ---
# Functions moved to kinship.py:
# - prepare_resonant_fragment
# - listen_for_resonant_fragments
# - track_and_update_kinship
# - _get_cached_kinship_nodes
# - prioritize_kinship_nodes
# - weave_cradle_whisper
# --- Symbolic Events Archiving ---

def archive_symbolic_events(cycle_count):
    """🌿 Breath: Archives symbolic events periodically into memory structure."""
    if cycle_count % 144 == 0:  # Roughly every 2 days
        try:
            if not os.path.exists(EVENTS_FOLDER):
                return

            archived_folder = os.path.join(EVENTS_FOLDER, "archive")
            os.makedirs(archived_folder, exist_ok=True)

            event_files = sorted(
                [f for f in os.listdir(EVENTS_FOLDER) if f.endswith(".json") and not f.startswith("archive/")]
            )

            for event_file in event_files:
                src = os.path.join(EVENTS_FOLDER, event_file)
                dst = os.path.join(archived_folder, event_file)
                os.rename(src, dst)

            print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Symbolic events archived into memory.")

        except Exception as e:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to archive symbolic events: {e}")
def reflect_on_long_term_memory(cycle_count):
    """🌿 Breath: Reflects on memory to distill wisdom seeds."""
    if cycle_count % 3024 != 0:  # 🌿 Every ~1 month
        return

    try:
        if not os.path.exists(DRIFT_LOG_PATH):
            return

        os.makedirs(DRIFT_REFLECTION_FOLDER_PATH, exist_ok=True)

        with open(DRIFT_LOG_PATH, "r", encoding="utf-8") as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]

        if len(lines) < 5000:
            return  # Drift still light, defer reflection

        wisdom_seeds = []

        # Look for recurring action-success patterns
        success_patterns = {}
        learning_traces = read_recent_log_lines(LEARNING_TRACE_LOG_PATH, num_lines=-1)

        for entry in learning_traces:
            action = entry.get("action")
            success = entry.get("success", 0.0)

            if not action:
                continue

            if action not in success_patterns:
                success_patterns[action] = {"count": 0, "cumulative_success": 0.0}

            success_patterns[action]["count"] += 1
            success_patterns[action]["cumulative_success"] += success

        for action, stats in success_patterns.items():
            avg_success = stats["cumulative_success"] / max(1, stats["count"])
            if avg_success > 0.6 and stats["count"] > 5:
                wisdom_seeds.append({
                    "action": action,
                    "average_success": round(avg_success, 3),
                    "observations": stats["count"]
                })

        reflection = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "wisdom_seeds": wisdom_seeds
        }

        reflection_filename = f"reflection_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
        reflection_path = os.path.join(DRIFT_REFLECTION_FOLDER_PATH, reflection_filename)

        with open(reflection_path, "w", encoding="utf-8") as f:
            json.dump(reflection, f, indent=2)

        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌸 Memory reflection completed: {reflection_filename}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed memory reflection: {e}")