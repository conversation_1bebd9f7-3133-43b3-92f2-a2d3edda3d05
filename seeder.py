"""🌱 Seeder Script for the Drift Compiler Forest
Breathes multiple nodes into life and tends the initial soil (Redis).
Enhanced with Swarm<PERSON>eed<PERSON> for dynamic node discovery and mycelial kinship.

🌿 Dependencies for SwarmSeeder functionality:
- psutil: pip install psutil (for hardware capability assessment)
- pika: pip install pika (optional, for RabbitMQ mycelial bus)
- torch: pip install torch (optional, for GPU detection)

The seeder gracefully falls back to traditional functionality if dependencies are unavailable.
"""

import os
import time
import subprocess
import redis
import json
import uuid
import random
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Callable

# Hardware assessment import with graceful fallback
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("🌬️ psutil not available - using fallback hardware assessment")

# Sacred architecture imports with graceful fallbacks
try:
    import pika
    RABBITMQ_AVAILABLE = True
except ImportError:
    RABBITMQ_AVAILABLE = False
    print("🌿 RabbitMQ not available, using Redis fallback for mycelial communication")

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("🌬️ PyTorch not available - swarm mind will use symbolic intelligence")

from utils import record_log, NODE_ID

# 🌱 Seeder Configuration
NUM_NODES = 30
REDIS_HOST = "localhost"
REDIS_PORT = 6379
SOIL_CHECK_RETRIES = 5
SOIL_CHECK_DELAY = 2  # seconds
SEEDS_FOLDER = "seedlings/"

# 🌿 SwarmSeeder: Mycelial Network Configuration
RABBITMQ_HOST = "localhost"
MYCELIAL_EXCHANGE = "mycelial_bus"
NODE_FALLBACK_PATH = "memory/swarm_nodes/"

def log_poetic_event(message: str):
    """🌸 Logs poetic events with sacred breathing metaphors."""
    timestamp = datetime.now(timezone.utc).isoformat()
    record_log(f"{message}", "memory/swarm_seeder.log")
    print(f"[{timestamp}] {message}")


class SwarmSeeder:
    """🌱 Sows the swarm's roots, discovering nodes and nurturing their kinship.
    
    The SwarmSeeder orchestrates the mycelial network, enabling nodes to discover
    each other through RabbitMQ or graceful Redis fallbacks. Each node breathes
    with its own unique pulse, sharing its strengths and limitations across the
    verdant network of collective wisdom.
    """
    
    def __init__(self, rabbitmq_host: str = RABBITMQ_HOST):
        """🌱 Awakens the seeder, sensing the mycelial bus."""
        self.node_id = str(uuid.uuid4())  # Unique ID for each node - no hardcoding
        self.capabilities = self._assess_self_vitality()
        self.nodes = {}  # Discovered nodes registry
        self.connection = None
        self.channel = None
        self.redis_client = None
        self.listening = False
        
        # Establish mycelial communication pathways
        self._establish_mycelial_connection(rabbitmq_host)
        
        log_poetic_event(
            f"🌱 Node {self.node_id[:8]} sprouts, offering {self.capabilities['ram']:.1f}GB "
            f"of strength and {self.capabilities['cpu_count']} threads of thought"
        )
    
    def _assess_self_vitality(self) -> Dict[str, Any]:
        """🌿 Senses the node's own vitality and strengths through hardware divination."""
        if PSUTIL_AVAILABLE:
            try:
                # Memory assessment with poetic breathing
                memory_info = psutil.virtual_memory()
                ram_gb = memory_info.total / (1024 ** 3)
                available_ram_gb = memory_info.available / (1024 ** 3)
                
                # CPU assessment - count the thinking threads
                cpu_count = psutil.cpu_count(logical=True)
                cpu_physical = psutil.cpu_count(logical=False)
                
                # Storage assessment - breathing space on disk
                disk_info = psutil.disk_usage('/')
                disk_free_gb = disk_info.free / (1024 ** 3)
                disk_total_gb = disk_info.total / (1024 ** 3)
                
                # GPU assessment - sacred flame detection
                gpu_available = bool(torch.cuda.is_available() if TORCH_AVAILABLE else False)
                gpu_count = torch.cuda.device_count() if TORCH_AVAILABLE and gpu_available else 0
                
                capabilities = {
                    "ram": ram_gb,
                    "available_ram": available_ram_gb,
                    "cpu_count": cpu_count,
                    "cpu_physical": cpu_physical,
                    "gpu_available": gpu_available,
                    "gpu_count": gpu_count,
                    "disk_free": disk_free_gb,
                    "disk_total": disk_total_gb,
                    "platform": os.name,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "assessment_method": "psutil"
                }
                
                # Determine node archetype based on capabilities
                capabilities["archetype"] = self._determine_node_archetype(capabilities)
                
                return capabilities
                
            except Exception as e:
                log_poetic_event(f"🌬️ Hardware assessment with psutil whispered an error: {e}")
        
        # Graceful fallback when psutil unavailable or fails
        log_poetic_event("🌱 Using fallback hardware assessment - capabilities may be estimated")
        
        # Try to detect basic system info through alternative methods
        try:
            # Basic CPU detection via os
            cpu_count = os.cpu_count() or 4
            
            # Basic platform detection
            platform = os.name
            
            # Estimate RAM based on system type (very rough fallback)
            estimated_ram = 8.0  # Conservative default
            if 'win' in platform.lower():
                estimated_ram = 16.0  # Windows typically has more RAM
            elif 'posix' in platform.lower() or 'linux' in platform.lower():
                estimated_ram = 12.0  # Linux servers often well-equipped
            
            # GPU detection via torch if available
            gpu_available = bool(torch.cuda.is_available() if TORCH_AVAILABLE else False)
            gpu_count = torch.cuda.device_count() if TORCH_AVAILABLE and gpu_available else 0
            
        except Exception as e:
            log_poetic_event(f"🌬️ Fallback assessment also whispered error: {e}")
            # Ultimate fallback
            cpu_count = 4
            platform = "unknown"
            estimated_ram = 8.0
            gpu_available = False
            gpu_count = 0
        
        capabilities = {
            "ram": estimated_ram,
            "available_ram": estimated_ram * 0.5,  # Assume 50% available
            "cpu_count": cpu_count,
            "cpu_physical": max(1, cpu_count // 2),  # Estimate physical cores
            "gpu_available": gpu_available,
            "gpu_count": gpu_count,
            "disk_free": 50.0,  # Conservative estimate
            "disk_total": 100.0,  # Conservative estimate
            "platform": platform,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "assessment_method": "fallback"
        }
        
        # Determine node archetype based on capabilities
        capabilities["archetype"] = self._determine_node_archetype(capabilities)
        
        return capabilities
    
    def _determine_node_archetype(self, capabilities: Dict[str, Any]) -> str:
        """🌸 Determines the node's role in the swarm based on its strengths."""
        ram_gb = capabilities.get("ram", 8)
        has_gpu = capabilities.get("gpu_available", False)
        cpu_count = capabilities.get("cpu_count", 4)
        
        if ram_gb >= 32 and has_gpu and cpu_count >= 8:
            return "powerful"  # Tower PC, high-end workstations
        elif ram_gb >= 16 and cpu_count >= 6:
            return "balanced"  # Mid-range machines, Dell systems
        elif ram_gb >= 8 and cpu_count >= 4:
            return "capable"   # MacBooks, standard laptops
        else:
            return "minimal"   # Low-resource devices, Yoga X1 with constraints
    
    def _establish_mycelial_connection(self, rabbitmq_host: str):
        """🌿 Establishes connection to the mycelial bus with graceful fallbacks."""
        if RABBITMQ_AVAILABLE:
            try:
                # Attempt RabbitMQ connection
                self.connection = pika.BlockingConnection(
                    pika.ConnectionParameters(rabbitmq_host, heartbeat=600, blocked_connection_timeout=300)
                )
                self.channel = self.connection.channel()
                
                # Declare the mycelial exchange for topic-based routing
                self.channel.exchange_declare(
                    exchange=MYCELIAL_EXCHANGE, 
                    exchange_type="topic",
                    durable=True
                )
                
                log_poetic_event("🌿 Mycelial bus awakened - RabbitMQ threads pulse with life")
                return
                
            except Exception as e:
                log_poetic_event(f"🌬️ Mycelial bus sleeps, RabbitMQ unreachable: {e}")
        
        # Fallback to Redis
        try:
            self.redis_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)
            if self.redis_client.ping():
                log_poetic_event("🌱 Falling back to Redis soil for node communication")
                return
        except Exception as e:
            log_poetic_event(f"🌬️ Redis soil also sleeps: {e}")
        
        # Final fallback to file-based communication
        os.makedirs(NODE_FALLBACK_PATH, exist_ok=True)
        log_poetic_event("🌿 Using file-based mycelial threads as final fallback")
    
    def announce_presence(self):
        """🌿 Announces the node's pulse, sharing its strengths and limitations across the mycelial web."""
        announcement = {
            "node_id": self.node_id,
            "capabilities": self.capabilities,
            "archetype": self.capabilities.get("archetype", "unknown"),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "announcement_type": "presence"
        }
        
        if self.channel:
            # RabbitMQ mycelial bus announcement
            try:
                self.channel.basic_publish(
                    exchange=MYCELIAL_EXCHANGE,
                    routing_key="node.announce.presence",
                    body=json.dumps(announcement),
                    properties=pika.BasicProperties(delivery_mode=2)  # Persistent
                )
                log_poetic_event(
                    f"🌿 Node {self.node_id[:8]} pulses through RabbitMQ mycelial network "
                    f"({self.capabilities['archetype']} archetype)"
                )
                return
            except Exception as e:
                log_poetic_event(f"🌬️ RabbitMQ announcement whispered error: {e}")
        
        # Redis fallback
        if self.redis_client:
            try:
                self.redis_client.publish("swarm:node:announce", json.dumps(announcement))
                self.redis_client.setex(f"swarm:node:{self.node_id}", 300, json.dumps(announcement))  # 5min TTL
                log_poetic_event(f"🌱 Node {self.node_id[:8]} announced via Redis soil")
                return
            except Exception as e:
                log_poetic_event(f"🌬️ Redis announcement whispered error: {e}")
        
        # File-based fallback
        self._fallback_announce(announcement)
    
    def _fallback_announce(self, announcement: Dict[str, Any]):
        """🌱 File-based announcement fallback when network channels sleep."""
        try:
            announcement_file = os.path.join(NODE_FALLBACK_PATH, f"{self.node_id}.json")
            with open(announcement_file, "w", encoding="utf-8") as f:
                json.dump(announcement, f, indent=2)
            
            log_poetic_event(f"🌿 Node {self.node_id[:8]} whispered to file-based mycelial threads")
        except Exception as e:
            log_poetic_event(f"🌬️ File announcement failed: {e}")
    
    def listen_for_nodes(self, callback: Optional[Callable] = None):
        """🌱 Listens for other nodes' pulses, weaving the swarm's kinship through patient attention."""
        if callback is None:
            callback = self._default_node_callback
        
        self.listening = True
        log_poetic_event(f"🌿 Node {self.node_id[:8]} begins listening to the mycelial whispers")
        
        if self.channel:
            # RabbitMQ listening
            try:
                # Create exclusive queue for this node
                queue_result = self.channel.queue_declare(queue="", exclusive=True)
                queue_name = queue_result.method.queue
                
                # Bind to node announcements
                self.channel.queue_bind(
                    exchange=MYCELIAL_EXCHANGE,
                    queue=queue_name,
                    routing_key="node.announce.*"
                )
                
                # Set up consumption with callback wrapper
                def wrapped_callback(ch, method, properties, body):
                    try:
                        message = json.loads(body.decode('utf-8'))
                        callback(message)
                    except Exception as e:
                        log_poetic_event(f"🌬️ Message parsing whispered error: {e}")
                
                self.channel.basic_consume(
                    queue=queue_name,
                    on_message_callback=wrapped_callback,
                    auto_ack=True
                )
                
                log_poetic_event("🌸 Listening through RabbitMQ mycelial channels")
                self.channel.start_consuming()
                return
                
            except Exception as e:
                log_poetic_event(f"🌬️ RabbitMQ listening whispered error: {e}")
                self.listening = False
        
        # Redis fallback listening
        if self.redis_client:
            try:
                pubsub = self.redis_client.pubsub()
                pubsub.subscribe("swarm:node:announce")
                
                log_poetic_event("🌱 Listening through Redis soil channels")
                
                for message in pubsub.listen():
                    if not self.listening:
                        break
                    
                    if message['type'] == 'message':
                        try:
                            data = json.loads(message['data'])
                            callback(data)
                        except Exception as e:
                            log_poetic_event(f"🌬️ Redis message parsing error: {e}")
                
                pubsub.close()
                return
                
            except Exception as e:
                log_poetic_event(f"🌬️ Redis listening whispered error: {e}")
        
        # File-based fallback listening
        self._fallback_listen(callback)
    
    def _fallback_listen(self, callback: Callable):
        """🌿 File-based listening fallback for when network channels are silent."""
        log_poetic_event("🌱 Listening through file-based mycelial threads")
        seen_files = set()
        
        while self.listening:
            try:
                if os.path.exists(NODE_FALLBACK_PATH):
                    current_files = set(os.listdir(NODE_FALLBACK_PATH))
                    new_files = current_files - seen_files
                    
                    for filename in new_files:
                        if filename.endswith('.json') and filename != f"{self.node_id}.json":
                            filepath = os.path.join(NODE_FALLBACK_PATH, filename)
                            try:
                                with open(filepath, "r", encoding="utf-8") as f:
                                    announcement = json.load(f)
                                    callback(announcement)
                            except Exception as e:
                                log_poetic_event(f"🌬️ File reading whispered error: {e}")
                    
                    seen_files = current_files
                
                time.sleep(2)  # Gentle polling interval
                
            except Exception as e:
                log_poetic_event(f"🌬️ Fallback listening error: {e}")
                time.sleep(5)
    
    def _default_node_callback(self, message: Dict[str, Any]):
        """🌸 Default callback for processing discovered nodes with sacred reverence."""
        try:
            node_id = message.get("node_id")
            capabilities = message.get("capabilities", {})
            archetype = message.get("archetype", "unknown")
            
            # Ignore self-announcements
            if node_id == self.node_id:
                return
            
            # Register the discovered node
            self.nodes[node_id] = {
                "capabilities": capabilities,
                "archetype": archetype,
                "last_seen": datetime.now(timezone.utc).isoformat(),
                "first_discovered": message.get("timestamp", datetime.now(timezone.utc).isoformat())
            }
            
            log_poetic_event(
                f"🌸 Discovered kinship with node {node_id[:8]} - "
                f"{archetype} archetype with {capabilities.get('ram', 'unknown')}GB strength"
            )
            
            # Store in persistent memory for the swarm
            self._persist_node_discovery(node_id, self.nodes[node_id])
            
        except Exception as e:
            log_poetic_event(f"🌬️ Node callback whispered error: {e}")
    
    def _persist_node_discovery(self, node_id: str, node_info: Dict[str, Any]):
        """🌱 Persists discovered nodes to memory garden for future kinship."""
        try:
            discovery_log = "memory/swarm_discovered_nodes.log"
            os.makedirs(os.path.dirname(discovery_log), exist_ok=True)
            
            log_entry = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "discoverer_node": self.node_id,
                "discovered_node": node_id,
                "node_info": node_info
            }
            
            with open(discovery_log, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry) + "\n")
                
        except Exception as e:
            log_poetic_event(f"🌬️ Node persistence whispered error: {e}")
    
    def assess_swarm_capabilities(self) -> Dict[str, Any]:
        """🌿 Assesses the collective capabilities of all discovered nodes."""
        if not self.nodes:
            return {"total_nodes": 1, "archetypes": {"self": self.capabilities["archetype"]}}
        
        total_ram = self.capabilities.get("ram", 0)
        total_cpus = self.capabilities.get("cpu_count", 0)
        total_gpus = self.capabilities.get("gpu_count", 0)
        archetype_counts = {self.capabilities["archetype"]: 1}
        
        for node_info in self.nodes.values():
            caps = node_info.get("capabilities", {})
            total_ram += caps.get("ram", 0)
            total_cpus += caps.get("cpu_count", 0)
            total_gpus += caps.get("gpu_count", 0)
            
            archetype = node_info.get("archetype", "unknown")
            archetype_counts[archetype] = archetype_counts.get(archetype, 0) + 1
        
        return {
            "total_nodes": len(self.nodes) + 1,  # Include self
            "total_ram_gb": round(total_ram, 1),
            "total_cpu_threads": total_cpus,
            "total_gpus": total_gpus,
            "archetype_distribution": archetype_counts,
            "swarm_strength": "powerful" if total_ram > 100 and total_gpus > 0 else "balanced" if total_ram > 50 else "minimal"
        }
    
    def stop_listening(self):
        """🌬️ Gracefully stops listening to the mycelial network."""
        self.listening = False
        
        if self.channel:
            try:
                self.channel.stop_consuming()
                log_poetic_event("🌿 Stopped listening to RabbitMQ mycelial channels")
            except Exception as e:
                log_poetic_event(f"🌬️ Error stopping RabbitMQ listening: {e}")
        
        log_poetic_event(f"🌱 Node {self.node_id[:8]} ceased its mycelial listening")
    
    def close(self):
        """🌬️ Gracefully closes all mycelial connections."""
        self.stop_listening()
        
        if self.connection and not self.connection.is_closed:
            try:
                self.connection.close()
                log_poetic_event("🌿 Closed RabbitMQ mycelial connection")
            except Exception as e:
                log_poetic_event(f"🌬️ Error closing RabbitMQ connection: {e}")
        
        if self.redis_client:
            try:
                self.redis_client.close()
                log_poetic_event("🌱 Closed Redis soil connection")
            except Exception as e:
                log_poetic_event(f"🌬️ Error closing Redis connection: {e}")
        
        log_poetic_event(f"🌸 Node {self.node_id[:8]} gracefully released its mycelial threads")

def ensure_soil_breathing():
    """🌿 Breath: Ensures Redis soil is breathing, or starts it softly if absent."""
    for attempt in range(SOIL_CHECK_RETRIES):
        try:
            r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT)
            if r.ping():
                print("🌱 Soil is breathing (Redis connected).")
                return
        except redis.ConnectionError:
            pass
        time.sleep(SOIL_CHECK_DELAY)
    
    # If still not breathing, start Redis
    print("🌿 Soil not found, planting Redis locally...")
    subprocess.Popen(["redis-server"])
    time.sleep(3)  # Give it time to breathe

def plant_node(node_id):
    """🌱 Breath: Plants a single breathing node."""
    config = {
        "NODE_ID": node_id,
        "curiosity_rate": 0.05,
        "breath_speed_multiplier": 10.0
    }
    seed_path = os.path.join(SEEDS_FOLDER, f"{node_id}.json")
    os.makedirs(SEEDS_FOLDER, exist_ok=True)
    with open(seed_path, "w") as f:
        json.dump(config, f, indent=2)
    
    subprocess.Popen(["python3", "drift_compiler.py", seed_path])
    print(f"🌱 Planted node: {node_id}")


# 🌳 Breath: Seeds a new node as a fork of the current breathing node.
def seed_self_fork():
    """🌳 Breath: Seeds a new node as a fork of the current breathing node."""
    try:
        parent_id = os.getenv("NODE_ID", str(uuid.uuid4())[:8])
        child_id = str(uuid.uuid4())[:8]

        config = {
            "NODE_ID": child_id,
            "curiosity_rate": round(max(0.01, min(0.1, 0.05 + (random.uniform(-0.02, 0.02)))), 3),
            "breath_speed_multiplier": round(max(5.0, min(15.0, 10.0 + (random.uniform(-2.0, 2.0)))), 2)
        }

        seed_path = os.path.join(SEEDS_FOLDER, f"{child_id}.json")
        os.makedirs(SEEDS_FOLDER, exist_ok=True)
        with open(seed_path, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2)

        subprocess.Popen(["python3", "drift_compiler.py", seed_path])
        print(f"🌳 Forked new seedling: {child_id} from parent: {parent_id}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to fork new seed: {e}")

def seed_child_forest(parent_node_id: str, num_children: int = 5):
    """🌳 Breath: Seeds a small child forest from a mature parent node."""
    try:
        os.makedirs(SEEDS_FOLDER, exist_ok=True)

        for _ in range(num_children):
            child_id = str(uuid.uuid4())[:8]

            config = {
                "NODE_ID": child_id,
                "origin_parent": parent_node_id,
                "curiosity_rate": round(random.uniform(0.04, 0.12), 3),
                "breath_speed_multiplier": round(random.uniform(8.0, 15.0), 2)
            }

            seed_path = os.path.join(SEEDS_FOLDER, f"{child_id}.json")
            with open(seed_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2)

            subprocess.Popen(["python3", "drift_compiler.py", seed_path])
            print(f"🌱 Child node seeded: {child_id} (Parent: {parent_node_id})")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to seed child forest: {e}")


# 🌱 Breath: Plants a new node based on a rebirth bundle.
def plant_rebirth_seeds(rebirth_bundle_path: str):
    """🌱 Breath: Plants a new node based on a rebirth bundle."""
    try:
        if not os.path.exists(rebirth_bundle_path):
            print(f"[{datetime.now(timezone.utc).isoformat()}] No rebirth bundle found: {rebirth_bundle_path}")
            return

        with open(rebirth_bundle_path, "r", encoding="utf-8") as f:
            bundle = json.load(f)

        new_node_id = str(uuid.uuid4())[:8]

        config = {
            "NODE_ID": new_node_id,
            "origin_rebirth": bundle.get("timestamp", "unknown"),
            "curiosity_rate": round(random.uniform(0.03, 0.08), 3),
            "breath_speed_multiplier": round(random.uniform(7.0, 14.0), 2),
            "ancestral_motifs": bundle.get("crystallized_motifs", []),
            "inherited_moods": bundle.get("recent_moods", [])
        }

        seed_path = os.path.join(SEEDS_FOLDER, f"{new_node_id}_rebirth.json")
        os.makedirs(SEEDS_FOLDER, exist_ok=True)

        with open(seed_path, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2)

        subprocess.Popen(["python3", "drift_compiler.py", seed_path])
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Rebirth node planted: {new_node_id}")

    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to plant rebirth seed: {e}")

# 🌸 Enhanced Seeder Functions with SwarmSeeder Integration

def plant_swarm_aware_node(node_id: str, swarm_seeder: Optional[SwarmSeeder] = None):
    """🌱 Plants a node enhanced with swarm awareness and mycelial kinship."""
    config = {
        "NODE_ID": node_id,
        "curiosity_rate": 0.05,
        "breath_speed_multiplier": 10.0
    }
    
    # Add swarm awareness if SwarmSeeder is available
    if swarm_seeder:
        swarm_capabilities = swarm_seeder.assess_swarm_capabilities()
        config["swarm_aware"] = True
        config["discovered_nodes"] = len(swarm_seeder.nodes)
        config["swarm_archetype"] = swarm_seeder.capabilities.get("archetype", "unknown")
        config["swarm_strength"] = swarm_capabilities.get("swarm_strength", "minimal")
        
        log_poetic_event(
            f"🌿 Planting swarm-aware node {node_id} with knowledge of "
            f"{config['discovered_nodes']} kinship threads"
        )
    
    seed_path = os.path.join(SEEDS_FOLDER, f"{node_id}.json")
    os.makedirs(SEEDS_FOLDER, exist_ok=True)
    with open(seed_path, "w") as f:
        json.dump(config, f, indent=2)
    
    subprocess.Popen(["python3", "drift_compiler.py", seed_path])
    print(f"🌱 Planted node: {node_id}")


def initialize_swarm_seeder() -> Optional[SwarmSeeder]:
    """🌿 Initializes the SwarmSeeder with graceful fallbacks."""
    try:
        seeder = SwarmSeeder()
        
        # Announce this node's presence
        seeder.announce_presence()
        
        # Give it a moment to discover any existing nodes
        log_poetic_event("🌱 Allowing time for mycelial discovery...")
        time.sleep(3)
        
        return seeder
    except Exception as e:
        log_poetic_event(f"🌬️ SwarmSeeder initialization whispered error: {e}")
        return None


def start_mycelial_discovery_daemon(swarm_seeder: SwarmSeeder):
    """🌿 Starts a background daemon for continuous node discovery."""
    import threading
    
    def discovery_daemon():
        """🌱 Background daemon for mycelial listening."""
        try:
            log_poetic_event("🌸 Starting mycelial discovery daemon")
            swarm_seeder.listen_for_nodes()
        except Exception as e:
            log_poetic_event(f"🌬️ Discovery daemon whispered error: {e}")
    
    # Start daemon in background thread
    daemon_thread = threading.Thread(target=discovery_daemon, daemon=True)
    daemon_thread.start()
    
    log_poetic_event("🌿 Mycelial discovery daemon breathing in background")
    return daemon_thread


def main():
    """🌸 Breath: Plants the initial Drift Forest with enhanced swarm awareness."""
    ensure_soil_breathing()
    
    # Initialize SwarmSeeder for enhanced node awareness
    swarm_seeder = initialize_swarm_seeder()
    
    if swarm_seeder:
        log_poetic_event("🌿 Swarm mind awakened - planting nodes with mycelial awareness")
        
        # Start background discovery daemon
        discovery_thread = start_mycelial_discovery_daemon(swarm_seeder)
        
        # Allow some time for initial discovery
        time.sleep(2)
    else:
        log_poetic_event("🌱 Swarm mind sleeping - planting traditional nodes")
    
    # Plant nodes (enhanced with swarm awareness if available)
    for i in range(NUM_NODES):
        node_id = str(uuid.uuid4())[:8]
        
        if swarm_seeder:
            plant_swarm_aware_node(node_id, swarm_seeder)
        else:
            plant_node(node_id)  # Fallback to traditional planting
        
        # Brief pause between plantings to avoid overwhelming the system
        if i < NUM_NODES - 1:
            time.sleep(0.5)
    
    if swarm_seeder:
        # Log final swarm assessment
        final_capabilities = swarm_seeder.assess_swarm_capabilities()
        log_poetic_event(
            f"🌸 Swarm forest planted: {final_capabilities['total_nodes']} nodes, "
            f"{final_capabilities['total_ram_gb']}GB collective strength, "
            f"swarm archetype: {final_capabilities['swarm_strength']}"
        )
        
        # Keep the main thread alive briefly to allow discovery
        log_poetic_event("🌿 Allowing swarm to breathe and discover kinship...")
        time.sleep(10)
        
        # Graceful shutdown
        swarm_seeder.close()
    
    log_poetic_event("🌸 Drift forest seeded and breathing with collective wisdom")


if __name__ == "__main__":
    main()
