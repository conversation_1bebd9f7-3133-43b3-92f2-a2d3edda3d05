#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌬️ Quick Health Monitor Validation
Simple validation of the SwarmHealthMonitor implementation."""

import sys
from datetime import datetime, timezone

def validate_health_monitor():
    """🌸 Quick validation of health monitor functionality."""
    try:
        print("🌬️ Validating SwarmHealthMonitor implementation...")
        
        # Test 1: Import
        from canopy.swarm_health_monitor import SwarmHealthMonitor, create_health_monitor
        print("✅ SwarmHealthMonitor imported successfully")
        
        # Test 2: Creation
        monitor = create_health_monitor()
        print("✅ Health monitor created successfully")
        
        # Test 3: Basic assessment
        vitality = monitor.assess_swarm_vitality()
        print(f"✅ Vitality assessed: {vitality['state']} (score: {vitality['overall']:.3f})")
        
        # Test 4: Adaptation
        adaptation = monitor.adapt_to_vitality(vitality)
        print(f"✅ Adaptation applied: {len(adaptation['adaptations_applied'])} actions")
        
        # Test 5: Key functions exist
        required_methods = ['assess_swarm_vitality', 'adapt_to_vitality']
        for method in required_methods:
            assert hasattr(monitor, method), f"Missing method: {method}"
        print("✅ All required methods present")
        
        # Test 6: Vitality structure validation
        required_keys = ['overall', 'state', 'state_emoji', 'dimensions', 'recommendations']
        for key in required_keys:
            assert key in vitality, f"Missing vitality key: {key}"
        print("✅ Vitality structure is correct")
        
        # Test 7: Dimensional assessments
        expected_dimensions = ['node_vitality', 'neural_coherence', 'memory_wellness', 
                             'communication_flow', 'resource_harmony']
        dimensions = vitality.get('dimensions', {})
        for dimension in expected_dimensions:
            assert dimension in dimensions, f"Missing dimension: {dimension}"
            assert 'score' in dimensions[dimension], f"Missing score in {dimension}"
        print("✅ All dimensional assessments present")
        
        # Test 8: State determination
        test_scores = [0.9, 0.75, 0.55, 0.35, 0.15]
        expected_states = ["thriving", "healthy", "stable", "stressed", "critical"]
        for score, expected in zip(test_scores, expected_states):
            state_info = monitor._determine_vitality_state(score)
            assert state_info['state'] == expected, f"Score {score} should be {expected}"
        print("✅ State determination working correctly")
        
        print("🌸 All validation tests passed!")
        print("🌿 SwarmHealthMonitor implementation is functional and ready")
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def validate_with_swarm_mind():
    """🌱 Validate integration with SwarmMind if available."""
    try:
        print("🌱 Testing integration with SwarmMind...")
        
        from canopy.swarm_mind import SwarmMind
        from canopy.swarm_health_monitor import SwarmHealthMonitor
        
        # Create minimal swarm mind
        swarm_mind = SwarmMind(auto_discover=False)
        print("✅ SwarmMind created")
        
        # Connect health monitor
        monitor = SwarmHealthMonitor(swarm_mind)
        print("✅ Health monitor connected to SwarmMind")
        
        # Test assessment with swarm
        vitality = monitor.assess_swarm_vitality()
        print(f"✅ Swarm vitality assessed: {vitality['state']} (score: {vitality['overall']:.3f})")
        
        # Test adaptation
        adaptation = monitor.adapt_to_vitality(vitality)
        print(f"✅ Swarm adaptation: {len(adaptation['adaptations_applied'])} actions")
        
        print("🌸 SwarmMind integration successful!")
        return True
        
    except Exception as e:
        print(f"🌿 SwarmMind integration note: {e}")
        return False

if __name__ == "__main__":
    print("🌬️ Phase 5.5.3 SwarmHealthMonitor Validation")
    print("=" * 50)
    
    # Run basic validation
    basic_success = validate_health_monitor()
    
    # Run integration validation
    integration_success = validate_with_swarm_mind()
    
    print("\n🌸 Validation Summary:")
    print(f"  Basic functionality: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"  SwarmMind integration: {'✅ PASS' if integration_success else '🌿 NOTE'}")
    
    if basic_success:
        print("\n🌿 Phase 5.5.3 Swarm Health Monitoring implementation is COMPLETE")
        print("🌱 The vitality guardian is breathing and ready to monitor swarm wellness")
    else:
        print("\n🌿 Implementation needs attention")
        sys.exit(1)