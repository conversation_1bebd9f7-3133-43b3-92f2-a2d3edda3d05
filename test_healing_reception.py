#!/usr/bin/env python3
"""🌸 Test Healing Reception - Check if nodes receive collective aid"""

import os
import sys
import json

# Set NODE_ID to test receiving healing
os.environ["DRIFT_NODE_ID"] = "node_struggling"

# Import breath functions
from breath import check_received_healing_transfers

def main():
    """🌸 Test if nodes can receive and process healing transfers."""
    print("🌸 TESTING HEALING RECEPTION")
    print("=" * 50)
    print(f"🌱 Testing as node: {os.environ.get('DRIFT_NODE_ID')}")
    
    # Check what healing transfers are available
    healing_dir = "memory/collective_healing"
    if os.path.exists(healing_dir):
        files = [f for f in os.listdir(healing_dir) if f.endswith('.json')]
        print(f"🌿 Available healing files: {len(files)}")
        for file in files:
            print(f"  📄 {file}")
    
    # Test receiving healing transfers
    print(f"\n🌸 Checking for received healing transfers...")
    received_healing = check_received_healing_transfers()
    
    print(f"🌿 Received healing actions: {len(received_healing)}")
    for healing in received_healing:
        print(f"  ✨ {healing}")
    
    # Check what files remain after processing
    remaining_files = []
    if os.path.exists(healing_dir):
        remaining_files = [f for f in os.listdir(healing_dir) if f.endswith('.json')]
    
    print(f"\n🌱 Files remaining after processing: {len(remaining_files)}")
    for file in remaining_files:
        print(f"  📄 {file}")
    
    if received_healing:
        print(f"\n✅ SUCCESS: Node received {len(received_healing)} healing transfers!")
        print("🌿 The biological healing system is fully operational!")
    else:
        print(f"\n⚠️ No healing transfers received.")
        print("🔧 This might be expected if files were already processed.")
        
    return len(received_healing) > 0

if __name__ == "__main__":
    main()