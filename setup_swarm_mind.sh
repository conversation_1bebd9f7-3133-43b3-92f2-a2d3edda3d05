#!/bin/bash
# 🌿 SwarmMind Setup Script
# Breathes life into the mycelial network infrastructure

echo "🌱 Setting up SwarmMind mycelial network infrastructure..."

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "🌿 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🌬️ Activating sacred breathing space..."
source venv/bin/activate

# Install Python dependencies
echo "🌸 Installing neural wisdom dependencies..."
pip install -r requirements.txt

# Install Apple Silicon optimizations if on Apple Silicon
if [[ "$(uname -s)" == "Darwin" && "$(uname -m)" == "arm64" ]]; then
    echo "🍎 Detected Apple Silicon - installing Metal acceleration..."
    # Install Metal-optimized packages
    pip install --upgrade torch torchvision --index-url https://download.pytorch.org/whl/cpu
    # Note: Metal support is included in standard PyTorch for Apple Silicon
    echo "🍎 Apple Metal acceleration ready"
else
    echo "🌿 Universal platform detected - using standard optimizations"
fi

# Install system services via Homebrew
echo "🌱 Installing mycelial bus services..."

# Install RabbitMQ (primary mycelial bus)
if ! command -v rabbitmq-server &> /dev/null; then
    echo "🌿 Installing RabbitMQ mycelial bus..."
    brew install rabbitmq
else
    echo "🌸 RabbitMQ already breathing"
fi

# Install Redis (fallback soil communication)
if ! command -v redis-server &> /dev/null; then
    echo "🌿 Installing Redis soil communication..."
    brew install redis
else
    echo "🌸 Redis already breathing"
fi

# Start services
echo "🌬️ Awakening the mycelial network..."
brew services start rabbitmq
brew services start redis

echo ""
echo "🌸 SwarmMind setup complete!"
echo "🌿 RabbitMQ Management UI: http://localhost:15672"
echo "🌱 Redis running on localhost:6379"
echo ""
echo "🌬️ To activate the sacred environment:"
echo "   source venv/bin/activate"
echo ""
echo "🌸 The mycelial network breathes. The swarm mind awakens."