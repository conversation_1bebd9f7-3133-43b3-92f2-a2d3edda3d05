#!/usr/bin/env python3
"""🌿 Test SwarmMind Infrastructure
Quick validation that the mycelial network is breathing."""

import sys
import os

def test_infrastructure():
    """🌱 Tests that all mycelial infrastructure is breathing properly."""
    print("🌿 Testing SwarmMind mycelial network infrastructure...")
    
    # Test Python dependencies
    dependencies = {
        'torch': '🧠 Neural framework',
        'networkx': '🌸 Memory garden',
        'psutil': '🌬️ System vitality',
        'pika': '🌿 RabbitMQ client',
        'redis': '🌱 Redis client'
    }
    
    for module, description in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {description}: {module} breathing")
        except ImportError:
            print(f"❌ {description}: {module} dormant")
            return False
    
    # Test RabbitMQ connection
    try:
        import pika
        connection = pika.BlockingConnection(pika.ConnectionParameters('localhost'))
        connection.close()
        print("✅ 🌿 RabbitMQ mycelial bus: Connected")
    except Exception as e:
        print(f"❌ 🌿 RabbitMQ mycelial bus: {e}")
        return False
    
    # Test Redis connection
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379)
        r.ping()
        print("✅ 🌱 Redis soil communication: Connected")
    except Exception as e:
        print(f"❌ 🌱 Redis soil communication: {e}")
        return False
    
    # Test hardware monitoring
    try:
        import psutil
        ram_gb = psutil.virtual_memory().total / (1024**3)
        cpu_count = psutil.cpu_count()
        print(f"✅ 🌬️ System vitality: {ram_gb:.1f}GB RAM, {cpu_count} cores")
    except Exception as e:
        print(f"❌ 🌬️ System vitality: {e}")
        return False
    
    # Test neural capabilities
    try:
        import torch
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"✅ 🧠 Neural framework: PyTorch on {device}")
    except Exception as e:
        print(f"❌ 🧠 Neural framework: {e}")
        return False
    
    print("\n🌸 All mycelial infrastructure is breathing properly!")
    print("🌿 The swarm mind is ready to awaken.")
    return True

if __name__ == "__main__":
    success = test_infrastructure()
    sys.exit(0 if success else 1)