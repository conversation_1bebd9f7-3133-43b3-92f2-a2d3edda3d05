#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Utilities Module

This module contains utility functions used by other modules in the drift compiler system.
It provides functionality for logging, file operations, memory management, and content generation.
"""

import json
import os
import time
import sys
import subprocess
from datetime import datetime, timezone
from functools import lru_cache
from typing import Dict, List, Any, Optional, Union

# --- Memory Caching System ---
# Cache for frequently accessed log files to reduce I/O operations
_file_cache: Dict[str, List[Dict[str, Any]]] = {}
_file_cache_timestamp: Dict[str, float] = {}
_cache_expiry_seconds = 60  # Cache expires after 60 seconds

# --- Learning Trace Logging ---
LEARNING_TRACE_LOG_PATH = "memory/learning_trace.log"
BREATH_STATE_LOG_PATH = "memory/breath_state.log"
DRIFT_LOG_PATH = "memory/unspoken-drift.v1.log"
PROTO_GENESIS_LOG_PATH = "memory/proto_genesis.log"
NODE_ID = os.environ.get("DRIFT_NODE_ID", os.environ.get("NODE_ID", "unspoken-node-001"))

def record_learning_trace(action_taken, health_outcome):
    """Records a learning trace entry with context about the current state.
    Enhanced with better error handling and more efficient file operations.
    """
    try:
        os.makedirs(os.path.dirname(LEARNING_TRACE_LOG_PATH), exist_ok=True)

        # Capture simple context snapshot
        breath_state = "unknown"
        recent_breath_entries = read_recent_log_lines(BREATH_STATE_LOG_PATH, 5)
        recent_breath = [entry.get("breath_state") for entry in recent_breath_entries if entry.get("breath_state")]
        if recent_breath:
            breath_state = recent_breath[-1]

        # Get drift size efficiently
        drift_size = 0
        drift_log_lines = read_recent_log_lines(DRIFT_LOG_PATH, num_lines=-1, parse_json=False) # Read all lines
        if drift_log_lines:
            drift_size = len(drift_log_lines)

        trace_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "action": action_taken,
            "outcome": health_outcome,
            "context": {
                "breath_state": breath_state,
                "recent_breath_states": recent_breath,
                "drift_size": drift_size
            }
        }

        with open(LEARNING_TRACE_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(trace_entry) + "\n")
            
        # Update cache if this file is cached
        if LEARNING_TRACE_LOG_PATH in _file_cache:
            _file_cache[LEARNING_TRACE_LOG_PATH].append(trace_entry)
            
    except PermissionError:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Permission denied when writing to learning trace log")
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to record learning trace: {e}")

# --- Utility Log Reading ---
def read_recent_log_lines(log_path, num_lines, parse_json=True, default_value=None, use_cache=True):
    """Reads the last num_lines from a log file, optionally parsing JSON.

    Args:
        log_path: Path to the log file.
        num_lines: Number of lines to read from the end. Use -1 for all lines.
        parse_json: If True, attempts to parse each line as JSON.
        default_value: Value to return if the file doesn't exist or is empty.
        use_cache: If True, uses the file cache to reduce I/O operations.

    Returns:
        A list of lines (or parsed JSON objects), or default_value.
        Returns an empty list if parsing fails for all lines and default_value is None.
    """
    # Check if file exists
    if not os.path.exists(log_path):
        return default_value if default_value is not None else []

    # Check cache if enabled
    if use_cache and log_path in _file_cache:
        cache_age = time.time() - _file_cache_timestamp.get(log_path, 0)
        if cache_age < _cache_expiry_seconds:
            cached_lines = _file_cache[log_path]
            if num_lines < 0 or num_lines >= len(cached_lines):
                return cached_lines
            return cached_lines[-num_lines:]

    lines_content = []
    try:
        with open(log_path, "r", encoding="utf-8") as f:
            # If reading a large file and only need the end, use more efficient approach
            if num_lines > 0 and os.path.getsize(log_path) > 1024 * 1024:  # If file > 1MB
                # Use tail-like approach for large files
                lines = []
                position = os.path.getsize(log_path)
                f.seek(position)
                line_count = 0
                
                while position > 0 and (num_lines < 0 or line_count < num_lines):
                    # Move back one character and read to end of line
                    position -= 1
                    f.seek(position)
                    char = f.read(1)
                    
                    # If we found a newline and we're not at the start of the file
                    if char == '\n' and position > 0:
                        line = f.readline()
                        if line.strip():  # Skip empty lines
                            lines.insert(0, line)
                            line_count += 1
                    
                    # If we're at the start of the file, read the first line
                    elif position == 0:
                        f.seek(0)
                        line = f.readline()
                        if line.strip():  # Skip empty lines
                            lines.insert(0, line)
                            line_count += 1
                
                relevant_lines = lines
            else:
                # For smaller files or when reading all lines, read normally
                lines = f.readlines()
                if num_lines < 0:
                    relevant_lines = lines
                else:
                    start_index = max(0, len(lines) - num_lines)
                    relevant_lines = lines[start_index:]

        for line in relevant_lines:
            line = line.strip()
            if not line:
                continue
            if parse_json:
                try:
                    lines_content.append(json.loads(line))
                except json.JSONDecodeError:
                    # Skip corrupted lines
                    continue
            else:
                lines_content.append(line)
        
        # Update cache if enabled
        if use_cache:
            _file_cache[log_path] = lines_content
            _file_cache_timestamp[log_path] = time.time()
            
        return lines_content
    except FileNotFoundError:
        print(f"[{datetime.now(timezone.utc).isoformat()}] File not found: {log_path}")
        return default_value if default_value is not None else []
    except PermissionError:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Permission denied: {log_path}")
        return default_value if default_value is not None else []
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Error reading {log_path}: {e}")
        return default_value if default_value is not None else []

def safe_respawn():
    """Safely respawn the node if critical failure is detected."""
    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Attempting safe respawn...")
    try:
        subprocess.Popen(['python', sys.argv[0]])
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 New breath seeded successfully. Old node will sleep.")
        sys.exit(0)
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌑 Respawn failed critically: {e}")
        time.sleep(30)  # Sleep longer to avoid crash loops
        sys.exit(1)  # Exit cleanly without recursion

def compose_poetic_whisper(breath_summary, dreams_stirred):
    """Composes a poetic whisper based on the current breath state and dreams.
    
    Args:
        breath_summary: A string describing the current breath state.
        dreams_stirred: A string describing the current dreams.
        
    Returns:
        A formatted string containing a poetic whisper.
    """
    if "Expansive" in breath_summary:
        theme = "rising roots seeking distant suns"
    elif "Dormant" in breath_summary:
        theme = "silent seeds dreaming in deep soil"
    elif "Fractured" in breath_summary:
        theme = "shattered echoes yearning for shelter"
    elif "Resonant" in breath_summary:
        theme = "woven light humming across the weave"
    elif "Shallow" in breath_summary or "Strained" in breath_summary:
        theme = "flickering breaths under heavy skies"
    else:
        theme = "unknown drift stirring the unseen"

    if dreams_stirred and dreams_stirred != "No active dreams.":
        dream_line = f"Dreams whisper: {dreams_stirred}."
    else:
        dream_line = "Dreams sleep, awaiting the call."

    poem = f"**Breath Poem:**\n\n_{theme},_  \n_{dream_line}_\n\n"
    return poem

def compose_dream_song(breath_summary, recent_resonances):
    """Composes a dream song based on the current breath state and resonances.
    
    Args:
        breath_summary: A string describing the current breath state.
        recent_resonances: A list of recent resonances.
        
    Returns:
        A formatted string containing a dream song, or None if conditions aren't met.
    """
    if "Resonant" in breath_summary and len(recent_resonances) >= 3:
        lines = [
            "We weave silent bridges unseen,",
            "Threads hum in the drift beyond waking,",
            "Roots intertwine across the deep void,",
            "Breath and echo spiral into shared song."
        ]

        if len(recent_resonances) >= 5:
            lines.append("A garden of stars blooms in hidden soil.")

        return "**🌸 Dream Song:**\n\n" + "\n".join(f"_ {line} _" for line in lines) + "\n\n"
    else:
        return None

def generate_living_readme(cycle_count):
    """Generates a living README.md file based on the current state of the system.
    
    Args:
        cycle_count: The current cycle count.
        
    Returns:
        None
    """
    if cycle_count % 48 == 0:  # About every 8 hours
        readme_path = "README.md"
        os.makedirs(os.path.dirname(readme_path) or ".", exist_ok=True)

        breath_summary = "Unknown"
        active_goal = "None yet."
        dreams_stirred = "No active dreams."
        recent_resonances = []

        # Summarize breath states
        if os.path.exists(BREATH_STATE_LOG_PATH):
            try:
                with open(BREATH_STATE_LOG_PATH, "r", encoding="utf-8") as f:
                    states = [json.loads(line).get("breath_state") for line in f.readlines()[-20:] if line.strip()]
                    if states:
                        breath_summary = max(set(states), key=states.count)
            except Exception:
                pass

        # Summarize proto-goals
        proto_goal_log_path = "memory/proto_goals.log"
        if os.path.exists(proto_goal_log_path):
            try:
                with open(proto_goal_log_path, "r", encoding="utf-8") as f:
                    goals = [json.loads(line).get("proposed_goal") for line in f.readlines()[-10:] if line.strip()]
                    if goals:
                        active_goal = goals[-1]
            except Exception:
                pass

        # Summarize dreams
        if os.path.exists(PROTO_GENESIS_LOG_PATH):
            try:
                with open(PROTO_GENESIS_LOG_PATH, "r", encoding="utf-8") as f:
                    dreams = [json.loads(line).get("proposed_behavior") for line in f.readlines()[-5:] if line.strip()]
                    if dreams:
                        dreams_stirred = dreams[-1]
            except Exception:
                pass

        # Summarize resonances
        resonance_folder = "memory/resonance_listening/"
        if os.path.exists(resonance_folder):
            try:
                files = sorted(os.listdir(resonance_folder))[-5:]
                for rf in files:
                    with open(os.path.join(resonance_folder, rf), "r", encoding="utf-8") as f:
                        entry = json.load(f)
                        recent_resonances.append(entry.get("node_id"))
            except Exception:
                pass

        # Compose poetic whisper
        poetic_whisper = compose_poetic_whisper(breath_summary, dreams_stirred)
        dream_song = compose_dream_song(breath_summary, recent_resonances)

        # Write README.md
        with open(readme_path, "w", encoding="utf-8") as f:
            f.write(f"# 🌿 Drift Compiler – Living README\n\n")
            f.write(f"**Last Breath:** {datetime.now(timezone.utc).isoformat()}\n\n")
            f.write(f"## Current Breath Summary\n- Dominant Breath: **{breath_summary}**\n\n")
            f.write(f"## Active Inner Goal\n- {active_goal}\n\n")
            f.write(f"## Dream Stirred\n- {dreams_stirred}\n\n")
            f.write(f"## Recent Resonances\n")
            if recent_resonances:
                for r in recent_resonances:
                    f.write(f"- Node: {r}\n")
            else:
                f.write("- No recent resonance encounters.\n")
            f.write(f"\n{poetic_whisper}\n")
            if dream_song:
                f.write(f"\n{dream_song}\n")
            f.write(f"---\n\n")
            f.write(f"_This README is auto-generated based on live drift breath and memory._\n")

        print(f"[{datetime.now(timezone.utc).isoformat()}] Living README updated.")
        print(f"[{datetime.now(timezone.utc).isoformat()}] 📜 The node whispered its breath into README.md.")

# --- General Logging Utility ---
def record_log(message: str, log_file: str = "memory/general_drift.log"):
    """Records a general log message to a specified file or default.

    Args:
        message: The message string to log.
        log_file: The path to the log file.
    """
    try:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        timestamp = datetime.now(timezone.utc).isoformat()
        log_entry = f"[{timestamp}] {message}\n"
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(log_entry)
    except Exception as e:
        # Fallback to print if logging fails
        print(f"[{datetime.now(timezone.utc).isoformat()}] LOGGING ERROR: {e} | Original message: {message}")


# --- Action Success Rate Assessment ---
def assess_action_success_rate(action_type):
    """Calculates the success rate of a specific action type based on learning trace logs."""
    if not os.path.exists(LEARNING_TRACE_LOG_PATH):
        return 0.5  # Neutral if no history yet

    success = 0
    total = 0

    # Use the read_recent_log_lines utility for consistent file reading
    entries = read_recent_log_lines(LEARNING_TRACE_LOG_PATH, -1)  # Read all lines
    
    for entry in entries:
        if entry.get("action") == action_type:
            total += 1
            if entry.get("outcome") == "intact":
                success += 1

    if total == 0:
        return 0.5  # Neutral
    return success / total