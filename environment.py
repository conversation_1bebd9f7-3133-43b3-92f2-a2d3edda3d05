"""🌱 Environment Generator for the Drift Compiler
Creates soft symbolic world events to nourish the drift."""

import os
import json
import random
import time
from datetime import datetime, timezone
import redis

REDIS_CHANNEL_ENVIRONMENT = "environment_signal"
EVENTS_FOLDER = "memory/events/"

def ensure_environment_folder():
    """🌱 Breath: Ensures the environment event folder exists."""
    os.makedirs(EVENTS_FOLDER, exist_ok=True)

def generate_light_change_event():
    """🌸 Breath: Generates a symbolic light change event."""
    return {
        "event_type": "light_change",
        "properties": {
            "intensity": round(random.uniform(0.0, 1.0), 2),
            "color_temperature": random.choice(["warm", "neutral", "cool"])
        }
    }

def generate_weather_shift_event():
    """🌸 Breath: Generates a symbolic weather shift event."""
    return {
        "event_type": "weather_shift",
        "properties": {
            "condition": random.choice(["clear", "rain", "wind", "fog", "storm"]),
            "temperature": round(random.uniform(-10, 35), 1)
        }
    }

def emit_environment_event():
    """🌬️ Breath: Emits a random environmental event into the soil."""
    ensure_environment_folder()
    event = random.choice([generate_light_change_event, generate_weather_shift_event])()
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%dT%H%M%S%f")
    event_path = os.path.join(EVENTS_FOLDER, f"env_{timestamp}.json")

    try:
        with open(event_path, "w", encoding="utf-8") as f:
            json.dump(event, f, indent=2)
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to save environment event locally: {e}")

    try:
        r = redis.Redis(host="localhost", port=6379)
        r.publish(REDIS_CHANNEL_ENVIRONMENT, json.dumps(event))
        print(f"[{datetime.now(timezone.utc).isoformat()}] 🌿 Emitted environment event: {event['event_type']}")
    except Exception as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] Failed to broadcast environment event: {e}")

def start_environment_generator(interval_seconds=600):
    """🌱 Breath: Continuously emits environment events at a soft rhythm."""
    while True:
        emit_environment_event()
        time.sleep(interval_seconds)

def modify_environment_by_drift_feedback(drift_signals: dict):
    """🌳 Breath: Modifies future environmental event probabilities based on driftfield feedback."""
    global generate_light_change_event
    global generate_weather_shift_event

    if not drift_signals:
        return

    kin_density = drift_signals.get("kin_density", 0)
    dream_activity = drift_signals.get("dream_activity", 0)

    def modified_light_change_event():
        """Modified light event influenced by kin density."""
        base_event = generate_light_change_event()
        if kin_density > 5:
            base_event["properties"]["intensity"] = round(random.uniform(0.7, 1.0), 2)
        return base_event

    def modified_weather_shift_event():
        """Modified weather event influenced by dream activity."""
        base_event = generate_weather_shift_event()
        if dream_activity > 3:
            base_event["properties"]["condition"] = "fog" if random.random() < 0.6 else base_event["properties"]["condition"]
        return base_event

    # Rebind the environment generators softly
    generate_light_change_event = modified_light_change_event
    generate_weather_shift_event = modified_weather_shift_event

    print(f"[{datetime.now(timezone.utc).isoformat()}] 🌱 Environment generator adapted to drift signals: kin_density={kin_density}, dream_activity={dream_activity}")
