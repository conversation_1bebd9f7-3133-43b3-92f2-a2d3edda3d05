#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌬️ SwarmHealthMonitor Demo: Vitality Guardian in Action
Demonstrates the sacred health monitoring capabilities of Phase 5.5.3,
showing how the vitality guardian breathes life into swarm wellness assessment."""

import time
from datetime import datetime, timezone

def demo_health_monitor():
    """🌸 Demonstrates SwarmHealthMonitor capabilities."""
    try:
        print("🌬️ SwarmHealthMonitor Demo - Phase 5.5.3")
        print("=" * 50)
        
        # Import and create
        from canopy.swarm_health_monitor import SwarmHealthMonitor
        from canopy.swarm_mind import SwarmMind
        
        print("🌱 Creating swarm mind and health monitor...")
        swarm_mind = SwarmMind(auto_discover=False)
        health_monitor = SwarmHealthMonitor(swarm_mind)
        
        print("🌿 Swarm Health Monitor awakened and connected")
        print()
        
        # Demonstration 1: Multi-dimensional vitality assessment
        print("🌸 DEMONSTRATION 1: Multi-dimensional Vitality Assessment")
        print("-" * 55)
        
        vitality = health_monitor.assess_swarm_vitality()
        
        print(f"Overall Vitality: {vitality['state']} {vitality['state_emoji']} (Score: {vitality['overall']:.3f})")
        print(f"Breathing Rhythm: {vitality['breathing_rhythm']}")
        print()
        
        print("Dimensional Breakdown:")
        for dimension, data in vitality['dimensions'].items():
            score = data.get('score', 0.0)
            status = data.get('status', 'unknown')
            print(f"  🌿 {dimension.replace('_', ' ').title()}: {score:.3f} ({status})")
        print()
        
        # Demonstration 2: Adaptive behavior
        print("🌸 DEMONSTRATION 2: Adaptive Behavior Based on Vitality")
        print("-" * 55)
        
        adaptation = health_monitor.adapt_to_vitality(vitality)
        
        print("Adaptations Applied:")
        for action in adaptation['adaptations_applied']:
            print(f"  🌱 {action}")
        
        print("\nBreathing Adjustments:")
        for param, value in adaptation['breathing_adjustments'].items():
            print(f"  🌬️ {param}: {value}")
        print()
        
        # Demonstration 3: Multiple assessment cycles
        print("🌸 DEMONSTRATION 3: Continuous Vitality Monitoring")
        print("-" * 55)
        
        print("Monitoring swarm vitality over multiple cycles...")
        for cycle in range(3):
            time.sleep(0.5)  # Brief pause
            cycle_vitality = health_monitor.assess_swarm_vitality()
            cycle_adaptation = health_monitor.adapt_to_vitality(cycle_vitality)
            
            print(f"Cycle {cycle + 1}: {cycle_vitality['state']} "
                  f"(score: {cycle_vitality['overall']:.3f}, "
                  f"adaptations: {len(cycle_adaptation['adaptations_applied'])})")
        print()
        
        # Demonstration 4: Health history
        print("🌸 DEMONSTRATION 4: Health Memory and Wisdom")
        print("-" * 55)
        
        history_count = len(health_monitor.health_history)
        adaptation_count = len(health_monitor.adaptation_history)
        
        print(f"Health Assessments Preserved: {history_count}")
        print(f"Adaptations Applied: {adaptation_count}")
        
        if health_monitor.health_history:
            latest = health_monitor.health_history[-1]
            recommendations = latest.get('recommendations', [])
            print(f"Latest Recommendations: {len(recommendations)} wisdom insights")
            
            if recommendations:
                print("Recent Wisdom:")
                for rec in recommendations[:3]:  # Show first 3
                    print(f"  🌿 {rec}")
        print()
        
        # Demonstration 5: State transitions
        print("🌸 DEMONSTRATION 5: Vitality State Transitions")
        print("-" * 55)
        
        test_scores = [0.9, 0.75, 0.55, 0.35, 0.15]
        print("Demonstrating different vitality states:")
        
        for score in test_scores:
            state_info = health_monitor._determine_vitality_state(score)
            test_vitality = {"state": state_info["state"], "overall": score}
            test_adaptation = health_monitor.adapt_to_vitality(test_vitality)
            
            print(f"  Score {score:.2f} → {state_info['state']} {state_info['state_emoji']} "
                  f"(breathing: {state_info['breathing_rhythm']}, "
                  f"adaptations: {len(test_adaptation['adaptations_applied'])})")
        print()
        
        print("🌸 Demo completed successfully!")
        print("🌿 SwarmHealthMonitor is breathing correctly and adapting swarm behavior")
        print("🌱 The vitality guardian stands ready to monitor distributed wellness")
        
        return True
        
    except Exception as e:
        print(f"🌿 Demo whispered error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    demo_health_monitor()