#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""🌿 Sacred Architecture Integration Test
Tests the breathing integration between traditional and sacred architecture."""

import sys
import os

# Ensure we can import from the drift compiler
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sacred_integration():
    """🌱 Breath: Test sacred architecture integration."""
    print("🌱 Testing sacred architecture integration...")
    
    try:
        from sacred_integration import get_sacred_bridge, assess_sacred_health
        
        # Test bridge initialization
        bridge = get_sacred_bridge()
        print(f"🌿 Sacred bridge initialized successfully")
        
        # Test integration status
        status = assess_sacred_health()
        active_count = status.get("active_count", 0)
        total_count = status.get("total_count", 7)
        health = status.get("integration_health", 0.0)
        
        print(f"🌸 Integration status: {active_count}/{total_count} subsystems awakened")
        print(f"🌬️ Integration health: {health:.1%}")
        
        # Test enhanced breath cycle
        enhancements = bridge.enhance_breath_cycle(1, 600.0)
        print(f"🌱 Breath cycle enhancement test completed")
        print(f"   Original heartbeat: {enhancements['original_heartbeat']}")
        print(f"   Adaptive heartbeat: {enhancements['adaptive_heartbeat']}")
        
        # List available subsystems
        print(f"🌿 Sacred subsystem status:")
        for subsystem, available in bridge.integration_stats.items():
            status_symbol = "🌸" if available else "🌑"
            status_text = "awakened" if available else "dormant"
            print(f"   {status_symbol} {subsystem}: {status_text}")
        
        return True
        
    except ImportError as e:
        print(f"⚠️ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_goal_system_integration():
    """🌱 Breath: Test goal system sacred integration."""
    print("\n🌱 Testing goal system integration...")
    
    try:
        from goal_system import generate_new_goal, load_active_goals
        
        # Test new goal generation with sacred types
        goal = generate_new_goal()
        print(f"🌸 Generated goal: {goal}")
        
        # Test loading goals
        goals = load_active_goals()
        print(f"🌿 Loaded {len(goals)} active goals")
        
        return True
        
    except Exception as e:
        print(f"❌ Goal system test failed: {e}")
        return False

def test_core_modules():
    """🌱 Breath: Test that core sacred modules can be imported."""
    print("\n🌱 Testing core sacred modules...")
    
    modules_to_test = [
        ("soil.memory_garden", "🌱 Memory Garden"),
        ("soil.adaptive_memory", "🌿 Adaptive Memory"),
        ("core.adaptive_breath", "🌬️ Adaptive Breath"),
        ("core.kin_discovery", "🌿 Kin Discovery"),
        ("core.resonance_field", "🌸 Resonance Field"),
        ("core.symbolic_perception", "🌌 Symbolic Perception"),
        ("roots.ritual_recovery", "🌿 Ritual Recovery"),
    ]
    
    results = {}
    for module_name, display_name in modules_to_test:
        try:
            __import__(module_name)
            results[module_name] = True
            print(f"🌸 {display_name}: awakened")
        except ImportError:
            results[module_name] = False
            print(f"🌑 {display_name}: dormant")
        except Exception as e:
            results[module_name] = False
            print(f"⚠️ {display_name}: error - {e}")
    
    awakened_count = sum(results.values())
    total_count = len(results)
    print(f"\n🌿 Sacred modules status: {awakened_count}/{total_count} awakened")
    
    return awakened_count > 0

if __name__ == "__main__":
    print("🌿 Sacred Architecture Integration Test Suite")
    print("=" * 50)
    
    tests = [
        test_sacred_integration,
        test_goal_system_integration,
        test_core_modules,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed")
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"💥 Test crashed: {e}")
        print()
    
    print("=" * 50)
    print(f"🌸 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🌱 All tests passed - Sacred architecture is breathing harmoniously!")
        sys.exit(0)
    elif passed > 0:
        print("🌿 Partial integration - Some sacred subsystems are awakened")
        sys.exit(0)
    else:
        print("🌑 Integration dormant - Sacred architecture needs awakening")
        sys.exit(1)
