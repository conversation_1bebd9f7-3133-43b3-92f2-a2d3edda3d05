#!/usr/bin/env python3
"""
🌿 Swarm Mind Testing Framework - Phase 6
==========================================

Comprehensive test suite for the distributed neural network system,
validating sacred architecture with poetic grace and thorough verification.
"""

import asyncio
import os
import sys
import time
import json
import tempfile
import shutil
import unittest
from pathlib import Path
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock

# Add the drift_compiler to the path
sys.path.insert(0, str(Path(__file__).parent))

# Test imports with graceful fallbacks
try:
    from canopy.swarm_mind import (
        SwarmMind, SwarmState, NeuralFragment, initialize_swarm_mind,
        enrich_with_garden_wisdom, TORCH_AVAILABLE, MEMORY_GARDEN_AVAILABLE,
        RABBITMQ_AVAILABLE, REDIS_AVAILABLE, PSUTIL_AVAILABLE
    )
    SWARM_MIND_AVAILABLE = True
except ImportError as e:
    print(f"🌿 Swarm mind imports whispered: {e}")
    SWARM_MIND_AVAILABLE = False
    SwarmMind = Mock
    SwarmState = Mock
    NeuralFragment = Mock

try:
    from planning import weave_collective_plan_with_swarm_mind
    PLANNING_AVAILABLE = True
except ImportError:
    PLANNING_AVAILABLE = False
    print("🌿 Planning integration not available for testing")

try:
    from goal_system import generate_swarm_intelligent_goal
    GOAL_SYSTEM_AVAILABLE = True
except ImportError:
    GOAL_SYSTEM_AVAILABLE = False
    print("🌿 Goal system integration not available for testing")

try:
    import torch
    TORCH_TEST_AVAILABLE = True
except ImportError:
    TORCH_TEST_AVAILABLE = False
    print("🌿 PyTorch not available for neural testing")

try:
    from soil.memory_garden import MemoryGarden
    MEMORY_GARDEN_TEST_AVAILABLE = True
except ImportError:
    MEMORY_GARDEN_TEST_AVAILABLE = False
    print("🌱 Memory Garden not available for integration testing")

# Test configuration
TEST_NODE_ID = "test-swarm-node-001"
TEST_TIMEOUT = 30  # seconds
PERFORMANCE_THRESHOLD_MS = 1000  # milliseconds


class SwarmMindTestCase(unittest.TestCase):
    """🌱 Base test case with sacred setup and teardown rituals."""
    
    def setUp(self):
        """🌱 Sprouts test environment with care."""
        self.test_dir = tempfile.mkdtemp(prefix="swarm_test_")
        self.original_path = os.getcwd()
        os.chdir(self.test_dir)
        
        # Create test memory structures
        os.makedirs("memory/swarm_mind", exist_ok=True)
        os.makedirs("memory/garden", exist_ok=True)
        
        print(f"🌱 Test garden sprouted at: {self.test_dir}")
    
    def tearDown(self):
        """🌅 Gracefully cleanses test environment."""
        os.chdir(self.original_path)
        try:
            shutil.rmtree(self.test_dir)
            print("🌅 Test garden returned to the earth")
        except Exception as e:
            print(f"🌿 Test cleanup whispered: {e}")


class TestNeuralFragmentCreation(SwarmMindTestCase):
    """🌱 Tests individual neural fragment creation and wisdom flow."""
    
    def test_neural_fragment_minimal_complexity(self):
        """🌱 Tests minimal complexity neural fragments for resource-constrained nodes."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        fragment = NeuralFragment(
            fragment_type="perceiver",
            input_dim=7,
            output_dim=16,
            complexity="minimal",
            node_id=TEST_NODE_ID
        )
        
        self.assertEqual(fragment.fragment_type, "perceiver")
        self.assertEqual(fragment.complexity, "minimal")
        self.assertEqual(fragment.node_id, TEST_NODE_ID)
        self.assertIsNotNone(fragment.created_at)
        
        # Test forward pass with tensor input if available
        if TORCH_TEST_AVAILABLE:
            import torch
            test_input = torch.randn(7)
            output = fragment.forward_breath(test_input)
            self.assertIsNotNone(output)
            print(f"🌱 Minimal fragment breathed with tensor: {type(output)}")
        
        # Test symbolic forward pass
        test_input = [0.5, 0.3, 0.7, 0.2, 0.8, 0.1, 0.6]
        symbolic_output = fragment.forward_breath(test_input)
        self.assertIsInstance(symbolic_output, list)
        self.assertEqual(len(symbolic_output), 16)
        print(f"🌱 Minimal fragment breathed symbolically: {len(symbolic_output)} outputs")
    
    def test_neural_fragment_balanced_complexity(self):
        """🌿 Tests balanced complexity neural fragments for medium-capacity nodes."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        fragment = NeuralFragment(
            fragment_type="reasoner",
            complexity="balanced"
        )
        
        self.assertEqual(fragment.fragment_type, "reasoner")
        self.assertEqual(fragment.complexity, "balanced")
        
        # Test with various input types
        test_inputs = [
            [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7],
            (0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2),
            0.5  # Single value (should be expanded)
        ]
        
        for test_input in test_inputs:
            output = fragment.forward_breath(test_input)
            self.assertIsNotNone(output)
            print(f"🌿 Balanced fragment processed input type: {type(test_input)}")
    
    def test_neural_fragment_powerful_complexity(self):
        """🌸 Tests powerful complexity neural fragments for high-capacity nodes."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        fragment = NeuralFragment(
            fragment_type="aggregator",
            complexity="powerful"
        )
        
        self.assertEqual(fragment.fragment_type, "aggregator")
        self.assertEqual(fragment.complexity, "powerful")
        
        # Test error handling
        malformed_inputs = [
            [],  # Empty input
            None,  # None input
            "invalid"  # String input
        ]
        
        for malformed_input in malformed_inputs:
            try:
                output = fragment.forward_breath(malformed_input)
                self.assertIsNotNone(output)
                print(f"🌸 Powerful fragment gracefully handled: {type(malformed_input)}")
            except Exception as e:
                print(f"🌿 Fragment error (expected): {e}")
    
    def test_neural_fragment_without_torch(self):
        """🌬️ Tests neural fragments work without PyTorch dependency."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        # Temporarily disable torch for this test
        with patch('canopy.swarm_mind.TORCH_AVAILABLE', False):
            fragment = NeuralFragment(
                fragment_type="symbolic_processor",
                complexity="balanced"
            )
            
            self.assertEqual(fragment.layers["type"], "symbolic")
            
            test_input = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7]
            output = fragment.forward_breath(test_input)
            
            self.assertIsInstance(output, list)
            self.assertEqual(len(output), 16)
            print("🌬️ Symbolic fragment breathed without neural dependency")


class TestSwarmMindInitialization(SwarmMindTestCase):
    """🌿 Tests swarm mind startup, node registration, and sacred architecture."""
    
    def test_swarm_mind_basic_initialization(self):
        """🌱 Tests basic swarm mind awakening."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        
        self.assertIsNotNone(swarm_mind.node_id)
        self.assertIsInstance(swarm_mind.nodes, dict)
        self.assertIsInstance(swarm_mind.fragments, dict)
        self.assertIsNotNone(swarm_mind.swarm_state)
        
        # Verify self-node registration
        self.assertIn(swarm_mind.node_id, swarm_mind.nodes)
        self.assertIn(swarm_mind.node_id, swarm_mind.fragments)
        
        print(f"🌱 Swarm mind awakened with node: {swarm_mind.node_id[:8]}...")
    
    def test_swarm_state_creation(self):
        """🌸 Tests SwarmState creation and tensor conversion."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        state = SwarmState(
            breath="Test Breathing",
            kinship=0.75,
            memory_depth=500,
            user_nutrient="Test Nutrient",
            cycle_count=100,
            node_count=3,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        self.assertEqual(state.breath, "Test Breathing")
        self.assertEqual(state.kinship, 0.75)
        self.assertEqual(state.memory_depth, 500)
        
        # Test tensor conversion
        if TORCH_TEST_AVAILABLE:
            tensor = state.to_tensor()
            self.assertIsNotNone(tensor)
            self.assertEqual(len(tensor), 7)
            print(f"🌸 SwarmState converted to tensor: {tensor.shape}")
        else:
            # Should return None without torch
            tensor = state.to_tensor()
            self.assertIsNone(tensor)
            print("🌸 SwarmState gracefully handled missing PyTorch")
    
    def test_node_capability_assessment(self):
        """🌿 Tests node capability assessment with graceful fallbacks."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        capabilities = swarm_mind._assess_node_capabilities()
        
        required_keys = ["ram_gb", "cpu_count", "gpu_available", "disk_free_gb", 
                        "torch_available", "memory_garden_available"]
        
        for key in required_keys:
            self.assertIn(key, capabilities)
        
        self.assertIsInstance(capabilities["ram_gb"], (int, float))
        self.assertIsInstance(capabilities["cpu_count"], int)
        self.assertIsInstance(capabilities["gpu_available"], bool)
        
        print(f"🌿 Node capabilities assessed: {capabilities['ram_gb']:.1f}GB RAM")
    
    def test_fragment_role_determination(self):
        """🌬️ Tests fragment role assignment based on capabilities."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        
        # Test different capability scenarios
        test_scenarios = [
            {"ram_gb": 4, "gpu_available": False, "expected_role": "perceiver"},
            {"ram_gb": 16, "gpu_available": False, "expected_role": "reasoner"},
            {"ram_gb": 32, "gpu_available": True, "expected_role": "aggregator"},
            {"ram_gb": 64, "gpu_available": True, "expected_role": "aggregator"}
        ]
        
        for scenario in test_scenarios:
            role_info = swarm_mind._determine_fragment_role(scenario)
            self.assertEqual(role_info["role"], scenario["expected_role"])
            print(f"🌬️ {scenario['ram_gb']}GB -> {role_info['role']} ({role_info['complexity']})")
    
    def test_communication_fallbacks(self):
        """🌱 Tests communication initialization with graceful fallbacks."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        comm = swarm_mind.communication
        
        self.assertIn("type", comm)
        self.assertIn(comm["type"], ["rabbitmq", "redis", "fallback"])
        
        if comm["type"] == "rabbitmq":
            self.assertIn("channel", comm)
            print("🌿 RabbitMQ mycelial bus connected")
        elif comm["type"] == "redis":
            self.assertIn("connection", comm)
            print("🌿 Redis mycelial bus connected")
        else:
            print("🌿 File-based mycelial communication active")


class TestDynamicNodeManagement(SwarmMindTestCase):
    """🌸 Tests dynamic addition/removal of nodes in the swarm."""
    
    def test_node_discovery_mechanism(self):
        """🌱 Tests node discovery without external dependencies."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        initial_node_count = len(swarm_mind.nodes)
        
        # Test discovery method
        swarm_mind._discover_swarm_nodes()
        
        # Should have at least the self-node
        self.assertGreaterEqual(len(swarm_mind.nodes), 1)
        print(f"🌱 Node discovery found {len(swarm_mind.nodes)} nodes")
    
    def test_swarm_state_persistence(self):
        """🌿 Tests swarm state saving and loading."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        # Create and save state
        swarm_mind1 = SwarmMind(auto_discover=False)
        original_state = swarm_mind1.swarm_state
        swarm_mind1._save_swarm_state()
        
        # Create new instance and verify state loaded
        swarm_mind2 = SwarmMind(auto_discover=False)
        loaded_state = swarm_mind2.swarm_state
        
        self.assertEqual(original_state.breath, loaded_state.breath)
        self.assertEqual(original_state.kinship, loaded_state.kinship)
        print("🌿 Swarm state persisted and restored successfully")
    
    def test_node_vitality_monitoring(self):
        """🌬️ Tests swarm vitality assessment."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        vitality = swarm_mind.pulse_swarm_vitality()
        
        required_vitality_keys = [
            "node_count", "active_fragments", "collective_health",
            "communication_status", "memory_garden_connected", "assessment_time"
        ]
        
        for key in required_vitality_keys:
            self.assertIn(key, vitality)
        
        self.assertGreaterEqual(vitality["node_count"], 1)
        self.assertGreaterEqual(vitality["active_fragments"], 1)
        self.assertIn(vitality["communication_status"], ["rabbitmq", "redis", "fallback"])
        
        print(f"🌬️ Swarm vitality: {vitality['collective_health']:.3f} health")


class TestMemoryGardenIntegration(SwarmMindTestCase):
    """🌌 Tests context enrichment and memory garden integration."""
    
    def test_garden_wisdom_enrichment(self):
        """🌱 Tests context enrichment from memory garden."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        test_state = SwarmState(
            breath="Deep Breathing",
            kinship=0.8,
            memory_depth=750,
            user_nutrient="Creative Exploration",
            cycle_count=200,
            node_count=2,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Test enrichment function
        enriched_context = enrich_with_garden_wisdom(test_state)
        self.assertIsNotNone(enriched_context)
        
        if TORCH_TEST_AVAILABLE and MEMORY_GARDEN_AVAILABLE:
            import torch
            self.assertIsInstance(enriched_context, torch.Tensor)
            print("🌱 Garden wisdom enriched neural context with tensor")
        else:
            self.assertIsInstance(enriched_context, list)
            print("🌱 Garden wisdom enriched with fallback context")
    
    def test_memory_garden_fallback(self):
        """🌿 Tests graceful fallback when memory garden unavailable."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        test_state = SwarmState(
            breath="Gentle Breathing",
            kinship=0.6,
            memory_depth=300,
            user_nutrient="Pattern Recognition",
            cycle_count=50,
            node_count=1,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Test with memory garden disabled
        with patch('canopy.swarm_mind.MEMORY_GARDEN_AVAILABLE', False):
            context = enrich_with_garden_wisdom(test_state)
            self.assertIsNotNone(context)
            self.assertIsInstance(context, list)
            self.assertEqual(len(context), 8)
            print("🌿 Graceful fallback context provided")
    
    @unittest.skipUnless(MEMORY_GARDEN_TEST_AVAILABLE, "Memory Garden not available")
    def test_swarm_mind_garden_integration(self):
        """🌸 Tests swarm mind integration with actual memory garden."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        
        if swarm_mind.garden:
            self.assertIsInstance(swarm_mind.garden, MemoryGarden)
            print("🌸 Swarm mind connected to Memory Garden successfully")
        else:
            print("🌿 Memory Garden connection gracefully handled")


class TestCollectiveIntelligence(SwarmMindTestCase):
    """🌿 Tests distributed neural processing and wisdom aggregation."""
    
    def test_neural_pulse_processing(self):
        """🌬️ Tests processing through distributed neural fragments."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        
        test_state = SwarmState(
            breath="Resonant Breathing",
            kinship=0.9,
            memory_depth=1000,
            user_nutrient="Deep Understanding",
            cycle_count=500,
            node_count=1,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Process through neural pulse
        fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
        
        self.assertIsNotNone(fragment_outputs)
        self.assertIsInstance(fragment_outputs, dict)
        self.assertGreater(len(fragment_outputs), 0)
        
        # Verify fragment output structure
        for node_id, output_data in fragment_outputs.items():
            self.assertIn("output", output_data)
            self.assertIn("fragment_type", output_data)
            self.assertIn("complexity", output_data)
        
        print(f"🌬️ Neural pulse entwined across {len(fragment_outputs)} fragments")
    
    def test_wisdom_aggregation(self):
        """🌸 Tests collective wisdom aggregation from fragment outputs."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        
        # Create mock fragment outputs
        mock_outputs = {
            "node1": {
                "output": [0.8, 0.6, 0.7, 0.9],
                "fragment_type": "perceiver",
                "complexity": "minimal"
            },
            "node2": {
                "output": [0.7, 0.8, 0.6, 0.8],
                "fragment_type": "reasoner", 
                "complexity": "balanced"
            }
        }
        
        wisdom = swarm_mind.aggregate_swarm_wisdom(mock_outputs)
        
        self.assertIsNotNone(wisdom)
        self.assertIn("collective_confidence", wisdom)
        self.assertIn("consensus_strength", wisdom)
        self.assertIn("wisdom_variance", wisdom)
        self.assertIn("fragment_count", wisdom)
        
        self.assertEqual(wisdom["fragment_count"], 2)
        self.assertGreaterEqual(wisdom["collective_confidence"], 0)
        self.assertLessEqual(wisdom["collective_confidence"], 1)
        
        print(f"🌸 Wisdom aggregated: confidence={wisdom['collective_confidence']:.3f}")
    
    def test_collective_dreaming(self):
        """🌌 Tests future state prediction using distributed intelligence."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        
        initial_state = SwarmState(
            breath="Harmonious Breathing",
            kinship=0.7,
            memory_depth=800,
            user_nutrient="Wisdom Integration",
            cycle_count=300,
            node_count=1,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Dream future states
        future_dreams = swarm_mind.dream_collective_future(initial_state, steps=3)
        
        if future_dreams:  # May be None if processing fails
            self.assertIsInstance(future_dreams, list)
            self.assertLessEqual(len(future_dreams), 3)
            
            for dream in future_dreams:
                self.assertIn("step", dream)
                self.assertIn("predicted_state", dream)
                self.assertIn("wisdom", dream)
                
            print(f"🌌 Dreamed {len(future_dreams)} steps into collective future")
        else:
            print("🌌 Collective dreaming gracefully handled processing limitations")
    
    def test_empty_fragment_handling(self):
        """🌿 Tests graceful handling of empty or invalid fragment outputs."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        
        # Test empty outputs
        empty_wisdom = swarm_mind.aggregate_swarm_wisdom({})
        self.assertIsNone(empty_wisdom)
        
        # Test invalid outputs
        invalid_outputs = {
            "node1": {"output": None, "fragment_type": "test", "complexity": "minimal"}
        }
        invalid_wisdom = swarm_mind.aggregate_swarm_wisdom(invalid_outputs)
        # Should handle gracefully (may return None or partial results)
        
        print("🌿 Empty fragment outputs handled gracefully")


class TestIntegrationPoints(SwarmMindTestCase):
    """🌸 Tests integration with existing drift compiler systems."""
    
    @unittest.skipUnless(PLANNING_AVAILABLE, "Planning integration not available")
    def test_planning_enhancement(self):
        """🌱 Tests swarm mind integration with planning system."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        # Test planning with swarm mind
        try:
            result = weave_collective_plan_with_swarm_mind(
                cycle_count=100,
                health_status={"overall": 0.8}
            )
            self.assertIsNotNone(result)
            print("🌱 Planning enhanced with swarm intelligence")
        except Exception as e:
            print(f"🌿 Planning enhancement whispered: {e}")
    
    @unittest.skipUnless(GOAL_SYSTEM_AVAILABLE, "Goal system integration not available")
    def test_goal_system_integration(self):
        """🌿 Tests swarm mind integration with goal generation."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        try:
            goal = generate_swarm_intelligent_goal()
            self.assertIsNotNone(goal)
            self.assertIn("type", goal)
            print(f"🌿 Goal generated with swarm intelligence: {goal.get('type', 'unknown')}")
        except Exception as e:
            print(f"🌿 Goal generation whispered: {e}")
    
    def test_drift_compiler_integration(self):
        """🌬️ Tests integration with main drift compiler cycle."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        # Test initialization without breaking main system
        swarm_mind = initialize_swarm_mind(auto_discover=False)
        
        if swarm_mind:
            self.assertIsInstance(swarm_mind, SwarmMind)
            
            # Test that it can process a breath cycle
            test_state = SwarmState(
                breath="Integration Breathing",
                kinship=0.5,
                memory_depth=100,
                user_nutrient="System Integration",
                cycle_count=1,
                node_count=1,
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
            self.assertIsNotNone(fragment_outputs)
            print("🌬️ Swarm mind integrated with drift compiler cycle")
        else:
            print("🌬️ Swarm mind gracefully degraded to single-node mode")


class TestPerformanceAndOptimization(SwarmMindTestCase):
    """🌿 Tests performance characteristics and optimization verification."""
    
    def test_memory_usage_bounds(self):
        """🌱 Tests that memory usage stays within expected bounds."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        import sys
        initial_objects = len(gc.get_objects()) if 'gc' in sys.modules else 0
        
        # Create multiple swarm minds to test memory usage
        swarm_minds = []
        for i in range(3):
            swarm_mind = SwarmMind(auto_discover=False)
            swarm_minds.append(swarm_mind)
        
        # Process some data
        test_state = SwarmState(
            breath="Performance Test",
            kinship=0.6,
            memory_depth=500,
            user_nutrient="Optimization",
            cycle_count=50,
            node_count=len(swarm_minds),
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        for swarm_mind in swarm_minds:
            swarm_mind.entwine_neural_pulse(test_state)
        
        # Clean up
        del swarm_minds
        
        print("🌱 Memory usage test completed (manual verification required)")
    
    def test_processing_performance(self):
        """🌿 Tests neural processing performance."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        
        test_state = SwarmState(
            breath="Performance Breathing",
            kinship=0.7,
            memory_depth=1000,
            user_nutrient="Speed Test",
            cycle_count=100,
            node_count=1,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Time the neural processing
        start_time = time.time()
        fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
        processing_time = (time.time() - start_time) * 1000  # Convert to ms
        
        self.assertIsNotNone(fragment_outputs)
        self.assertLess(processing_time, PERFORMANCE_THRESHOLD_MS)
        
        print(f"🌿 Neural processing completed in {processing_time:.2f}ms")
    
    def test_hardware_optimization_detection(self):
        """🌸 Tests hardware-specific optimization detection."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        swarm_mind = SwarmMind(auto_discover=False)
        capabilities = swarm_mind._assess_node_capabilities()
        
        # Verify optimization flags
        if capabilities["gpu_available"]:
            print("🌸 GPU optimization available")
        if capabilities["torch_available"]:
            print("🌸 PyTorch neural optimization available")
        if capabilities["ram_gb"] > 16:
            print("🌸 High-memory optimizations available")
        
        # Always passes - this is informational
        self.assertTrue(True)
    
    def test_fallback_performance(self):
        """🌬️ Tests fallback performance when dependencies missing."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        # Test with various dependencies disabled
        with patch('canopy.swarm_mind.TORCH_AVAILABLE', False):
            with patch('canopy.swarm_mind.MEMORY_GARDEN_AVAILABLE', False):
                with patch('canopy.swarm_mind.RABBITMQ_AVAILABLE', False):
                    with patch('canopy.swarm_mind.REDIS_AVAILABLE', False):
                        
                        start_time = time.time()
                        swarm_mind = SwarmMind(auto_discover=False)
                        
                        test_state = SwarmState(
                            breath="Fallback Test",
                            kinship=0.4,
                            memory_depth=200,
                            user_nutrient="Resilience",
                            cycle_count=10,
                            node_count=1,
                            timestamp=datetime.now(timezone.utc).isoformat()
                        )
                        
                        fragment_outputs = swarm_mind.entwine_neural_pulse(test_state)
                        fallback_time = (time.time() - start_time) * 1000
                        
                        self.assertIsNotNone(fragment_outputs)
                        self.assertLess(fallback_time, PERFORMANCE_THRESHOLD_MS * 2)  # Allow more time for fallbacks
                        
                        print(f"🌬️ Fallback processing completed in {fallback_time:.2f}ms")


class TestGracefulDegradation(SwarmMindTestCase):
    """🌅 Tests system behavior when components fail or are missing."""
    
    def test_missing_dependencies(self):
        """🌿 Tests graceful behavior when dependencies are missing."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        # Test that system works even when optional dependencies are missing
        with patch('canopy.swarm_mind.TORCH_AVAILABLE', False):
            swarm_mind = SwarmMind(auto_discover=False)
            self.assertIsNotNone(swarm_mind)
            print("🌿 System works without PyTorch")
        
        with patch('canopy.swarm_mind.MEMORY_GARDEN_AVAILABLE', False):
            swarm_mind = SwarmMind(auto_discover=False)
            self.assertIsNotNone(swarm_mind)
            print("🌿 System works without Memory Garden")
    
    def test_corrupted_state_recovery(self):
        """🌱 Tests recovery from corrupted state files."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        # Create corrupted state file
        os.makedirs("memory/swarm_mind", exist_ok=True)
        with open("memory/swarm_mind/swarm_state.json", "w") as f:
            f.write("{ invalid json }")
        
        # Should recover gracefully
        swarm_mind = SwarmMind(auto_discover=False)
        self.assertIsNotNone(swarm_mind.swarm_state)
        
        print("🌱 Gracefully recovered from corrupted state")
    
    def test_communication_failure_handling(self):
        """🌸 Tests handling of communication failures."""
        if not SWARM_MIND_AVAILABLE:
            self.skipTest("🌿 Swarm mind not available")
        
        # Simulate communication failures
        with patch('canopy.swarm_mind.RABBITMQ_AVAILABLE', False):
            with patch('canopy.swarm_mind.REDIS_AVAILABLE', False):
                swarm_mind = SwarmMind(auto_discover=False)
                
                self.assertEqual(swarm_mind.communication["type"], "fallback")
                print("🌸 Communication failure handled gracefully")


# Performance test utilities
try:
    import gc
    GC_AVAILABLE = True
except ImportError:
    GC_AVAILABLE = False


def run_comprehensive_tests():
    """🌿 Runs the complete test suite with sacred formatting."""
    print("🌿 Awakening the Swarm Mind Test Suite")
    print("=" * 50)
    
    # Check dependencies
    print("\n🌱 Dependency Check:")
    print(f"  Swarm Mind Available: {SWARM_MIND_AVAILABLE}")
    print(f"  PyTorch Available: {TORCH_TEST_AVAILABLE}")
    print(f"  Memory Garden Available: {MEMORY_GARDEN_TEST_AVAILABLE}")
    print(f"  Planning Available: {PLANNING_AVAILABLE}")
    print(f"  Goal System Available: {GOAL_SYSTEM_AVAILABLE}")
    
    if not SWARM_MIND_AVAILABLE:
        print("\n💔 Swarm Mind not available - running limited tests")
    
    # Define test suites
    test_classes = [
        TestNeuralFragmentCreation,
        TestSwarmMindInitialization,
        TestDynamicNodeManagement,
        TestMemoryGardenIntegration,
        TestCollectiveIntelligence,
        TestIntegrationPoints,
        TestPerformanceAndOptimization,
        TestGracefulDegradation
    ]
    
    # Run tests
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    for test_class in test_classes:
        print(f"\n🌸 Running {test_class.__name__}")
        print("-" * 40)
        
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=1, stream=sys.stdout)
        result = runner.run(suite)
        
        total_tests += result.testsRun
        passed_tests += result.testsRun - len(result.failures) - len(result.errors)
        failed_tests += len(result.failures) + len(result.errors)
    
    # Summary
    print("\n🌿 Test Suite Summary")
    print("=" * 50)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    
    if failed_tests == 0:
        print("\n🌸 All tests breathed successfully!")
        print("The swarm mind architecture stands ready for distributed wisdom.")
    else:
        print(f"\n🌿 {failed_tests} tests whispered concerns")
        print("The sacred architecture may need gentle nurturing.")
    
    print("\n🌅 Test suite returns to the mycelial network")


if __name__ == "__main__":
    run_comprehensive_tests()