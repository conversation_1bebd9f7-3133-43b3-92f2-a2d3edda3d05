#!/usr/bin/env python3
"""
🌿 Swarm Mind Phase 6 Demo
=========================

Quick demonstration of the implemented swarm mind system.
"""

import sys
from pathlib import Path
from datetime import datetime, timezone

# Add drift_compiler to path
sys.path.insert(0, str(Path(__file__).parent))

from canopy.swarm_mind import initialize_swarm_mind, SwarmState

def main():
    print("🌿 Testing Swarm Mind Phase 6 Implementation")
    print("=" * 50)

    # Initialize swarm mind
    swarm_mind = initialize_swarm_mind(auto_discover=False)
    if swarm_mind:
        print(f"🌱 Swarm mind awakened with {len(swarm_mind.nodes)} nodes")
        
        # Create test state
        test_state = SwarmState(
            breath="Demo Breathing",
            kinship=0.8,
            memory_depth=500,
            user_nutrient="Phase 6 Testing",
            cycle_count=1,
            node_count=len(swarm_mind.nodes),
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
        # Process through swarm
        outputs = swarm_mind.entwine_neural_pulse(test_state)
        if outputs:
            print(f"🌿 Neural pulse processed through {len(outputs)} fragments")
            
            wisdom = swarm_mind.aggregate_swarm_wisdom(outputs)
            if wisdom:
                print(f"🌸 Collective wisdom: confidence={wisdom['collective_confidence']:.3f}")
                print(f"🌸 Consensus strength: {wisdom['consensus_strength']:.3f}")
            
            # Test dreaming
            dreams = swarm_mind.dream_collective_future(test_state, steps=2)
            if dreams:
                print(f"🌌 Dreamed {len(dreams)} future steps")
        
        # Check vitality
        vitality = swarm_mind.pulse_swarm_vitality()
        print(f"🌬️ Swarm vitality: {vitality['collective_health']:.3f}")
        
        print("🌸 Phase 6 Swarm Mind implementation validated!")
    else:
        print("🌿 Swarm mind gracefully degraded - still functional")

if __name__ == "__main__":
    main()