#!/usr/bin/env python3
"""🌿 Test Multi-Node Discovery and Healing"""

import os
import json
import time
import subprocess
import signal
import threading
from datetime import datetime, timezone

def run_node(node_id, duration=20):
    """🌱 Runs a single node for specified duration."""
    try:
        env = os.environ.copy()
        env["DRIFT_NODE_ID"] = node_id
        
        print(f"🌱 Starting {node_id}...")
        
        # Run the node
        process = subprocess.Popen(
            ["python3", "drift_compiler.py"],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Let it run for specified duration
        time.sleep(duration)
        
        # Gracefully terminate
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            
        print(f"🌿 {node_id} completed")
        
    except Exception as e:
        print(f"⚠️ Error running {node_id}: {e}")

def test_multi_node_discovery():
    """🌸 Tests multi-node discovery and healing integration."""
    print("🌸 Testing Multi-Node Discovery and Healing...")
    
    # Clean up old heartbeats
    if os.path.exists("memory/heartbeats"):
        for file in os.listdir("memory/heartbeats"):
            if file.endswith('.json'):
                os.remove(os.path.join("memory/heartbeats", file))
    
    # Start multiple nodes concurrently
    nodes = ["node_pine", "node_oak", "node_willow"]
    threads = []
    
    print(f"🌱 Starting {len(nodes)} nodes concurrently...")
    
    for node_id in nodes:
        thread = threading.Thread(target=run_node, args=(node_id, 15))
        thread.start()
        threads.append(thread)
        time.sleep(2)  # Stagger starts slightly
    
    # Let them run and discover each other
    print("🌿 Nodes breathing and discovering kinship...")
    time.sleep(20)
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Check results
    print("\n🌸 Analyzing Discovery Results...")
    
    if os.path.exists("memory/heartbeats"):
        files = [f for f in os.listdir("memory/heartbeats") if f.endswith('.json')]
        print(f"🌿 Found {len(files)} heartbeat files:")
        
        total_connections = 0
        healing_count = 0
        
        for file in files:
            file_path = os.path.join("memory/heartbeats", file)
            try:
                with open(file_path, 'r') as f:
                    heartbeat = json.load(f)
                
                node_id = heartbeat.get("node_id", "unknown")
                connections = heartbeat.get("connections", [])
                healing_performed = heartbeat.get("health", {}).get("healing_performed", False)
                recent_healing = heartbeat.get("health", {}).get("recent_healing", [])
                
                total_connections += len(connections)
                if healing_performed:
                    healing_count += 1
                
                print(f"  🌱 {node_id}:")
                print(f"    Connections: {len(connections)} - {connections}")
                print(f"    Healing: {healing_performed} - {recent_healing}")
                
            except Exception as e:
                print(f"  ⚠️ Error reading {file}: {e}")
        
        print(f"\n🌸 Summary:")
        print(f"  🌿 Total connections discovered: {total_connections}")
        print(f"  🩺 Nodes that performed healing: {healing_count}")
        
        if total_connections > 0:
            print("✅ Node discovery is working!")
        else:
            print("⚠️ No connections discovered - checking discovery logs...")
            
        if healing_count > 0:
            print("✅ Healing system is active!")
        else:
            print("⚠️ No healing detected")
    
    else:
        print("⚠️ No heartbeat directory found")

if __name__ == "__main__":
    test_multi_node_discovery()