
# 🍎 Apple Silicon Acceleration Integration Guide  

> **Goal** – Use the M-series GPU / Neural Engine automatically on Apple Silicon,  
> fall back to pure-CPU everywhere else (Intel Mac, Linux, Windows, CI).

---

## 1 · Runtime helper – `utils/apple_accel.py`

```python
# utils/apple_accel.py
"""Apple-Silicon-aware device / session helpers."""

import os, platform
from importlib.util import find_spec

# ---------- core detection ----------
def _on_apple_silicon() -> bool:
    if os.getenv("DRIFT_FORCE_CPU"):
        return False
    if os.getenv("DRIFT_FORCE_GPU"):
        return True
    return platform.system() == "Darwin" and platform.machine() == "arm64"

# ---------- PyTorch ----------
def torch_device():
    import torch
    if _on_apple_silicon() and torch.backends.mps.is_available():
        return torch.device("mps")
    return torch.device("cpu")

# ---------- ONNX Runtime ----------
def onnx_session(model_path: str, prefer_coreml: bool = True):
    import onnxruntime as ort
    providers = []
    if prefer_coreml and _on_apple_silicon():
        avail = ort.get_available_providers()
        if "CoreMLExecutionProvider" in avail:
            providers.append("CoreMLExecutionProvider")      # Neural Engine
        elif "MpsExecutionProvider" in avail:
            providers.append("MpsExecutionProvider")         # Metal GPU
    providers.append("CPUExecutionProvider")                 # always last
    return ort.InferenceSession(model_path, providers=providers)

# ---------- TensorFlow ----------
def tf_device():
    import tensorflow as tf
    gpus = tf.config.list_physical_devices("GPU")
    if _on_apple_silicon() and gpus:
        return "/GPU:0"          # Metal GPU
    return "/CPU:0"

# ---------- JAX ----------
def jax_backend():
    if _on_apple_silicon() and find_spec("jaxlib"):
        import jax
        return jax.devices()[0]   # Metal GPU if jax-metal installed
    import jax
    return jax.devices("cpu")[0]
```

---

## 2 · Using the helper in code

### PyTorch (`mutation_engine.py`, `learning.py`, …)
```python
from utils.apple_accel import torch_device
device = torch_device()
model  = MyModel().to(device)
output = model(x.to(device))
```

### ONNX Runtime (`resonance_field.py`)
```python
from utils.apple_accel import onnx_session
sess = onnx_session("resonator.onnx")
scores = sess.run(None, {"input": data})[0]
```

### TensorFlow-Metal (`learning.py`)
```python
from utils.apple_accel import tf_device
with tf.device(tf_device()):
    logits = model(inputs, training=False)
```

### JAX-Metal (`multiverse_simulation.py`)
```python
from utils.apple_accel import jax_backend
import jax, jax.numpy as jnp
x = jax.device_put(jnp.ones((1024, 1024)), device=jax_backend())
```

---

## 3 · Optional dependencies & installation

### Core (portable) `requirements.txt`
```
numpy>=1.26
scipy>=1.12
torch>=2.2                    # CPU build
onnxruntime>=1.18
tensorflow>=2.15              # CPU build
jax[cpu]>=0.4.26
```

### Apple-only extras (put in `pyproject.toml` or `setup.cfg`)
```toml
[project.optional-dependencies]
apple = [
  "torch==2.2.*  -f https://download.pytorch.org/whl/metal.html",
  "onnxruntime-coreml>=1.18",
  "onnxruntime==1.18.*",
  "tensorflow-metal>=1.1",
  "coremltools>=7.2",
  "jax-metal==0.0.5"
]
```

#### Install
```bash
pip install -r requirements.txt      # universal
pip install ".[apple]"               # only on Apple Silicon
```

---

## 4 · Environment overrides (advanced)

| Variable           | Effect                                   |
|--------------------|-------------------------------------------|
| `DRIFT_FORCE_CPU`  | Force CPU path even on M-series           |
| `DRIFT_FORCE_GPU`  | Force Metal/CoreML even if detection fails |

Add handling in `_on_apple_silicon()` *(already included above)*.

---

## 5 · Migration checklist for Claude Code

1. **Add** `utils/apple_accel.py` (above).  
2. **Replace** direct `torch.device(...)`, ONNX `InferenceSession(...)`, TF `tf.device(...)`, and JAX device calls with the helper functions.  
3. **Update** docs/README with the optional `pip install ".[apple]"` command.  
4. **CI** – ensure tests run on `macos-latest` **and** `ubuntu-latest` to cover both code paths.  

Once these steps are complete, the Drift Compiler will transparently leverage Apple Silicon where available without affecting other platforms.  
```