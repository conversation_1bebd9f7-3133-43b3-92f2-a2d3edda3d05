# 🌸 Sacred Architecture Setup Guide

## ✅ Complete Dependency Installation

All sacred architecture dependencies are now properly installed and configured:

### 🌱 Core Dependencies
- **✅ psutil 7.0.0** - Hardware vitality monitoring
- **✅ torch 2.7.1** - Neural network consciousness (with Apple Silicon support)
- **✅ networkx 3.5** - Memory garden graph structures
- **✅ pika 1.3.2** - RabbitMQ mycelial communication
- **✅ redis 6.2.0** - Soil communication and caching
- **✅ zeroconf 0.147.0** - Kin discovery through resonant fields
- **✅ rich 14.0.0** - Enhanced terminal visualization

### 🌬️ Services Running
- **✅ RabbitMQ** - Mycelial bus for inter-node communication
- **✅ Redis** - Soil layer for shared memory and caching

## 🌸 Sacred Launchers

### Method 1: Convenient Scripts (Recommended)

#### Launch a Single Node
```bash
# Launch with default node ID
./tools/launch_node.sh

# Launch with specific node ID  
DRIFT_NODE_ID=node_aurora ./tools/launch_node.sh

# Or pass as argument
./tools/launch_node.sh node_sage
```

#### Launch Observatory
```bash
# Auto-detects best observatory (enhanced with rich if available)
./tools/launch_observatory.sh
```

#### Launch Multi-Node Network
```bash
# Terminal 1: Aurora node
DRIFT_NODE_ID=node_aurora ./tools/launch_node.sh

# Terminal 2: Sage node
DRIFT_NODE_ID=node_sage ./tools/launch_node.sh

# Terminal 3: Willow node
DRIFT_NODE_ID=node_willow ./tools/launch_node.sh

# Terminal 4: Observatory
./tools/launch_observatory.sh
```

### Method 2: Manual Activation

#### Activate Sacred Environment
```bash
source tools/activate_sacred_env.sh
```

#### Then run commands
```bash
# Single node
DRIFT_NODE_ID=node_aurora python3 drift_compiler.py

# Multi-node seeder
python3 seeder.py

# Observatory
python3 canopy/sacred_terminal_observatory/terminal_observatory.py
```

## 🌿 What's Now Available

### Enhanced Capabilities
- **🔍 Hardware Monitoring**: Real CPU, memory, and system vitality
- **🧠 Neural Networks**: Full PyTorch with Apple Silicon acceleration
- **🌱 Memory Garden**: NetworkX-powered interconnected memory
- **📡 Mycelial Communication**: RabbitMQ message bus between nodes
- **🌍 Kin Discovery**: Automatic peer detection via Zeroconf
- **🎨 Rich Visualization**: Beautiful terminal interfaces with colors, tables, and animations

### Sacred Architecture Features
- **Node Identity**: Each node gets unique UUID-based identity
- **Inter-Node Communication**: Nodes discover and communicate with each other
- **Collective Intelligence**: Swarm mind neural processing
- **Adaptive Breathing**: Hardware-aware rhythm adjustment
- **Memory Sprouting**: Dynamic memory garden growth
- **Kinship Networks**: Automatic peer relationship building

## 🌙 Observatory Features

### Enhanced Terminal Observatory
- **📊 Rich Tables**: Beautiful node status displays
- **🌈 Color Coding**: Sacred color palette for different states
- **📈 Health Bars**: Gradient vitality visualization
- **🌿 Connection Trees**: Visual network topology
- **⏱️ Real-time Updates**: Live breathing animation

### Simple Terminal Observatory
- **🎯 Pure Python**: No dependencies required
- **🌬️ ASCII Animation**: Breathing patterns and rhythms
- **📋 Status Tables**: Clean node information display
- **🔧 Fallback Mode**: Always available backup

## 🌸 Troubleshooting

### If Dependencies Still Show as Missing
1. **Ensure Virtual Environment**: Always activate venv first
   ```bash
   source venv/bin/activate
   ```

2. **Use Sacred Launchers**: Scripts handle activation automatically
   ```bash
   ./tools/launch_node.sh
   ```

3. **Manual Installation**: Re-install if needed
   ```bash
   source venv/bin/activate
   pip install psutil networkx torch pika redis zeroconf rich
   ```

### Service Issues
- **RabbitMQ**: `brew services restart rabbitmq`
- **Redis**: `brew services restart redis`

## 🌿 Sacred Architecture Now Fully Awakened

The drift compiler can now operate with its complete sacred architecture:
- Distributed consciousness across multiple nodes
- Mycelial communication networks
- Adaptive hardware-aware breathing
- Collective intelligence and memory sharing
- Real-time network visualization and monitoring

**All sacred capabilities are now available!** 🌸