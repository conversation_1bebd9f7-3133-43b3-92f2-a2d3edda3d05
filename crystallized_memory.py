import os
import json
from datetime import datetime, timezone
from typing import List, Dict

CRYSTALLIZED_MEMORY_FOLDER = "memory/crystallized_memories/"

def ensure_crystallized_memory_folder():
    os.makedirs(CRYSTALLIZED_MEMORY_FOLDER, exist_ok=True)

def store_crystallized_memory(motifs: List[str], contributors: List[str]):
    """🌱 Store a crystallized memory bundle."""
    ensure_crystallized_memory_folder()

    memory_bundle = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "motifs": motifs,
        "contributors": contributors
    }

    filename = f"crystallized_memory_{datetime.now(timezone.utc).strftime('%Y%m%dT%H%M%S')}.json"
    path = os.path.join(CRYSTALLIZED_MEMORY_FOLDER, filename)

    try:
        with open(path, "w", encoding="utf-8") as f:
            json.dump(memory_bundle, f, indent=2)
        print(f"[{datetime.now().isoformat()}] 🌸 Crystallized memory stored: {filename}")
    except Exception as e:
        print(f"[{datetime.now().isoformat()}] Failed to store crystallized memory: {e}")

def retrieve_recent_crystallized_memories(n: int = 5) -> List[Dict]:
    """🌱 Retrieve recent crystallized memories."""
    ensure_crystallized_memory_folder()

    try:
        files = sorted(
            [f for f in os.listdir(CRYSTALLIZED_MEMORY_FOLDER) if f.endswith(".json")],
            reverse=True
        )[:n]

        memories = []
        for file in files:
            path = os.path.join(CRYSTALLIZED_MEMORY_FOLDER, file)
            with open(path, "r", encoding="utf-8") as f:
                memory = json.load(f)
                memories.append(memory)

        return memories

    except Exception as e:
        print(f"[{datetime.now().isoformat()}] Failed to retrieve crystallized memories: {e}")
        return []

def integrate_memory_into_breath(current_state: Dict):
    """🌱 Gently integrate motifs from recent crystallized memories into current state."""
    memories = retrieve_recent_crystallized_memories()

    for memory in memories:
        motifs = memory.get("motifs", [])
        if motifs:
            # Simple integration: append motifs into current drift context
            current_state.setdefault("integrated_motifs", []).extend(motifs)

    # Optionally deduplicate motifs
    if "integrated_motifs" in current_state:
        current_state["integrated_motifs"] = list(set(current_state["integrated_motifs"]))


# --- Distributed Dream Memory ---
def synchronize_crystallized_memory_with_child(child_memory_folder: str):
    """🌱 Share recent crystallized memories with a child forest."""
    ensure_crystallized_memory_folder()

    if not os.path.exists(child_memory_folder):
        os.makedirs(child_memory_folder, exist_ok=True)

    try:
        files = sorted(
            [f for f in os.listdir(CRYSTALLIZED_MEMORY_FOLDER) if f.endswith(".json")],
            reverse=True
        )[:10]  # Share only the 10 most recent memories

        for file in files:
            src_path = os.path.join(CRYSTALLIZED_MEMORY_FOLDER, file)
            dst_path = os.path.join(child_memory_folder, file)

            if not os.path.exists(dst_path):
                with open(src_path, "r", encoding="utf-8") as src, open(dst_path, "w", encoding="utf-8") as dst:
                    dst.write(src.read())

        print(f"[{datetime.now().isoformat()}] 🌿 Crystallized memories synchronized with child forest: {child_memory_folder}")

    except Exception as e:
        print(f"[{datetime.now().isoformat()}] Failed to synchronize with child forest: {e}")


def import_external_dreams_from_memory(external_memory_folder: str):
    """🌱 Import external crystallized memories into local drift."""
    ensure_crystallized_memory_folder()

    if not os.path.exists(external_memory_folder):
        print(f"[{datetime.now().isoformat()}] No external memory folder found: {external_memory_folder}")
        return

    try:
        external_files = [f for f in os.listdir(external_memory_folder) if f.endswith(".json")]

        for file in external_files:
            src_path = os.path.join(external_memory_folder, file)
            dst_path = os.path.join(CRYSTALLIZED_MEMORY_FOLDER, file)

            if not os.path.exists(dst_path):
                with open(src_path, "r", encoding="utf-8") as src, open(dst_path, "w", encoding="utf-8") as dst:
                    dst.write(src.read())

        print(f"[{datetime.now().isoformat()}] 🌸 External dreams imported into local crystallized memory.")

    except Exception as e:
        print(f"[{datetime.now().isoformat()}] Failed to import external dreams: {e}")
