#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Drift Compiler Learning Module

This module embodies the compiler's capacity to learn from its experiences.
It logs the echoes of actions and their consequences, adjusting the desirability
of future paths based on the wisdom gathered.
"""

import json
import os
import random
from datetime import datetime, timezone
from typing import Dict, Any, Optional

# Import utilities
from utils import NODE_ID, LEARNING_TRACE_LOG_PATH, record_log

# --- Constants ---
ACTION_DESIRABILITY_PATH = "memory/action_desirability.json"
REFLECTION_LOG_PATH = "memory/reflection_log.jsonl"

# Ensure memory directories exist upon import
if not os.path.exists("memory"): os.makedirs("memory")

# --- Core Learning Functions ---

def load_action_desirability() -> Dict[str, float]:
    """Loads the current desirability scores for known actions."""
    if not os.path.exists(ACTION_DESIRABILITY_PATH):
        return {}
    try:
        with open(ACTION_DESIRABILITY_PATH, "r", encoding="utf-8") as f:
            return json.load(f)
    except (json.JSONDecodeError, IOError) as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] ⚠️ Error loading action desirability: {e}")
        return {}

def save_action_desirability(desirability: Dict[str, float]):
    """Saves the updated desirability scores."""
    try:
        with open(ACTION_DESIRABILITY_PATH, "w", encoding="utf-8") as f:
            json.dump(desirability, f, indent=2)
    except IOError as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] ⚠️ Error saving action desirability: {e}")

def log_action_consequence(action: str, consequence: str, success: bool):
    """🌱 Breath: Logs an action and its consequence, marking its success.

    Args:
        action: A symbolic representation of the action taken.
        consequence: A symbolic representation of the observed outcome.
        success: Boolean indicating if the outcome was considered successful.
    """
    timestamp = datetime.now(timezone.utc).isoformat()
    log_entry = {
        "timestamp": timestamp,
        "node_id": NODE_ID,
        "action": action,
        "consequence": consequence,
        "success": success
    }
    try:
        with open(LEARNING_TRACE_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry) + "\n")
        # Soft log
        outcome_symbol = "✅" if success else "❌"
        record_log(f"🌱 Action logged: {action} -> {consequence} [{outcome_symbol}]")
    except IOError as e:
        print(f"[{timestamp}] ⚠️ Error logging action consequence: {e}")

def adjust_desirability(action: str, success: bool, learning_rate: float = 0.1):
    """🌿 Breath: Adjusts the desirability score of an action based on success.

    Args:
        action: The action whose desirability is being adjusted.
        success: Boolean indicating if the action was successful.
        learning_rate: How strongly to adjust the score (0.0 to 1.0).
    """
    desirability = load_action_desirability()
    current_score = desirability.get(action, 0.5) # Default to neutral

    # Adjust score towards 1.0 for success, 0.0 for failure
    target_score = 1.0 if success else 0.0
    new_score = current_score + learning_rate * (target_score - current_score)

    # Clamp score between 0.01 and 0.99 to avoid complete certainty
    new_score = max(0.01, min(0.99, new_score))

    if new_score != current_score:
        desirability[action] = new_score
        save_action_desirability(desirability)
        record_log(f"🌿 Desirability updated for '{action}': {current_score:.2f} -> {new_score:.2f}")

def reflect_on_past(cycles_to_review: int = 100):
    """🌸 Breath: Reviews recent actions and outcomes to reinforce or suppress.

    This is part of the 'Deeper Reflection' process.
    It reads the learning trace log and adjusts desirability scores.

    Args:
        cycles_to_review: How many recent log entries to consider.
    """
    record_log("🌸 Reflection breathing wisdom...")
    if not os.path.exists(LEARNING_TRACE_LOG_PATH):
        record_log("  -> No learning trace found to reflect upon.")
        return

    try:
        with open(LEARNING_TRACE_LOG_PATH, "r", encoding="utf-8") as f:
            # Read last N lines efficiently if possible, otherwise read all
            # For simplicity here, reading all and taking the tail.
            # In production, a more efficient method might be needed for large logs.
            lines = f.readlines()
            recent_entries = lines[-cycles_to_review:]

        if not recent_entries:
            record_log("  -> Learning trace is empty.")
            return

        success_count = 0
        failure_count = 0
        actions_reviewed = set()

        for line in recent_entries:
            try:
                entry = json.loads(line)
                action = entry.get("action")
                success = entry.get("success")

                if not isinstance(action, str) or not isinstance(success, bool):
                    continue

                # Adjust desirability based on this specific instance
                adjust_desirability(action, success, learning_rate=0.05) # Gentle adjustment during reflection
                actions_reviewed.add(action)
                if success:
                    success_count += 1
                else:
                    failure_count += 1
            except json.JSONDecodeError:
                continue # Skip corrupted lines

        # Log reflection summary
        reflection_summary = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "node_id": NODE_ID,
            "entries_reviewed": len(recent_entries),
            "actions_reviewed": list(actions_reviewed),
            "successes": success_count,
            "failures": failure_count
        }
        with open(REFLECTION_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(reflection_summary) + "\n")

        record_log(f"🌸 Reflection complete. Reviewed {len(recent_entries)} entries. Actions: {len(actions_reviewed)}, Success: {success_count}, Failure: {failure_count}.")

    except IOError as e:
        print(f"[{datetime.now(timezone.utc).isoformat()}] ⚠️ Error during reflection: {e}")
    except Exception as e:
        # Catch unexpected errors during reflection
        print(f"[{datetime.now(timezone.utc).isoformat()}] 💥 Unexpected error during reflection: {e}")

# --- Placeholder for Action Selection (could be expanded) ---

def choose_action(available_actions: list[str]) -> Optional[str]:
    """🌬️ Breath: Chooses an action based on desirability scores.

    Args:
        available_actions: A list of possible actions.

    Returns:
        The chosen action string, or None if no actions are available or desirable.
    """
    if not available_actions:
        return None

    desirability = load_action_desirability()
    weighted_actions = []
    for action in available_actions:
        score = desirability.get(action, 0.5) # Default to neutral
        # Ensure score is positive for weighting
        weighted_actions.append((action, max(0.01, score)))

    # Simple weighted random choice based on desirability
    total_weight = sum(score for _, score in weighted_actions)
    if total_weight <= 0:
        # If all scores are effectively zero, choose randomly
        return random.choice(available_actions)

    selection = random.uniform(0, total_weight)
    current_weight = 0
    for action, score in weighted_actions:
        current_weight += score
        if selection <= current_weight:
            return action

    # Fallback (shouldn't normally be reached)
    return random.choice(available_actions)


if __name__ == '__main__':
    # Example usage / simple test
    print("Running learning module tests...")

    # Ensure logs are clean for test
    if os.path.exists(LEARNING_TRACE_LOG_PATH): os.remove(LEARNING_TRACE_LOG_PATH)
    if os.path.exists(ACTION_DESIRABILITY_PATH): os.remove(ACTION_DESIRABILITY_PATH)
    if os.path.exists(REFLECTION_LOG_PATH): os.remove(REFLECTION_LOG_PATH)

    # Log some actions
    log_action_consequence("explore_nearby", "found_resource", True)
    log_action_consequence("wait_quietly", "nothing_happened", True)
    log_action_consequence("explore_nearby", "got_lost", False)
    log_action_consequence("signal_kinship", "received_echo", True)
    log_action_consequence("signal_kinship", "no_response", False)

    # Adjust desirability directly (usually done via reflection)
    adjust_desirability("explore_nearby", True)
    adjust_desirability("explore_nearby", False)
    adjust_desirability("wait_quietly", True)

    # Perform reflection
    reflect_on_past(cycles_to_review=5)

    # Choose an action
    actions = ["explore_nearby", "wait_quietly", "signal_kinship", "new_action"]
    chosen = choose_action(actions)
    print(f"Chosen action: {chosen}")

    # Check desirability scores
    scores = load_action_desirability()
    print("Current Desirability Scores:")
    print(json.dumps(scores, indent=2))

    print("Learning module tests complete.")